package com.leliven.ddd.core.support;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LazyLoaderTest {

	@InjectMocks
	private LazyLoader<String> lazyLoader;

	@Mock
	private Supplier<String> supplierMock;

	@BeforeEach
	void setUp() {
		lazyLoader = new LazyLoader<>(supplierMock);
	}

	@Test
	void testLazyLoader_CreationWithNullSupplier_ShouldThrowException() {
		assertThrows(NullPointerException.class, () -> {
			new LazyLoader<>(null);
		});
	}

	@Test
	void testLazyLoader_StaticFactoryMethod_ShouldReturnInstance() {
		Supplier<String> anotherSupplierMock = () -> "anotherTest";
		LazyLoader<String> anotherLazyLoader = LazyLoader.of(anotherSupplierMock);

		assertEquals("anotherTest", anotherLazyLoader.get());
	}

	@Test
	void testLazyLoader_Get_ShouldReturnSuppliedValue() {
		when(supplierMock.get()).thenReturn("test");
		assertEquals("test", lazyLoader.get());
	}

	@Test
	void testLazyLoader_Get_ShouldThrowExceptionWhenSupplierThrows() {
		RuntimeException exception = new RuntimeException();
		Supplier<String> exceptionThrowingSupplier = () -> {
			throw exception;
		};
		LazyLoader<String> exceptionLazyLoader = new LazyLoader<>(exceptionThrowingSupplier);

		assertThrows(RuntimeException.class, exceptionLazyLoader::get);
	}

	@Test
	void testLazyLoader_Get_ShouldHandleNullFromSupplier() {
		Supplier<String> nullSupplier = () -> null;
		LazyLoader<String> nullLazyLoader = new LazyLoader<>(nullSupplier);

		assertFalse(nullLazyLoader.isLoaded());
		assertNull(nullLazyLoader.get());
		assertTrue(nullLazyLoader.isLoaded());
	}

	@Test
	void testLazyLoader_Get_ShouldLoadOnceAndCacheResult() {
		when(supplierMock.get()).thenReturn("test");
		// Verify that the supplier is called only once
		lazyLoader.get();
		lazyLoader.get();

		verify(supplierMock, times(1)).get();
	}

	@Test
	void testLazyLoader_Get_ShouldReturnSameInstanceOnMultipleCalls() {
		when(supplierMock.get()).thenReturn("test");
		String result1 = lazyLoader.get();
		String result2 = lazyLoader.get();

		assertEquals(result1, result2);
		assertSame(result1, result2);
	}

	@Test
	void testLazyLoader_ConcurrentLoading_ShouldLoadOnceAndCacheResult() throws Exception {
		when(supplierMock.get()).thenReturn("test");
		ExecutorService executor = Executors.newFixedThreadPool(10);
		Future<?>[] futures = new Future<?>[10];

		for (int i = 0; i < 10; i++) {
			futures[i] = executor.submit(() -> lazyLoader.get());
		}

		for (Future<?> future : futures) {
			future.get();
		}

		verify(supplierMock, times(1)).get();
		executor.shutdown();
	}

	@Test
	void testLazyLoader_WithDifferentTypesOfSuppliers_ShouldWorkCorrectly() {
		Supplier<Integer> integerSupplier = () -> 42;
		LazyLoader<Integer> integerLazyLoader = new LazyLoader<>(integerSupplier);
		assertEquals(Integer.valueOf(42), integerLazyLoader.get());

		Supplier<Double> doubleSupplier = () -> 3.14;
		LazyLoader<Double> doubleLazyLoader = new LazyLoader<>(doubleSupplier);
		assertEquals(Double.valueOf(3.14), doubleLazyLoader.get());
	}

}
