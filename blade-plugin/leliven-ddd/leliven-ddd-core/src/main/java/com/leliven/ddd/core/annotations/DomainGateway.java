package com.leliven.ddd.core.annotations;

import java.lang.annotation.*;

/**
 * 领域网关接口标记注解
 *
 * <p>领域网关是一个复合角色，同时承担防腐层(Anti-Corruption Layer)
 * 和仓储(Repository)的职责。通过依赖反转原则(DIP)，领域层定义网关接口，基础设施层实现该接口，
 * 从而使领域层不直接依赖外部系统和基础设施。</p>
 *
 * <p><b>依赖反转的应用：</b></p>
 * <ul>
 *   <li>领域层定义接口，控制权在领域层</li>
 *   <li>基础设施层实现接口，依赖指向领域层</li>
 *   <li>运行时通过依赖注入绑定实现</li>
 * </ul>
 *
 * <p><b>双重职责：</b></p>
 * <ul>
 *   <li><b>防腐层职责：</b></li>
 *   <ul>
 *     <li>封装外部系统调用细节</li>
 *     <li>处理数据格式转换</li>
 *     <li>处理远程调用异常</li>
 *   </ul>
 *   <li><b>仓储职责：</b></li>
 *   <ul>
 *     <li>领域对象的持久化</li>
 *     <li>领域对象的查询</li>
 *     <li>维护领域对象的生命周期</li>
 *   </ul>
 * </ul>
 *
 * <p><b>使用示例1 - 仓储功能：</b></p>
 * <pre>
 * // 领域层定义接口
 * {@code @DomainGateway}
 * public interface OrderGateway {
 *     void save(Order order);
 *     Order findById(OrderId orderId);
 * }
 *
 * // 基础设施层实现接口
 * {@code @DomainGatewayImpl}
 * {@code @RequiredArgsConstructor}
 * public class OrderGatewayImpl implements OrderGateway {
 *     private final OrderMapper orderMapper;
 *
 *     public void save(Order order) {
 *         OrderDO orderDO = OrderConverter.toOrderDO(order);
 *         orderMapper.insert(orderDO);
 *     }
 * }
 * </pre>
 *
 * <p><b>使用示例2 - 外部系统交互：</b></p>
 * <pre>
 * // 领域层定义接口
 * {@code @DomainGateway}
 * public interface PaymentGateway {
 *     PaymentResult pay(Payment payment);
 * }
 *
 * // 基础设施层实现接口
 * {@code @DomainGatewayImpl}
 * {@code @RequiredArgsConstructor}
 * public class PaymentGatewayImpl implements PaymentGateway {
 * 	   // 支付服务 feign client
 *     private final PaymentClient paymentClient;
 *
 *     public PaymentResult pay(PaymentCommand command) {
 *         PaymentRequest request = buildRequest(command);
 *         return processPaymentResponse(paymentClient.execute(request));
 *     }
 * }
 *
 * </pre>
 *
 * <p><b>最佳实践：</b></p>
 * <ul>
 *   <li>接口设计要符合领域模型的概念</li>
 *   <li>在实现类中处理技术细节</li>
 *   <li>通过依赖注入框架管理实现</li>
 *   <li>保持接口稳定性，实现可以随时替换</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface DomainGateway {
}
