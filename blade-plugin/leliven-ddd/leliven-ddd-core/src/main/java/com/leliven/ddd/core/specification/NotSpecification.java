package com.leliven.ddd.core.specification;

/**
 * 领域规约非接口
 *
 * <AUTHOR>
 * @param <T>
 */
public class NotSpecification<T> extends CompositeSpecification<T> {

	private final Specification<T> wrapped;

    public NotSpecification(Specification<T> wrapped) {
        this.wrapped = wrapped;
    }

    @Override
	public boolean isSatisfiedBy(T candidate) {
        return !wrapped.isSatisfiedBy(candidate);
    }
}
