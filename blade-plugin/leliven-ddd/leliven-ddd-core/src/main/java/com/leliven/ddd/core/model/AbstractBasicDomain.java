package com.leliven.ddd.core.model;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 基础领域实体抽象类
 *
 * <p>只包含ID字段和基础方法，作为所有简单领域实体的基类</p>
 *
 * <p>提供的功能：</p>
 * <ul>
 *   <li>实体ID管理</li>
 *   <li>基础实体验证和比较</li>
 *   <li>实体状态检查</li>
 * </ul>
 *
 * @param <ID> 实体ID类型
 * <AUTHOR>
 * @since 1.0.0
 */
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class AbstractBasicDomain<ID> implements Domain<ID> {

    private static final long serialVersionUID = 1L;

    /**
     * 实体主键
     */
	protected ID id;


    /**
     * 带ID的构造函数
     *
     * @param id 实体主键
     */
    protected AbstractBasicDomain(ID id) {
        this.id = id;
    }

    @Override
    public ID getId() {
        return id;
    }

	@Override
	public void setId(ID id) {
		this.id = id;
	}

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        @SuppressWarnings("unchecked")
		AbstractBasicDomain<ID> that = (AbstractBasicDomain<ID>) obj;

        // 如果两个实体都没有ID，则认为不相等
        if (id == null || that.id == null) {
            return false;
        }

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{id=" + id + "}";
    }
}
