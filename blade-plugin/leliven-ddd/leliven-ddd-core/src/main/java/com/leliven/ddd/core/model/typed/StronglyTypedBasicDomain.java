package com.leliven.ddd.core.model.typed;

import com.leliven.ddd.core.model.AbstractBasicDomain;
import com.leliven.ddd.core.model.Domain;

/**
 * 强类型基础领域实体抽象类
 *
 * <p>只包含强类型ID字段，适用于需要强类型ID的简单领域实体</p>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>需要强类型ID的简单值对象或枚举实体</li>
 *   <li>不需要审计功能但需要类型安全的实体</li>
 *   <li>临时或测试用的强类型实体</li>
 * </ul>
 *
 * <p>字段说明：</p>
 * <ul>
 *   <li>id: 实体主键，强类型ID（继承自DomainID）</li>
 * </ul>
 *
 * @param <ID> 强类型ID类型，必须继承自DomainID
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class StronglyTypedBasicDomain<ID extends DomainID<V>, V>
	extends AbstractBasicDomain<ID> implements Domain<ID> {

    private static final long serialVersionUID = 1L;

    /**
     * 默认构造函数
     */
    protected StronglyTypedBasicDomain() {
        super();
    }

    /**
     * 带ID的构造函数
     *
     * @param id 实体主键
     */
	protected StronglyTypedBasicDomain(ID id) {
        super(id);
    }

    public V getIdValue() {
        return id != null ? id.getValue() : null;
    }

    /**
     * 判断是否为同一实体
     *
     * @param other 其他实体
     * @return true如果是同一实体，false否则
     */
    public boolean sameIdentityAs(StronglyTypedBasicDomain<ID, V> other) {
        return other != null && java.util.Objects.equals(getId(), other.getId());
    }
}
