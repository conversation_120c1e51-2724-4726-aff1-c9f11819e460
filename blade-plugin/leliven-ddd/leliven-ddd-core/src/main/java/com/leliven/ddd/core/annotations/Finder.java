package com.leliven.ddd.core.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查找器注解
 *
 * <p>用于标记DDD架构中的查询器（Query）组件，主要用于CQRS模式下的读模型查询操作。
 * 在CQRS（命令查询职责分离）模式中，查询操作和命令操作被分开处理，以提高系统的性能和可扩展性。</p>
 *
 * <p><b>主要用途：</b></p>
 * <ul>
 *   <li>标记查询器接口（Query Interface）</li>
 *   <li>标识只读操作组件</li>
 *   <li>区分查询操作和命令操作</li>
 * </ul>
 *
 * <p><b>使用示例：</b></p>
 * <pre>
 *
 * // 订单查询接口
 * {@code @Finder}
 * public interface OrderFinder {
 *     // 查询单个订单详情
 *     OrderDTO findById(Long orderId);
 *
 *     // 分页查询订单列表
 *     Page<OrderDTO> findByCondition(OrderQuery query);
 * }
 * </pre>
 *
 * <p><b>最佳实践：</b></p>
 * <ul>
 *   <li>查询方法应返回DTO对象，避免返回领域对象</li>
 *   <li>查询方法不应该修改任何领域状态</li>
 *   <li>可以直接访问数据库，不需要经过领域层</li>
 *   <li>推荐使用专用的查询数据库或视图优化查询性能</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.TYPE})
public @interface Finder {
}
