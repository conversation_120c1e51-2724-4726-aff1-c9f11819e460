package com.leliven.ddd.core.specification;

/**
 * 领域规约与接口
 *
 * <AUTHOR>
 * @param <T> 规约对象类型
 */
public class AndSpecification<T> extends CompositeSpecification<T>{

    private final Specification<T> a;
    private final Specification<T> b;

    public AndSpecification(Specification<T> a, Specification<T> b){
        this.a = a;
        this.b = b;
    }

	/**
	 * 是否满足规约
	 *
	 * @param candidate 候选对象
	 * @return {@code true} 满足规约 {@code false} 不满足规约
	 */
    @Override
	public boolean isSatisfiedBy(T candidate){
		return a.isSatisfiedBy(candidate) && b.isSatisfiedBy(candidate);
    }
}
