package com.leliven.ddd.core.event;

import lombok.Getter;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.ApplicationEvent;

import javax.annotation.Nonnull;
import java.util.Date;

/**
 * 领域事件抽象类
 *
 * <AUTHOR>
 */
@Getter
public abstract class AbstractDomainEvent<T, D extends DomainEventType>
	extends ApplicationEvent implements PayloadDomainEvent<T> {

	private static final long serialVersionUID = 2203138148073407385L;

	/**
	 * 事件ID
	 */
	private final Long id;
	/**
	 * 事件发生时间
	 */
	private final Date datetime;
	/**
	 * 事件类型
	 */
	private final D eventType;
	/**
	 * 事件载体
	 */
	@Nonnull
	private final T payload;

	/**
	 * Create a new DomainEvent.
	 *
	 * @param source  the object on which the event initially occurred (never {@code null})
	 * @param payload the payload object (never {@code null})
	 * @param eventType domain event type
	 */
	protected AbstractDomainEvent(Object source, D eventType, @Nonnull T payload) {
		super(source);
		this.payload = payload;
		this.eventType = eventType;
		this.id = Func.toLong(SequenceNoUtils.generateNo());
		this.datetime = new Date();
	}

	@Override
	public String toString() {
		return "DomainEvent{" +
			"id=" + id +
			", datetime=" + datetime +
			", eventType=" + eventType +
			", payload=" + getPayload() +
			'}';
	}
}
