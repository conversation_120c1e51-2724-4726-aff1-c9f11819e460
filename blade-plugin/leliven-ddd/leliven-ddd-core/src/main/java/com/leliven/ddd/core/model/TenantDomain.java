package com.leliven.ddd.core.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 携带租户的领域模型类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TenantDomain extends BaseDomain {

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 8010918711582518674L;

	/**
	 * 租户ID
	 */
	protected String tenantId;

	/**
	 * 构造函数
	 *
	 * @param id 主键id
	 */
	public TenantDomain(Long id) {
		super(id);
	}
}
