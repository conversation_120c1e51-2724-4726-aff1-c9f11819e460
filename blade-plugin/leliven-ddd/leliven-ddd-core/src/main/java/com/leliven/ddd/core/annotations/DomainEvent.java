package com.leliven.ddd.core.annotations;

import java.lang.annotation.*;

/**
 * 领域事件注解
 *
 * <p>在领域驱动设计(DDD)中，领域事件用于捕获和传播领域中发生的重要变更。领域事件可以分为两类：</p>
 *
 * <p><b>事件类型：</b></p>
 * <ul>
 *   <li><b>领域事件：</b>在同一个限界上下文内（可以简单理解为同服务内）传播的事件，用于领域内部的状态同步和触发后续业务流程</li>
 *   <li><b>领域集成事件：</b>跨限界上下文（可以简单理解为跨服务）传播的事件，用于不同上下文之间的异步通信和状态同步</li>
 * </ul>
 *
 * <p><b>使用示例1 - 领域事件：</b></p>
 * <pre>
 * // 订单创建事件，在同一上下文内触发库存检查、通知等操作
 * {@code @DomainEvent}
 * public class OrderCreatedEvent implements DomainEvent {
 *     private final OrderId orderId;
 *     private final OrderStatus status;
 *     private final LocalDateTime createTime;
 *
 * }
 * </pre>
 *
 * <p><b>使用示例2 - 领域集成事件：</b></p>
 * <pre>
 * // 订单支付事件，需要通知库存系统、物流系统等其他上下文
 * {@code @DomainEvent}
 * public class OrderPaidEvent implements DomainEvent {
 *     private final OrderId orderId;
 *     private final PaymentId paymentId;
 *     private final Money amount;
 *
 * }
 * </pre>
 *
 * <p><b>最佳实践：</b></p>
 * <ul>
 *   <li>事件应该包含足够的上下文信息</li>
 *   <li>事件对象应该是不可变的</li>
 *   <li>使用事件发布器异步发布事件</li>
 *   <li>考虑事件的持久化和重放机制</li>
 * </ul>
 *
 * <AUTHOR>
 * @see com.leliven.ddd.core.event.DomainEvent
 * @see com.leliven.ddd.core.event.DomainEventPublisher
 * @see com.leliven.ddd.core.event.SpringDomainEventPublisher
 * @since 1.0.0
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.TYPE})
public @interface DomainEvent {

}
