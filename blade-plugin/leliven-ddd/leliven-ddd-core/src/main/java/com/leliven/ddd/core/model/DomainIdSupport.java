package com.leliven.ddd.core.model;

import java.util.Objects;

/**
 * 领域ID类型支持
 *
 * @param <ID> 领域ID类型
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DomainIdSupport<ID> {

	/**
	 * 获取ID
	 *
	 * @return ID
	 */
	ID getId();

	/**
	 * 设置领域对象标识
	 *
	 * @param id 领域对象标识 {@link ID}
	 */
	void setId(ID id);

	/**
	 * 根据传入的ID判断是否相同
	 *
	 * @param otherId 其他ID
	 * @return 结果是否相同  {@code true} 相同 {@code false} 不相同
	 */
	default boolean sameIdentityAs(ID otherId) {
		return otherId != null && Objects.equals(getId(), otherId);
	}
}
