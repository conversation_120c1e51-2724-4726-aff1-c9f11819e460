package com.leliven.ddd.core.model.typed;

import com.leliven.ddd.core.model.Identifier;
import lombok.Getter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 领域对象ID基类
 *
 * @param <V> 值类型
 * <AUTHOR>
 */
@Getter
public abstract class DomainID<V> implements Identifier<V>, Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 值
	 */
	protected V value;

	/**
	 * 默认构造函数
	 */
	protected DomainID() {
	}

	/**
	 * 构造函数
	 *
	 * @param value 值
	 */
	protected DomainID(V value) {
		this.value = value;
	}

	/**
	 * 设置主键值
	 *
	 * @param value 值
	 */
	protected void setValue(V value) {
		this.value = value;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}

		DomainID<?> domainId = (DomainID<?>) o;

		return Objects.equals(value, domainId.value);
	}

	@Override
	public int hashCode() {
		return Objects.hash(value);
	}

	@Override
	public String toString() {
		return getClass().getSimpleName() + "{" +
			"id=" + value +
			'}';
	}
}
