package com.leliven.ddd.core.converter;

import org.springblade.core.tool.utils.Func;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 领域对象转换器接口
 * T: 领域对象
 * D: 数据对象
 *
 * <AUTHOR>
 */
public interface DomainEntityConverter<T, D> {

    /**
     * 数据对象转换为领域对象
     *
     * @param dataObject 数据对象
     * @return 领域对象
     */
    T fromDO(D dataObject);

    /**
     * 领域对象转换为数据对象
     *
     * @param domainObject 领域对象
     * @return 数据对象
     */
    D toDO(T domainObject);

    /**
     * 数据对象列表转换为领域对象列表
     *
     * @param dataObjects 数据对象列表
     * @return 领域对象列表
     */
    default List<T> fromDOList(List<? extends D> dataObjects) {
        if (Func.isEmpty(dataObjects)) {
            return new ArrayList<>();
        }

        return dataObjects.stream().map(this::fromDO).collect(Collectors.toList());
    }

    /**
     * 领域对象列表转换为数据对象列表
     *
     * @param domainObjects 领域对象列表
     * @return 数据对象列表
     */
    default List<D> toDOList(List<? extends T> domainObjects) {
        if (Func.isEmpty(domainObjects)) {
            return new ArrayList<>();
        }

        return domainObjects.stream().map(this::toDO).collect(Collectors.toList());
    }
}
