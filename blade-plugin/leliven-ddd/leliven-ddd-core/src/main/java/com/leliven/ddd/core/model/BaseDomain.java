package com.leliven.ddd.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 基础领域模型类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public abstract class BaseDomain implements Domain<Long>, Serializable {

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = -6880224035899364715L;

	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	protected Long id;

	/**
	 * 创建人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	protected Long createUser;

	/**
	 * 创建部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	protected Long createDept;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	protected Date createTime;

	/**
	 * 更新人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	protected Long updateUser;

	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	protected Date updateTime;

	/**
	 * 状态[1:正常]
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	protected Integer status;

	/**
	 * 状态[0:未删除,1:删除]
	 */
	protected Integer isDeleted;

	/**
	 * 构造函数
	 *
	 * @param id 主键id
	 */
	protected BaseDomain(Long id) {
		this.id = id;
		BladeUser user = AuthUtil.getUser();
		Date now = DateUtil.now();
		if (user != null) {
			this.createUser = user.getUserId();
			this.createDept = Func.firstLong(user.getDeptId());
			this.updateUser = user.getUserId();
		}

		if (Objects.isNull(this.status)) {
			this.status = 1;
		}

		this.createTime = now;
		this.updateTime = now;
		this.isDeleted = 0;
	}

}
