package com.leliven.ddd.core.valueobject;


import com.leliven.ddd.core.annotations.ValueObject;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;

import java.util.Objects;

/**
 * 租户ID
 *
 * <p>
 * 强类型值对象，用于标识租户的唯一标识符。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@ValueObject
@EqualsAndHashCode
public class TenantID {

	/**
	 * 租户ID值
	 */
	private final String value;

	/**
	 * 私有构造
	 *
	 * @param value 租户ID值 {@link String}
	 */
	private TenantID(String value) {
		this.value = value;
	}

	/**
	 * 创建租户ID（不检查参数）
	 *
	 * @param idValue 租户ID值 {@link String}
	 * @return 租户ID {@link TenantID}
	 */
	public static TenantID of(String idValue) {
		return of(idValue, false);
	}

	/**
	 * 创建租户ID（带参数检查）
	 *
	 * <p>
	 * 此方法会对输入参数进行验证，确保租户ID的有效性。
	 * </p>
	 *
	 * @param idValue 租户ID值 {@link String}
	 * @return 租户ID {@link TenantID}
	 * @throws ServiceException 当租户ID为空、null时抛出
	 */
	public static TenantID ofChecked(String idValue) {
		return of(idValue, true);
	}

	/**
	 * 创建租户ID（带参数检查）
	 *
	 * @param idValue 租户ID值 {@link String}
	 * @param isChecked 是否检查参数
	 * @return 租户ID {@link TenantID}
	 */
	public static TenantID of(String idValue, boolean isChecked) {
		if (isChecked) {
			// 检查参数是否为空
			ObjectValidator.requireNotBlank(idValue, "租户ID不能为空");
		}
		return new TenantID(idValue);
	}

	/**
	 * 检查租户ID值是否有效（非空）
	 *
	 * @return 是否有效 {@code true} 有效 {@code false} 无效
	 */
	public boolean isValid() {
		return Func.isNotBlank(this.value);
	}

	/**
	 * 比较传入的租户ID值是否与当前租户ID值相同
	 *
	 * @param idValue 租户ID值 {@link String}
	 * @return 是否相等 {@code true} 相等 {@code false} 不相等
	 */
	public boolean equalsValue(String idValue) {
		return Objects.equals(this.value, idValue);
	}

}
