package com.leliven.ddd.core.specification;

import java.util.Arrays;
import java.util.List;

/**
 * 领域规约合并接口
 *
 * <AUTHOR>
 * @param <T> 规约对象类型
 */
public class ConjunctionSpecification<T> extends CompositeSpecification<T> {

	private final List<Specification<T>> specifications;

	@SafeVarargs
    public ConjunctionSpecification(Specification<T>... others) {
		this(Arrays.asList(others));
	}

	public ConjunctionSpecification(List<Specification<T>> specifications) {
		this.specifications = specifications;
	}

	/**
	 * 是否同时满足多个规约
	 *
	 * @param candidate 候选对象
	 * @return {@code true} 满足规约 {@code false} 不满足规约
	 */
	@Override
	public boolean isSatisfiedBy(T candidate) {
		return specifications.stream().allMatch(spec -> spec.isSatisfiedBy(candidate));
	}

	/**
	 * 添加规约
	 *
	 * @param specification 规约
	 */
	protected void addSpecification(Specification<T> specification) {
		this.specifications.add(specification);
	}

}
