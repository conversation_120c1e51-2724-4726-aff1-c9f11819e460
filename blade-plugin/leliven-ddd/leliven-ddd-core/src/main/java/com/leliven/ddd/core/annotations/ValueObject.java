package com.leliven.ddd.core.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 值对象标记注解
 *
 * <p>在领域驱动设计(DDD)中，值对象是一种特殊的对象类型，用于表示那些没有唯一标识、
 * 通过其属性值来判断相等性的对象。值对象应该是不可变的，这意味着创建后其状态不应被改变。</p>
 *
 * <p><b>值对象的特征：</b></p>
 * <ul>
 *     <li>不可变性：创建后状态不能改变</li>
 *     <li>相等性：通过属性值而不是身份来判断相等</li>
 *     <li>无副作用：方法调用不应该改变对象的状态</li>
 * </ul>
 *
 * <p><b>使用示例：</b></p>
 * <pre>
 * // 值对象定义
 * {@code @ValueObject}
 * public class Money {
 *     private final BigDecimal amount;
 *     private final Currency currency;
 *
 *     public Money(BigDecimal amount, Currency currency) {
 *         this.amount = amount;
 *         this.currency = currency;
 *     }
 *
 *     // 值对象的操作返回新实例
 *     public Money add(Money other) {
 *         return new Money(this.amount.add(other.amount), this.currency);
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.TYPE})
public @interface ValueObject {
}
