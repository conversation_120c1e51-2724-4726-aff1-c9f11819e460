package com.leliven.ddd.core.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.Objects;

import org.springblade.core.log.exception.ServiceException;

/**
 * 日期范围值对象
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class DateRange {
    /**
     * 开始日期
     */
    private final LocalDate startDate;

    /**
     * 结束日期
     */
    private final LocalDate endDate;

    /**
     * 创建日期范围
     */
    public static DateRange of(LocalDate startDate, LocalDate endDate) {
        Objects.requireNonNull(startDate, "开始日期不能为空");
        Objects.requireNonNull(endDate, "结束日期不能为空");
        if (endDate.isBefore(startDate)) {
            throw new ServiceException("结束日期不能早于开始日期");
        }
        return new DateRange(startDate, endDate);
    }

    /**
     * 判断指定日期是否在范围内
     */
    public boolean contains(LocalDate date) {
        Objects.requireNonNull(date, "待判断日期不能为空");
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * 判断是否有交集
     */
    public boolean hasOverlap(DateRange other) {
        Objects.requireNonNull(other, "待比较日期范围不能为空");
        return !this.endDate.isBefore(other.startDate)
            && !other.endDate.isBefore(this.startDate);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DateRange dateRange = (DateRange) o;
        return startDate.equals(dateRange.startDate)
            && endDate.equals(dateRange.endDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startDate, endDate);
    }
}
