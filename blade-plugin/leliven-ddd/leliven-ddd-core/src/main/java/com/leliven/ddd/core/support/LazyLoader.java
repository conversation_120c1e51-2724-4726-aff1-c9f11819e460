package com.leliven.ddd.core.support;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * 延迟加载器
 *
 * <p>
 * 用于延迟加载对象，只有在真正需要时才会加载对象
 * </p>
 *
 * <AUTHOR>
 */
public class LazyLoader<T> implements Supplier<T> {

	private volatile boolean initialized = false;
	private T instance;
	private final Supplier<T> supplier;

	public LazyLoader(Supplier<T> supplier) {
		Objects.requireNonNull(supplier);
		this.supplier = supplier;
	}

	public static <T> LazyLoader<T> of(Supplier<T> supplier) {
		return new LazyLoader<>(supplier);
	}

	/**
	 * 获取延迟加载的对象
	 *
	 * @return T 对象
	 */
	@Override
	public T get() {
		if (!initialized) {
			synchronized (this) {
				if (!initialized) {
					instance = supplier.get();
					initialized = true;
				}
			}
		}
		return instance;
	}

	/**
	 * 是否已加载
	 *
	 * @return {@code true} 已加载 {@code false} 未加载
	 */
	public boolean isLoaded() {
		return initialized;
	}

}
