package com.leliven.ddd.core.specification;


import java.util.List;

/**
 * 领域规约析取接口
 *
 * <AUTHOR>
 * @param <T> 规约对象类型
 */
public class DisjunctionSpecification<T> extends CompositeSpecification<T>{

	private final List<Specification<T>> specifications;

	public DisjunctionSpecification(List<Specification<T>> specifications) {
		this.specifications = specifications;
	}

	/**
	 * 是否满足至少一个规约
	 *
	 * @param candidate 候选对象
	 * @return {@code true} 满足规约 {@code false} 不满足规约
	 */
    @Override
	public boolean isSatisfiedBy(T candidate){
		return specifications.stream().anyMatch(spec -> spec.isSatisfiedBy(candidate));
    }
}
