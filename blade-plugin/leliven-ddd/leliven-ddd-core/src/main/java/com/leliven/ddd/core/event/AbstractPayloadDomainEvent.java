package com.leliven.ddd.core.event;


import lombok.Getter;
import org.springblade.common.utils.SequenceNoUtils;

import javax.annotation.Nonnull;
import java.time.LocalDateTime;

/**
 * 领域事件抽象类（携带载荷）
 *
 * <AUTHOR>
 */
@Getter
public abstract class AbstractPayloadDomainEvent<T> implements PayloadDomainEvent<T> {

	/**
	 * 事件ID
	 */
	private final String id;
	/**
	 * 事件发生时间
	 */
	private final LocalDateTime datetime;
	/**
	 * 事件载体
	 */
	@Nonnull
	private final T payload;

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	protected AbstractPayloadDomainEvent(@Nonnull T payload) {
		this.id = SequenceNoUtils.generateNo();
		this.datetime = LocalDateTime.now();
		this.payload = payload;
	}

	@Nonnull
	@Override
	public T getPayload() {
		return payload;
	}

	@Override
	public String toString() {
		return "DomainEvent{" +
			"id=" + id +
			", datetime=" + datetime +
			", payload=" + getPayload() +
			'}';
	}
}
