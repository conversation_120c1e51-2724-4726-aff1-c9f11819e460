package com.leliven.ddd.core.cache;

import java.util.Optional;

/**
 * 缓存映射器
 *
 * <AUTHOR>
 */
public interface CacheMapper<T> {

	/**
	 * 根据ID获取缓存对象
	 *
	 * @param id 主键
	 * @return 缓存对象 {@link T}
	 */
	T getById(Long id);

	/**
	 * 根据ID获取缓存对象
	 *
	 * @param id 主键
	 * @return 缓存对象 {@link Optional}<{@link T>
	 */
	default Optional<T> getOneOpt(Long id) {
		return Optional.ofNullable(getById(id));
	}

	/**
	 * 根据ID删除缓存对象
	 *
	 * @param id 主键
	 */
	void del(Long id);
}
