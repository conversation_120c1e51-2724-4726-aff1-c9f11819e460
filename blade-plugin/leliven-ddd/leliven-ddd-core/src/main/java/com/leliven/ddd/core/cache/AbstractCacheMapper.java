package com.leliven.ddd.core.cache;


import java.util.Optional;

/**
 * 缓存映射器抽象类
 *
 * <AUTHOR>
 */
public abstract class AbstractCacheMapper<T> implements CacheMapper<T> {

	@Override
	public T getById(Long id) {
		return Optional.ofNullable(id)
			.map(this::getByIdFromCache)
			.orElse(null);
	}

	@Override
	public void del(Long id) {
		if (null == id) {
			return;
		}
		delFromCache(id);
	}

	protected abstract T getByIdFromCache(Long id);

	protected abstract void delFromCache(Long id);
}
