package com.leliven.ddd.core.event;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.ObjectValidator;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Primary;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * Spring框架下的领域事件发布者实现类
 *
 * <p>该类实现了领域事件的发布功能，通过Spring的事件机制来实现事件的分发。
 * 作为默认的领域事件发布者实现，使用{@link Primary}注解标记。
 * 实现了{@link ApplicationContextAware}接口以获取Spring应用上下文，
 * 同时实现了{@link DomainEventPublisher}接口提供事件发布能力。</p>
 *
 * <p><b>事件订阅方式：</b></p>
 * <ul>
 *     <li>方式1：实现{@link org.springframework.context.ApplicationListener}接口</li>
 *     <li>方式2：使用{@link org.springframework.context.event.EventListener}注解</li>
 *     <li>方式3：使用{@link org.springframework.transaction.event.TransactionalEventListener}注解实现事务绑定的事件处理</li>
 * </ul>
 *
 * <p><b>示例代码：</b></p>
 * <pre>
 * // 方式1：实现接口
 * {@code @Component}
 * public class MyEventListener implements ApplicationListener<MyDomainEvent> {
 *     public void onApplicationEvent(MyDomainEvent event) {
 *         // 处理事件
 *     }
 * }
 *
 * // 方式2：使用注解
 * {@code @Component}
 * public class MyEventHandler {
 *     {@code @EventListener}
 *     public void handleEvent(MyDomainEvent event) {
 *         // 处理事件
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @see DomainEventPublisher
 * @see ApplicationContextAware
 * @see DomainEvent
 * @see org.springframework.context.ApplicationListener
 * @see org.springframework.context.event.EventListener
 * @see org.springframework.transaction.event.TransactionalEventListener
 */
@Slf4j
@Primary
@Component
public class SpringDomainEventPublisher implements ApplicationContextAware, DomainEventPublisher {

	private static ApplicationContext context;

	@Override
	public void setApplicationContext(@Nullable ApplicationContext applicationContext) throws BeansException {
		setContext(applicationContext);
	}

	/**
	 * 发布领域事件
	 *
	 * @param event 领域事件 {@link DomainEvent}
	 */
	@Override
	public void publishEvent(DomainEvent event) {
		ObjectValidator.requireNonNull(event, "DomainEvent can not be null");
		context.publishEvent(event);
	}

	/**
	 * 发布领域事件
	 *
	 * @param event 领域事件 {@link AbstractDomainEvent}
	 */
	public static void publish(AbstractDomainEvent<?, ?> event) {
		publish(event, true);
	}

	/**
	 * 发布领域事件
	 *
	 * @param event 领域事件 {@link AbstractDomainEvent}
	 * @param infoLogEnable 是否打印日志 {@code true} 打印 {@code false} 不打印
	 */
	public static void publish(AbstractDomainEvent<?, ?> event, boolean infoLogEnable) {
		ObjectValidator.requireNonNull(event, "DomainEvent can not be null");
		if (infoLogEnable) {
			log.info("DomainEventPublisher publish event:\n {}", event);
		}
		context.publishEvent(event);
	}

	/**
	 * 设置上下文
	 *
	 * @param applicationContext 上下文 {@link ApplicationContext}
	 */
	private static void setContext(ApplicationContext applicationContext) {
		context = applicationContext;
	}


}
