package com.leliven.ddd.core.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springblade.core.log.exception.ServiceException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 日期时间范围值对象
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class DateTimeRange {

    /**
     * 开始时间
     */
    private final LocalDateTime startDateTime;

    /**
     * 结束时间
     */
    private final LocalDateTime endDateTime;

    /**
     * 创建日期时间范围
     */
    public static DateTimeRange of(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Objects.requireNonNull(startDateTime, "开始时间不能为空");
        Objects.requireNonNull(endDateTime, "结束时间不能为空");
        if (endDateTime.isBefore(startDateTime)) {
            throw new ServiceException("结束时间不能早于开始时间");
        }
        return new DateTimeRange(startDateTime, endDateTime);
    }

    /**
     * 创建全天的日期时间范围（从开始日期的 00:00:00 到结束日期的 23:59:59）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期时间范围
     */
    public static DateTimeRange ofFullDay(LocalDate startDate, LocalDate endDate) {
        Objects.requireNonNull(startDate, "开始日期不能为空");
        Objects.requireNonNull(endDate, "结束日期不能为空");
        if (endDate.isBefore(startDate)) {
            throw new ServiceException("结束日期不能早于开始日期");
        }
        return new DateTimeRange(
            startDate.atTime(LocalTime.MIN),
            endDate.atTime(LocalTime.MAX)
        );
    }

    /**
     * 判断当前时间是否在范围内
     */
    public boolean contains(LocalDateTime dateTime) {
        Objects.requireNonNull(dateTime, "待判断时间不能为空");
        return !dateTime.isBefore(startDateTime) && !dateTime.isAfter(endDateTime);
    }

    /**
     * 判断是否有交集
     */
    public boolean hasOverlap(DateTimeRange other) {
        Objects.requireNonNull(other, "待比较时间范围不能为空");
        return !this.endDateTime.isBefore(other.startDateTime)
            && !other.endDateTime.isBefore(this.startDateTime);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DateTimeRange that = (DateTimeRange) o;
        return startDateTime.equals(that.startDateTime)
            && endDateTime.equals(that.endDateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startDateTime, endDateTime);
    }
}

