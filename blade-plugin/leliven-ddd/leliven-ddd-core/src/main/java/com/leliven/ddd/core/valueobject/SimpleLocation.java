package com.leliven.ddd.core.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

import org.springblade.core.log.exception.ServiceException;

/**
 * 简单位置值对象
 *
 * <AUTHOR>
 * @since 2024/8/27
 */
@Getter
@ValueObject
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class SimpleLocation {
    /**
     * 经度精度（小数点后6位，约等于厘米级精度）
     */
    private static final int LONGITUDE_SCALE = 6;

    /**
     * 纬度精度（小数点后6位，约等于厘米级精度）
     */
    private static final int LATITUDE_SCALE = 6;

    /**
     * 海拔精度（小数点后2位，厘米级精度）
     */
    private static final int ALTITUDE_SCALE = 2;

    /**
     * 经度最小值：-180
     */
    private static final BigDecimal MIN_LONGITUDE = new BigDecimal("-180");

    /**
     * 经度最大值：180
     */
    private static final BigDecimal MAX_LONGITUDE = new BigDecimal("180");

    /**
     * 纬度最小值：-90
     */
    private static final BigDecimal MIN_LATITUDE = new BigDecimal("-90");

    /**
     * 纬度最大值：90
     */
    private static final BigDecimal MAX_LATITUDE = new BigDecimal("90");

    /**
     * 空位置
     */
    public static final SimpleLocation EMPTY_LOCATION = new SimpleLocation(null, null, null, "");

    /**
     * 经度
     */
    private final BigDecimal longitude;

    /**
     * 纬度
     */
    private final BigDecimal latitude;

    /**
     * 海拔（米）
     */
    private final BigDecimal altitude;

    /**
     * 详细地址
     */
    private final String address;

    /**
     * 创建带校验的位置对象
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param altitude 海拔（米）
     * @param address 详细地址
     * @return 位置值对象
     * @throws ServiceException 当经纬度不合法时抛出异常
     */
    public static SimpleLocation ofValidated(BigDecimal longitude, BigDecimal latitude, BigDecimal altitude, String address) {
        validateCoordinate(longitude, latitude);
        return createLocation(longitude, latitude, altitude, address);
    }

    /**
     * 创建带校验的位置对象（不含海拔）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param address 详细地址
     * @return 位置值对象
     * @throws ServiceException 当经纬度不合法时抛出异常
     */
    public static SimpleLocation ofValidated(BigDecimal longitude, BigDecimal latitude, String address) {
        return ofValidated(longitude, latitude, null, address);
    }

    /**
     * 创建带校验的位置对象（基于Double类型参数，不含海拔）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param address 详细地址
     * @return 位置值对象
     * @throws ServiceException 当经纬度不合法时抛出异常
     */
    public static SimpleLocation ofValidated(Double longitude, Double latitude, String address) {
        BigDecimal bdLongitude = longitude != null ? new BigDecimal(longitude.toString()) : null;
        BigDecimal bdLatitude = latitude != null ? new BigDecimal(latitude.toString()) : null;

        return ofValidated(bdLongitude, bdLatitude, null, address);
    }

    /**
     * 创建无校验的位置对象（可能包含无效位置）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param altitude 海拔（米）
     * @param address 详细地址
     * @return 位置值对象
     */
    public static SimpleLocation ofUnvalidated(BigDecimal longitude, BigDecimal latitude, BigDecimal altitude, String address) {
        return createLocation(longitude, latitude, altitude, address);
    }

    /**
     * 创建无校验的位置对象（不含海拔，可能包含无效位置）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param address 详细地址
     * @return 位置值对象
     */
    public static SimpleLocation ofUnvalidated(BigDecimal longitude, BigDecimal latitude, String address) {
        return createLocation(longitude, latitude, null, address);
    }

    /**
     * 创建无校验的位置对象（基于Double类型参数，不含海拔，可能包含无效位置）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @param address 详细地址
     * @return 位置值对象
     */
    public static SimpleLocation ofUnvalidated(Double longitude, Double latitude, String address) {
        BigDecimal bdLongitude = longitude != null ? new BigDecimal(longitude.toString()) : null;
        BigDecimal bdLatitude = latitude != null ? new BigDecimal(latitude.toString()) : null;

        return createLocation(bdLongitude, bdLatitude, null, address);
    }

    /**
     * 创建空位置对象
     *
     * @return 空位置对象 {@link SimpleLocation}
     */
    public static SimpleLocation empty() {
        return EMPTY_LOCATION;
    }

    /**
     * 创建位置对象的内部方法
     */
    private static SimpleLocation createLocation(BigDecimal longitude, BigDecimal latitude, BigDecimal altitude, String address) {
        // 处理精度
        BigDecimal scaledLongitude = longitude != null ? longitude.setScale(LONGITUDE_SCALE, RoundingMode.HALF_UP) : null;
        BigDecimal scaledLatitude = latitude != null ? latitude.setScale(LATITUDE_SCALE, RoundingMode.HALF_UP) : null;

        // 海拔可以为空
        BigDecimal scaledAltitude = altitude != null ? altitude.setScale(ALTITUDE_SCALE, RoundingMode.HALF_UP) : null;

        return new SimpleLocation(scaledLongitude, scaledLatitude, scaledAltitude, address);
    }

    /**
     * 验证经纬度（公开方法，可手动校验）
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @throws ServiceException 当经纬度不合法时抛出异常
     */
    public static void validateCoordinate(BigDecimal longitude, BigDecimal latitude) {
        Objects.requireNonNull(longitude, "经度不能为空");
        Objects.requireNonNull(latitude, "纬度不能为空");

        if (longitude.compareTo(MIN_LONGITUDE) < 0 || longitude.compareTo(MAX_LONGITUDE) > 0) {
            throw new ServiceException("经度必须在-180到180之间");
        }

        if (latitude.compareTo(MIN_LATITUDE) < 0 || latitude.compareTo(MAX_LATITUDE) > 0) {
            throw new ServiceException("纬度必须在-90到90之间");
        }
    }

    /**
     * 验证当前位置对象的经纬度是否合法
     *
     * @return 是否合法
     */
    public boolean isValid() {
        if (longitude == null || latitude == null) {
            return false;
        }

        try {
            validateCoordinate(longitude, latitude);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
			return true;
		}
        if (o == null || getClass() != o.getClass()) {
			return false;
		}
        SimpleLocation that = (SimpleLocation) o;
        return Objects.equals(longitude, that.longitude) &&
               Objects.equals(latitude, that.latitude) &&
               Objects.equals(altitude, that.altitude) &&
               Objects.equals(address, that.address);
    }

    @Override
    public int hashCode() {
        return Objects.hash(longitude, latitude, altitude, address);
    }
}
