package com.leliven.ddd.core.converter;


import com.leliven.ddd.core.model.BaseDomain;
import com.leliven.ddd.core.model.TenantDomain;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Objects;

/**
 * 领域对象转换器抽象类
 *
 * @param <T> 领域对象
 * @param <D> 数据对象
 * <AUTHOR>
 */
public abstract class AbstractDomainEntityConverter<T extends BaseDomain, D extends BaseEntity>
    implements DomainEntityConverter<T, D> {

    @Override
    public T fromDO(D dataObject) {
        if (Objects.isNull(dataObject)) {
            return null;
        }

        T domainObject = doFromDO(dataObject);
        baseEntity2BaseDomain(dataObject, domainObject);
        return domainObject;
    }

    @Override
    public D toDO(T domainObject) {
        if (Objects.isNull(domainObject)) {
            return null;
        }

        D dataObject = doToDO(domainObject);
        baseDomain2BaseEntity(domainObject, dataObject);
        return dataObject;
    }

    /**
     * 数据对象转换为领域对象
     *
     * @param dataObject 领域对象
     * @return 实体对象
     */
    protected abstract T doFromDO(D dataObject);

    /**
     * 领域对象转换为数据对象
     *
     * @param domainObject 领域对象
     * @return 实体对象
     */
    protected abstract D doToDO(T domainObject);

    @SuppressWarnings("DuplicatedCode")
    private static <T extends BaseDomain, D extends BaseEntity> void baseEntity2BaseDomain(D baseEntity, T baseDomain) {
        baseDomain.setId(baseEntity.getId());
        baseDomain.setCreateTime(baseEntity.getCreateTime());
        baseDomain.setCreateUser(baseEntity.getCreateUser());
        baseDomain.setCreateDept(baseEntity.getCreateDept());
        baseDomain.setUpdateTime(baseEntity.getUpdateTime());
        baseDomain.setUpdateUser(baseEntity.getUpdateUser());
        baseDomain.setIsDeleted(baseEntity.getIsDeleted());
        baseDomain.setStatus(baseEntity.getStatus());
        if (baseDomain instanceof TenantDomain && baseEntity instanceof TenantEntity) {
            ((TenantDomain) baseDomain).setTenantId(((TenantEntity) baseEntity).getTenantId());
        }
    }

    @SuppressWarnings("DuplicatedCode")
    private static <T extends BaseDomain, D extends BaseEntity> void baseDomain2BaseEntity(T baseDomain, D baseEntity) {
        baseEntity.setId(baseDomain.getId());
        baseEntity.setCreateTime(baseDomain.getCreateTime());
        baseEntity.setCreateUser(baseDomain.getCreateUser());
        baseEntity.setCreateDept(baseDomain.getCreateDept());
        baseEntity.setUpdateTime(baseDomain.getUpdateTime());
        baseEntity.setUpdateUser(baseDomain.getUpdateUser());
        baseEntity.setIsDeleted(baseDomain.getIsDeleted());
        baseEntity.setStatus(baseDomain.getStatus());
        if (baseEntity instanceof TenantEntity && baseDomain instanceof TenantDomain) {
            ((TenantEntity) baseEntity).setTenantId(((TenantDomain) baseDomain).getTenantId());
        }
    }

}
