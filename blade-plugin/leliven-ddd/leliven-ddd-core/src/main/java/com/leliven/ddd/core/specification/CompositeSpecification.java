package com.leliven.ddd.core.specification;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * 领域规约组合接口
 *
 * <p>
 * 规约组合接口，用于实现规约的组合
 * </p>
 *
 * <AUTHOR>
 * @param <T> 规约对象类型
 */
public abstract class CompositeSpecification<T> implements Specification<T> {

	/**
	 * 与规约
	 *
	 * @param other 规约
	 * @return 与规约 {@link AndSpecification}
	 */
	@Override
	public Specification<T> and(Specification<T> other) {
		return new AndSpecification<>(this, other);
	}

	/**
	 * 或规约
	 *
	 * @param other 规约
	 * @return 或规约 {@link OrSpecification}
	 */
	@Override
	public Specification<T> or(Specification<T> other) {
		return new OrSpecification<>(this, other);
	}

	/**
	 * 取反规约
	 *
	 * @return 取反规约 {@link NotSpecification}
	 */
	@Override
	public Specification<T> not() {
		return new NotSpecification<>(this);
	}

	/**
	 * 合并规约
	 *
	 * @param others 规约列表
	 * @return 合并规约 {@link ConjunctionSpecification}
	 */
	@SafeVarargs
	@Override
	public final Specification<T> conjunction(Specification<T>... others) {
		return conjunction(Arrays.asList(others));
	}

	/**
	 * 合并规约
	 *
	 * @param others 规约列表
	 * @return 合并规约 {@link ConjunctionSpecification}
	 */
	@Override
	public final Specification<T> conjunction(List<Specification<T>> others) {
		List<Specification<T>> specs = new ArrayList<>();
		specs.add(this);
		specs.addAll(others);
		return new ConjunctionSpecification<>(specs);
	}
}
