package com.leliven.ddd.core.specification;

import java.util.Arrays;
import java.util.List;

/**
 * 领域规约接口
 *
 * <AUTHOR>
 */
public interface Specification<T> {

    /**
     * 是否满足规约
     *
     * @param candidate 候选对象 {@code T}
     * @return {@code true} 满足规约 {@code false} 不满足规约
     */
    boolean isSatisfiedBy(T candidate);

    /**
     * 与规约
     *
     * @param other 规约
     * @return 与规约 {@link AndSpecification}
     */
    default Specification<T> and(Specification<T> other) {
        return candidate -> this.isSatisfiedBy(candidate) && other.isSatisfiedBy(candidate);
    }

    /**
     * 或规约
     *
     * @param other 规约
     * @return 或规约 {@link OrSpecification}
     */
    default Specification<T> or(Specification<T> other) {
        return candidate -> this.isSatisfiedBy(candidate) || other.isSatisfiedBy(candidate);
    }

    /**
     * 取反规约
     *
     * @return 取反规约 {@link NotSpecification}
     */
    default Specification<T> not() {
        return candidate -> !this.isSatisfiedBy(candidate);
    }

    /**
     * 合并规约
     *
     * @param others 规约列表
     * @return 合并规约 {@link ConjunctionSpecification}
     */
    default Specification<T> conjunction(Specification<T>... others) {
        return conjunction(Arrays.asList(others));
    }

    /**
     * 合并规约
     *
     * @param others 规约列表
     * @return 合并规约 {@link ConjunctionSpecification}
     */
    default Specification<T> conjunction(List<Specification<T>> others) {
        return candidate -> {
            if (!this.isSatisfiedBy(candidate)) {
                return false;
            }

            for (Specification<T> spec : others) {
                if (!spec.isSatisfiedBy(candidate)) {
                    return false;
                }
            }
            return true;
        };
    }

}
