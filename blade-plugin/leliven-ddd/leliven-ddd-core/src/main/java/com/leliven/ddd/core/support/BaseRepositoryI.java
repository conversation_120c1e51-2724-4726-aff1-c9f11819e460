package com.leliven.ddd.core.support;

/**
 * 仓储接口
 * T: 领域实体
 * <AUTHOR>
 */
public interface BaseRepositoryI<T> {

	/**
	 * 根据ID获取领域实体
	 *
	 * @param id ID
	 * @return 实体
	 */
	T getById(Long id);

	/**
	 * 保存领域实体
	 *
	 * @param domainEntity 实体
	 * @return 是否成功
	 */
	boolean save(T domainEntity);

	/**
	 * 更新领域实体
	 *
	 * @param domainEntity 实体
	 * @return 是否成功
	 */
	boolean update(T domainEntity);
}
