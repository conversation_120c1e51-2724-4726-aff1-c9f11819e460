package com.leliven.ddd.core.gateway;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.service.IService;
import com.leliven.ddd.core.model.primitive.PrimitiveTenantSupport;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.ddd.core.model.Domain;
import org.springblade.common.exception.NotFoundException;
import org.springblade.common.exception.PersistenceException;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.utils.Func;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MybatisPlus版 - 网关基础实现类
 *
 * <p>
 * 通过构造注入增强映射器 {@link IService}、领域对象转换器 {@link DomainEntityConverter}，
 * 统一处理领域对象与数据对象的转换
 * </p>
 *
 * @param <D> 领域对象类型
 * @param <ID> 主键类型
 * @param <E> 数据对象类型
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S119")
public abstract class MybatisPlusGateway<D extends Domain<ID>, ID extends Serializable, E> implements BaseGateway<D, ID> {

	/**
	 * 增强映射器 - 负责复杂查询和数据操作
	 */
	protected final IService<E> enhancedMapper;

	/**
	 * 领域转换器 - 负责领域对象与数据对象的转换
	 */
	protected final DomainEntityConverter<D, E> converter;

	/**
	 * 构造器 - 强制设置依赖
	 *
	 * @param enhancedMapper 增强服务
	 * @param converter 转换器
	 */
	protected MybatisPlusGateway(IService<E> enhancedMapper, DomainEntityConverter<D, E> converter) {
		this.enhancedMapper = Objects.requireNonNull(enhancedMapper, "增强映射器不能为空");
		this.converter = Objects.requireNonNull(converter, "领域转换器不能为空");
	}

	/**
	 * 保存后回填数据扩展点
	 *
	 * <p>
	 * 子类可以重写此方法来实现保存后的数据回填逻辑
	 * 例如：回填主键ID、租户ID等数据库生成的字段
	 * </p>
	 *
	 *
	 * @param domain 领域对象
	 * @param dataObject 数据对象
	 */
	protected void backFillAfterSave(D domain, E dataObject) {

		// 回填租户ID（如果是租户相关的领域对象）
		if (domain instanceof PrimitiveTenantSupport && dataObject instanceof TenantEntity) {
			PrimitiveTenantSupport tenantSupport = (PrimitiveTenantSupport) domain;
			if (!tenantSupport.hasValidTenant()) {
				tenantSupport.setTenantId(((TenantEntity) dataObject).getTenantId());
			}
		}
	}

	@Override
	public D get(ID id) {
		E dataObject = enhancedMapper.getById(id);
		if (dataObject == null) {
			return null;
		}
		return converter.fromDO(dataObject);
	}

	@Override
	public Optional<D> getOptional(ID id) {
		return Optional.ofNullable(get(id));
	}

	@Override
	public D getOrElseThrow(ID id) {
		D domain = get(id);
		if (domain == null) {
			throw new NotFoundException("数据不存在，ID: " + id);
		}
		return domain;
	}

	@Override
	public boolean exists(ID id) {
		TableInfo tableInfo = TableInfoHelper.getTableInfo(this.enhancedMapper.getEntityClass());
        Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
        String keyColumn = tableInfo.getKeyColumn();
        Assert.notEmpty(keyColumn, "error: can not execute. because can not find column for id from entity!");
		return this.enhancedMapper.count(new QueryWrapper<E>().eq(keyColumn, id)) > 0;
	}

	@Override
	public List<D> list(Collection<? extends ID> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return Collections.emptyList();
		}
		return converter.fromDOList(enhancedMapper.listByIds(ids));
	}

	@Override
	public void save(D domain) {
        if (saveWithResult(domain)) {
            throw new PersistenceException("保存数据失败");
        }
	}

	@Override
	public boolean saveWithResult(D domain) {
		E dataObject = converter.toDO(domain);
		boolean saved = enhancedMapper.save(dataObject);
		if (saved) {
			backFillAfterSave(domain, dataObject);
		}
		return saved;
	}

	@Override
	public boolean saveBatch(List<D> domains) {
		if (CollectionUtils.isEmpty(domains)) {
			return true;
		}

		List<E> dataObjects = domains.stream()
			.map(converter::toDO)
			.collect(Collectors.toList());

		return enhancedMapper.saveBatch(dataObjects);
	}

	@Override
	public void update(D domain) {
        if (Func.isEmpty(domain.getId())) {
            throw new PersistenceException("更新数据失败，数据ID不能为空");
        }

		if (!updateWithResult(domain)) {
			throw new PersistenceException("更新数据失败");
		}
	}

	@Override
	public boolean updateWithResult(D domain) {
		E dataObject = converter.toDO(domain);
		return enhancedMapper.updateById(dataObject);
	}

	@Override
	public boolean updateBatch(List<D> domains) {
		if (CollectionUtils.isEmpty(domains)) {
			return false;
		}

		return enhancedMapper.updateBatchById(converter.toDOList(domains));
	}

	@Override
	public boolean saveOrUpdate(D domain) {
		if (domain.getId() != null) {
			return updateWithResult(domain);
		}

		return saveWithResult(domain);
	}

	@Override
	public boolean saveOrUpdateBatch(Collection<D> domains) {
		if (CollectionUtils.isEmpty(domains)) {
			return false;
		}

		List<E> dataObjects = domains.stream()
			.map(converter::toDO)
			.collect(Collectors.toList());

		return enhancedMapper.saveOrUpdateBatch(dataObjects);
	}

	@Override
	public boolean deleteById(ID id) {
		return enhancedMapper.removeById(id);
	}

	@Override
	public boolean delete(D domain) {
		return enhancedMapper.removeById(domain.getId());
	}

	@Override
	public boolean deleteBatch(Collection<? extends ID> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return false;
		}

		return enhancedMapper.removeByIds(ids);
	}

}
