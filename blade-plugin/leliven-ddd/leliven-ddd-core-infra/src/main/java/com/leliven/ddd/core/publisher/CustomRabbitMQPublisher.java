package com.leliven.ddd.core.publisher;

import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import com.leliven.ddd.core.support.MQPublisher;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
public abstract class CustomRabbitMQPublisher<T> implements MQPublisher {

	protected IMsgSender msgSender;

	protected void publish(String msgCode, T object) {
		if (null == object) {
			return;
		}

		msgSender.send(
			MsgRequest.builder()
				.code(msgCode)
				.body(object)
				.build()
		);
	}

	@Resource
	public void setMsgSender(IMsgSender msgSender) {
		this.msgSender = msgSender;
	}


}
