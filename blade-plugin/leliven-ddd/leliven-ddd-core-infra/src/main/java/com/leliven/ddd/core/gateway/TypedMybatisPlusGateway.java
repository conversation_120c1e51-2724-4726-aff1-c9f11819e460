package com.leliven.ddd.core.gateway;

import com.baomidou.mybatisplus.extension.service.IService;
import com.leliven.ddd.core.model.typed.DomainID;
import com.leliven.ddd.core.model.typed.StronglyTypedDomain;
import com.leliven.ddd.core.converter.DomainEntityConverter;

import java.io.Serializable;

/**
 * MybatisPlus版 - 网关基础实现类
 *
 * <p>
 * 通过构造注入增强映射器 {@link IService}、领域对象转换器 {@link DomainEntityConverter}，
 * 统一处理领域对象与数据对象的转换
 * </p>
 *
 * @param <D> 领域对象类型
 * @param <ID> 主键类型
 * @param <E> 数据对象类型
 *
 * <AUTHOR>
 */
public abstract class TypedMybatisPlusGateway<D extends StronglyTypedDomain<ID, VALUE_TYPE>,
	ID extends DomainID<VALUE_TYPE>, VALUE_TYPE extends Serializable, E> {

	/**
	 * 构造器 - 强制设置依赖
	 *
	 * @param enhancedMapper 增强服务
	 * @param converter 转换器
	 */
	protected TypedMybatisPlusGateway(IService<E> enhancedMapper, DomainEntityConverter<D, E> converter) {

	}


}
