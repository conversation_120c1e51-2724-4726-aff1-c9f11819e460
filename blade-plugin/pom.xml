<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>leliven-parking</artifactId>
        <groupId>org.springblade</groupId>
        <version>2.8.1.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>blade-plugin</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>parking-core-pay</module>
        <module>parking-core-geohash</module>
        <module>parking-core-vehicle</module>
        <module>parking-starter-log</module>
        <module>parking-starter-notify</module>
        <module>parking-starter-mq</module>
        <module>parking-core-log</module>
        <module>parking-core-openapi</module>
        <module>parking-core-tool</module>
        <module>parking-core-auth</module>
        <module>parking-core-notify</module>
        <module>parking-core-report</module>
        <module>parking-core-china-telecom</module>
        <module>parking-core-videostream</module>
        <module>parking-starter-ocr</module>
        <module>parking-starter-third-party</module>
        <module>parking-core-importexport</module>
        <module>leliven-ddd</module>
    </modules>
</project>
