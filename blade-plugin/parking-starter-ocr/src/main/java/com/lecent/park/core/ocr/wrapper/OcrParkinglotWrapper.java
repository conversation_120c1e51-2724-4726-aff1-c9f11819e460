package com.lecent.park.core.ocr.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.core.ocr.entity.OcrParkinglot;
import com.lecent.park.core.ocr.vo.OcrParkinglotVO;
import java.util.Objects;

/**
 * OCR车场关联表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class OcrParkinglotWrapper extends BaseEntityWrapper<OcrParkinglot, OcrParkinglotVO>  {

	public static OcrParkinglotWrapper build() {
		return new OcrParkinglotWrapper();
 	}

	@Override
	public OcrParkinglotVO entityVO(OcrParkinglot ocrParkinglot) {
		OcrParkinglotVO ocrParkinglotVO = Objects.requireNonNull(BeanUtil.copy(ocrParkinglot, OcrParkinglotVO.class));

		//User createUser = UserCache.getUser(ocrParkinglot.getCreateUser());
		//User updateUser = UserCache.getUser(ocrParkinglot.getUpdateUser());
		//ocrParkinglotVO.setCreateUserName(createUser.getName());
		//ocrParkinglotVO.setUpdateUserName(updateUser.getName());

		return ocrParkinglotVO;
	}

}
