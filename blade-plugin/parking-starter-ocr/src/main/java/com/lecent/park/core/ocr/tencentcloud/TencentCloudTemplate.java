package com.lecent.park.core.ocr.tencentcloud;

import com.lecent.park.core.ocr.OcrTemplate;
import com.lecent.park.core.ocr.entity.OcrSetting;
import com.lecent.park.core.ocr.model.DriverOcrLicensePlateResult;
import com.lecent.park.core.ocr.model.OcrLicensePlateResult;
import com.lecent.park.core.ocr.model.VehicleLicenseResult;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.*;
import org.springblade.core.tool.utils.Func;

/**
 * 腾讯云 OCR 模版实现类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */

public class TencentCloudTemplate implements OcrTemplate {

	private final String endpoint = "ocr.tencentcloudapi.com";

	private OcrClient client;

	public TencentCloudTemplate(OcrSetting ocrSetting){

		Credential cred = new Credential(ocrSetting.getSecretId(), ocrSetting.getSecretKey());
		// 实例化一个http选项，可选的，没有特殊需求可以跳过
		HttpProfile httpProfile = new HttpProfile();
		httpProfile.setEndpoint(endpoint);
		// 实例化一个client选项，可选的，没有特殊需求可以跳过
		ClientProfile clientProfile = new ClientProfile();
		clientProfile.setHttpProfile(httpProfile);
		// 实例化要请求产品的client对象,clientProfile是可选的
		OcrClient client = new OcrClient(cred, ocrSetting.getRegion(), clientProfile);
		this.client = client;
	}


	@Override
	public OcrLicensePlateResult LicenseOcrBase64(String base64Str) {

		if(Func.isEmpty(base64Str)){
			return new OcrLicensePlateResult(false, "识别参数不能为空");
		}

		try {
			LicensePlateOCRRequest req = new LicensePlateOCRRequest();
			req.setImageBase64(base64Str);
			LicensePlateOCRResponse resp = client.LicensePlateOCR(req);
			return getOcrLicensePlateResult(resp);
		} catch (TencentCloudSDKException e) {
			return new OcrLicensePlateResult(false, e.getMessage());
		}
	}


	@Override
	public OcrLicensePlateResult LicenseOcrUrl(String imageUrl) {

		if(Func.isEmpty(imageUrl)){
			return new OcrLicensePlateResult(false, "识别参数不能为空");
		}

		try {
			LicensePlateOCRRequest req = new LicensePlateOCRRequest();
			req.setImageUrl(imageUrl);
			LicensePlateOCRResponse resp = client.LicensePlateOCR(req);
			return getOcrLicensePlateResult(resp);
		} catch (TencentCloudSDKException e) {
			return new OcrLicensePlateResult(false, e.getMessage());
		}
	}

	@Override
	public DriverOcrLicensePlateResult DriverLicenseOceBase64(String base64Str) {
		if(Func.isEmpty(base64Str)){
			return new DriverOcrLicensePlateResult(false, "识别参数不能为空");
		}
		try {
			DriverLicenseOCRRequest req = new DriverLicenseOCRRequest();
			req.setImageBase64(base64Str);
			req.setCardSide("FRONT");
			DriverLicenseOCRResponse resp = client.DriverLicenseOCR(req);
			return getDriverOcrLicensePlateResult(resp);
		} catch (TencentCloudSDKException e) {
			return new DriverOcrLicensePlateResult(false, e.getMessage());
		}

	}

	@Override
	public DriverOcrLicensePlateResult DriverLicenseOcrUrl(String imageUrl) {
		if(Func.isEmpty(imageUrl)){
			return new DriverOcrLicensePlateResult(false, "识别参数不能为空");
		}
		try {
			DriverLicenseOCRRequest req = new DriverLicenseOCRRequest();
			req.setImageUrl(imageUrl);
			req.setCardSide("FRONT");
			DriverLicenseOCRResponse resp = client.DriverLicenseOCR(req);
			return getDriverOcrLicensePlateResult(resp);
		} catch (TencentCloudSDKException e) {
			return new DriverOcrLicensePlateResult(false, e.getMessage());
		}
	}


	@Override
	public VehicleLicenseResult VehicleLicenseOcrBase64(String base64Str){
		try {
			VehicleLicenseOCRRequest req = new VehicleLicenseOCRRequest();
			req.setImageBase64(base64Str);
			req.setCardSide("FRONT");
			VehicleLicenseOCRResponse resp = client.VehicleLicenseOCR(req);
			return getVehicleLicenseResult(resp);
		} catch (TencentCloudSDKException e) {
			return new VehicleLicenseResult(false, e.getMessage());
		}
	}

	@Override
	public VehicleLicenseResult VehicleLicenseOcrUrl(String imageUrl){
		try {
			VehicleLicenseOCRRequest req = new VehicleLicenseOCRRequest();
			req.setImageUrl(imageUrl);
			req.setCardSide("FRONT");
			VehicleLicenseOCRResponse resp = client.VehicleLicenseOCR(req);
			return getVehicleLicenseResult(resp);
		} catch (TencentCloudSDKException e) {
			return new VehicleLicenseResult(false, e.getMessage());
		}
	}




	private OcrLicensePlateResult getOcrLicensePlateResult(LicensePlateOCRResponse res){
		OcrLicensePlateResult result = new OcrLicensePlateResult();
		result.setColor(res.getColor());
		result.setConfidence(res.getConfidence());
		result.setNumber(res.getNumber());
		return result;
	}

	private DriverOcrLicensePlateResult getDriverOcrLicensePlateResult(DriverLicenseOCRResponse resp){
		DriverOcrLicensePlateResult result = new DriverOcrLicensePlateResult();
		result.setAddress(resp.getAddress());
		result.setArchivesCode(resp.getArchivesCode());
		result.setCardCode(resp.getCardCode());
		result.setClassModel(resp.getClass_());
		result.setDateOfBirth(resp.getDateOfBirth());
		result.setDateOfFirstIssue(resp.getDateOfFirstIssue());
		result.setEndDate(resp.getEndDate());
		result.setIssuingAuthority(resp.getIssuingAuthority());
		result.setName(resp.getName());
		result.setNationality(resp.getNationality());
		result.setSex(resp.getSex());
		result.setStartDate(resp.getStartDate());
		return result;
	}

	private VehicleLicenseResult getVehicleLicenseResult(VehicleLicenseOCRResponse resp){
		VehicleLicenseResult result = new VehicleLicenseResult();
		result.setAddress(resp.getFrontInfo().getAddress());
		result.setEngineNo(resp.getFrontInfo().getEngineNo());
		result.setIssueDate(resp.getFrontInfo().getIssueDate());
		result.setModel(resp.getFrontInfo().getModel());
		result.setOwner(resp.getFrontInfo().getOwner());
		result.setPlateNo(resp.getFrontInfo().getPlateNo());
		result.setRegisterDate(resp.getFrontInfo().getRegisterDate());
		result.setSeal(resp.getFrontInfo().getSeal());
		result.setUseCharacter(resp.getFrontInfo().getUseCharacter());
		result.setVehicleType(resp.getFrontInfo().getVehicleType());
		result.setVin(resp.getFrontInfo().getVin());
		return result;
	}

}
