package com.lecent.park.core.ocr.vo;

import com.lecent.park.core.ocr.entity.OcrParkinglot;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * OCR车场关联表视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OcrParkinglotVO对象", description = "OCR车场关联表")
public class OcrParkinglotVO extends OcrParkinglot {
	private static final long serialVersionUID = 1L;

}
