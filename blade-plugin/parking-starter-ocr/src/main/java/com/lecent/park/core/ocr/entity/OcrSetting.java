package com.lecent.park.core.ocr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * OCR信息表实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("d_ocr_setting")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OcrSetting对象", description = "OCR信息表")
public class OcrSetting extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 地域简称
	 */
	@ApiModelProperty(value = "地域简称")
	private String region;
	/**
	 * 服务商编号
	 */
	@ApiModelProperty(value = "服务商编号")
	private String ocrCode;
	/**
	 * secret_id
	 */
	@ApiModelProperty(value = "secret_id")
	private String secretId;
	/**
	 * secret_key
	 */
	@ApiModelProperty(value = "secret_key")
	private String secretKey;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
