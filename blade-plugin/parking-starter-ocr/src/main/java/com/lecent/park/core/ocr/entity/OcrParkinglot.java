package com.lecent.park.core.ocr.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * OCR车场关联表实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("d_ocr_parkinglot")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OcrParkinglot对象", description = "OCR车场关联表")
public class OcrParkinglot extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * ocr_setting_id
	 */
	@ApiModelProperty(value = "ocr_setting_id")
	private Long ocrSettingId;
	/**
	 * 车场ID
	 */
	@ApiModelProperty(value = "车场ID")
	private String parkinglotId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
