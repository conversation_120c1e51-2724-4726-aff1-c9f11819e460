package com.lecent.park.core.ocr.vo;

import com.lecent.park.core.ocr.entity.OcrSetting;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * OCR信息表视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OcrSettingVO对象", description = "OCR信息表")
public class OcrSettingVO extends OcrSetting {
	private static final long serialVersionUID = 1L;

}
