package com.lecent.park.core.ocr.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import com.lecent.park.core.ocr.entity.OcrSetting;
import com.lecent.park.core.ocr.vo.OcrSettingVO;
import java.util.Objects;

/**
 * OCR信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class OcrSettingWrapper extends BaseEntityWrapper<OcrSetting, OcrSettingVO>  {

	public static OcrSettingWrapper build() {
		return new OcrSettingWrapper();
 	}

	@Override
	public OcrSettingVO entityVO(OcrSetting ocrSetting) {
		OcrSettingVO ocrSettingVO = Objects.requireNonNull(BeanUtil.copy(ocrSetting, OcrSettingVO.class));

		//User createUser = UserCache.getUser(ocrSetting.getCreateUser());
		//User updateUser = UserCache.getUser(ocrSetting.getUpdateUser());
		//ocrSettingVO.setCreateUserName(createUser.getName());
		//ocrSettingVO.setUpdateUserName(updateUser.getName());

		return ocrSettingVO;
	}

}
