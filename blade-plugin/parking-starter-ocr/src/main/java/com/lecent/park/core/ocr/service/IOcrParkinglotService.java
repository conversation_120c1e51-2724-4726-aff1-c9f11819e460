package com.lecent.park.core.ocr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lecent.park.core.ocr.entity.OcrParkinglot;
import com.lecent.park.core.ocr.vo.OcrParkinglotVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * OCR车场关联表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface IOcrParkinglotService extends IService<OcrParkinglot> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param ocrParkinglot
	 * @return
	 */
	IPage<OcrParkinglotVO> selectOcrParkinglotPage(IPage<OcrParkinglotVO> page, OcrParkinglotVO ocrParkinglot);

}
