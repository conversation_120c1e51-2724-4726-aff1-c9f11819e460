package com.lecent.park.core.thirdparty.dto.sitech;


import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上传停车场信息 DTO
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月12日
 */
@Data
public class UpParkingSpaceDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "停车场 id, 合作伙伴标识该停车场唯一标识即可", required = true)
	private String parkId;

	@ApiModelProperty(value = "停车场名称", required = true)
	private String parkName;

	@ApiModelProperty(value = "停车车位总数", required = true)
	private Integer totalParkingSpace;

	@ApiModelProperty(value = "停车场空位总数", required = true)
	private Integer emptyParkingSpace;

	@ApiModelProperty(value = "停车场已用总数", required = true)
	private Integer usedParkingSpace;

	@ApiModelProperty(value = "停车场所在省", required = true)
	private String province;

	@ApiModelProperty(value = "停车场所在市", required = true)
	private String city;

	@ApiModelProperty(value = "停车场所在区(县)", required = true)
	private String country;

	@ApiModelProperty(value = "停车场详细地址", required = true)
	private String address;

	@ApiModelProperty(value = "开放状态 (1 完全开放, 2 部分开放, 3 不开放 )", required = true)
	private String openStatus;

	@ApiModelProperty(value = "收费规则", required = true)
	private String chargeRule;

	@ApiModelProperty(value = "经度", required = true)
	private Double longitude;

	@ApiModelProperty(value = "纬度", required = true)
	private Double latitude;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "厂商名称", required = true)
	private String trade;

	@ApiModelProperty(value = "租户，不json")
	@JSONField(serialize = false)
	private String tenantId;

}
