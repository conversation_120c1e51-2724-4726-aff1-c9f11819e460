package com.lecent.park.core.thirdparty.dto.sitech;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 接口请求 DTO
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月12日
 */
@Data
public class SiTechRequestDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "签名方式", required = true)
	private String signType;

	@ApiModelProperty(value = "按照签名方式生成的MD5", required = true)
	private String sign;

	@ApiModelProperty(value = "合作伙伴id", required = true)
	private String partnerId;

	@ApiModelProperty(value = "请求上行数据", required = true)
	private Object data;

}
