package com.lecent.park.core.thirdparty.manager;


import com.lecent.park.core.thirdparty.dto.sitech.UpParkChargeLogDTO;
import com.lecent.park.core.thirdparty.dto.sitech.UpParkInLogDTO;
import com.lecent.park.core.thirdparty.dto.sitech.UpParkingSpaceDTO;



/**
 * 思特奇开放接口调用接口
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月13日
 */
public interface ISiTechService {

	/**
	 * 上传车辆收费记录
	 * @param dto
	 */
	void upParkChargeLog(UpParkChargeLogDTO dto);

	/**
	 * 上传停车场信息
	 * @param dto
	 */
	void upParkingSpace(UpParkingSpaceDTO dto);

	/**
	 * 上传车辆出入场记录
	 * @param dto
	 */
	void upParkInLog(UpParkInLogDTO dto);

}
