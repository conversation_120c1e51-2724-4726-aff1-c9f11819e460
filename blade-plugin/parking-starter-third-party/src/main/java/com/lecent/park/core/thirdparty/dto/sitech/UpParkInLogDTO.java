package com.lecent.park.core.thirdparty.dto.sitech;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上传车辆出入场记录  DTO
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月12日
 */
@Data
public class UpParkInLogDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "停车场ID，用于唯一标识停车场", required = true)
	private String parkId;

	@ApiModelProperty(value = "车牌号，用于唯一标识车辆", required = true)
	private String plateNum;

	@ApiModelProperty(value = "车辆入场时间", required = true)
	private String inTime;

	@ApiModelProperty(value = "车辆出场时间，如果车辆尚未出场，则可以为空")
	private String outTime;

	@ApiModelProperty(value = "计费类型0临时车1月租车2充值车3贵宾车4免费车8收费月租车 ")
	private Integer cardTypeId;

	@ApiModelProperty(value = "计费类型名称 如:临时车")
	private String cardType;

	@ApiModelProperty(value = "车辆类型名称 0 小型车 1大型车 ", required = true)
	private Integer carTypeId;

	@ApiModelProperty(value = "车辆类型名称 如:小型车或 大型车 ", required = true)
	private String carType;

	@ApiModelProperty(value = "入场通道ID")
	private Long channelId;

	@ApiModelProperty(value = "入场通道名称 ")
	private String channelName;

	@ApiModelProperty(value = "入场值班人员id 例：0(没有就传0) ")
	private String auid;

	@ApiModelProperty(value = "入场值班人员名称 例:无（没有就传无） ")
	private String realName;

	@ApiModelProperty(value = "备注信息，用于记录额外的停车相关信息")
	private String remark;

	@JSONField(serialize = false)
	@ApiModelProperty(value = "租户，不json")
	private String tenantId;

}
