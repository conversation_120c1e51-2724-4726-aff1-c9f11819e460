package com.lecent.park.core.thirdparty.enums.sitech;

import lombok.Getter;

/**
 * 错误类枚举
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月13日
 */
@Getter
public enum ResultCode {

	// 定义枚举常量，每个常量都包含错误代码和对应的中文描述
	SUCCESS("200", "处理成功"),
	REQUEST_PARAM_ERROR("400", "请求参数有误"),
	UNAUTHORIZED("401", "未授权"),
	SIGNATURE_VERIFICATION_FAILED("402", "验签失败"),
	PARAMETER_VERIFICATION_FAILED("485", "参数校验失败"),
	SERVER_INTERNAL_ERROR("500", "服务器内部错误"),
	BUSINESS_EXCEPTION("501", "业务异常"),
	SIGN_CANNOT_BE_EMPTY("502", "sign 不能为空"),
	SIGNATURE_CHECK_FAILED("503", "签名校验失败"),
	PARTNER_ID_CANNOT_BE_EMPTY("504", "partnerId不能为空"),
	PARTNER_ID_NOT_EXIST("505", "partnerId不存在"),
	UNSUPPORTED_SIGNATURE("506", "不支持的验签"),
	PARAMETER_VERIFICATION_ERROR("507", "参数校验错误");

	// 枚举的字段
	private final String code;
	private final String message;

	// 枚举的构造方法
	ResultCode(String code, String message) {
		this.code = code;
		this.message = message;
	}


	// 一个静态方法，根据错误代码返回对应的枚举常量
	public static ResultCode fromCode(String code) {
		for (ResultCode errorCode : ResultCode.values()) {
			if (errorCode.getCode() == code) {
				return errorCode;
			}
		}
		return null;
	}

}
