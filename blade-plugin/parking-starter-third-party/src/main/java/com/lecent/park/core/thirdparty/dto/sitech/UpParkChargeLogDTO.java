package com.lecent.park.core.thirdparty.dto.sitech;


import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 上传车辆收费记录 DTO
 * 第三方：北京思特奇信息技术股份有限公司
 * <AUTHOR>
 * @date 2024年8月12日
 */
@Data
public class UpParkChargeLogDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "停车场 id, 合作伙伴标识该停车场唯一标识即可", required = true)
	private String parkId;

	@ApiModelProperty(value = "车牌号", required = true)
	private String plateNum;

	@ApiModelProperty(value = "入场时间", required = true)
	private String inTime;

	@ApiModelProperty(value = "出场时间", required = true)
	private String outTime;

	@JSONField(name = "charge_time")
	@ApiModelProperty(value = "收费时间", required = true)
	private String chargeTime;

	@ApiModelProperty(value = "停车时长 (单位:秒)", required = true)
	private Integer parkTime;

	@ApiModelProperty(value = "计费类型 0 临时车 1 月租车 2 充值车 3 贵宾车 4 免费车 5 收费月租车")
	private Integer cardTypeId;

	@ApiModelProperty(value = "计费类型名称 如:临时车")
	private String cardType;

	@ApiModelProperty(value = "车辆类型 id 0 小型车 1 大型车", required = true)
	private Integer carTypeId;

	@ApiModelProperty(value = "车辆类型名称 小型车或大型车", required = true)
	private String carType;

	@ApiModelProperty(value = "是否免费 0 不免费 1 免费")
	private Integer isFreeMoney;

	@ApiModelProperty(value = "停车应缴金额", required = true)
	private BigDecimal money;

	@ApiModelProperty(value = "停车免费金额")
	private BigDecimal freeMoney;

	@ApiModelProperty(value = "值班员实际收费金额", required = true)
	private BigDecimal chargeMoney;

	@ApiModelProperty(value = "扣费金额 注:根据实际情况")
	private BigDecimal deductionMoney;

	@ApiModelProperty(value = "折扣金额 注:根据实际情况")
	private BigDecimal discount;

	@ApiModelProperty(value = "减免金额 注:根据实际情况")
	private BigDecimal breaksMoney;

	@ApiModelProperty(value = "缴费方式 (0:现金, 1:微信支付, 2:支付宝支付, 3:其他)", required = true)
	private Integer feeType;

	@ApiModelProperty(value = "入场通道 id")
	private String channelId;

	@ApiModelProperty(value = "入场通道名称")
	private String channelName;

	@ApiModelProperty(value = "入场值班人员 id 例:0(没有就传0)")
	private String auid;

	@ApiModelProperty(value = "入场值班人员名称 例: 无 (没有就传 无)")
	private String realName;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "租户，不json")
	@JSONField(serialize = false)
	private String tenantId;

}
