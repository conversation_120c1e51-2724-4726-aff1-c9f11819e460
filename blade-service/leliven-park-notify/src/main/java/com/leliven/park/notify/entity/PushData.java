/**
 * Copyright 2023-2033, likavn (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.leliven.park.notify.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息数据
 *
 * <AUTHOR>
 * @date 2024/3/31
 **/
@Data
@TableName("push_data")
public class PushData {
	/**
	 * 事件ID,默认UUID
	 */
	@TableId
	private Long id;

	/**
	 * 消息类型，用于区分不同的消息类型
	 */
	private String code;

	/**
	 * 关联业务ID
	 */
	private Long businessId;

	/**
	 * 消息体，必须包含无参构造函数
	 */
	private String body;

	/**
	 * 发送者IP
	 */
	private String ipAddress;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
}
