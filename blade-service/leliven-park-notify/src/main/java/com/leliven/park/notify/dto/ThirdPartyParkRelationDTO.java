package com.leliven.park.notify.dto;

import com.leliven.park.notify.entity.PushAppParkRelation;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方车场关联表数据传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ThirdPartyParkRelationDTO对象", description = "第三方车场关联表")
public class ThirdPartyParkRelationDTO extends PushAppParkRelation {

}
