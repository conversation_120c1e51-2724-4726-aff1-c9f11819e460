package com.leliven.park.notify.handler.gyszct;

import com.lecent.park.common.constant.ParkMQConstant;
import com.lecent.park.core.thirdparty.guiyangparking.dto.ChangeOrderInfoRequest;
import com.lecent.park.core.thirdparty.guiyangparking.service.IGuiYangParkingService;
import com.lecent.park.core.thirdparty.guiyangparking.util.GyzhtcOrderUtils;
import com.leliven.park.notify.domain.PushContext;
import com.leliven.park.notify.domain.dto.RequestParkingOrderDto;
import com.leliven.park.notify.entity.PushAppParkRelation;
import com.leliven.park.notify.handler.gyszct.base.GyszctBasePushHandler;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO 稽核
 * @createTime 2025/6/18 17:45
 */
@Slf4j
@Component
public class GyszctParkPushCarAuditHandler extends GyszctBasePushHandler<RequestParkingOrderDto> {
	@Autowired
	private IGuiYangParkingService guiYangParkingService;

	@Override
	public String getPushCode() {
		return ParkMQConstant.Parking.Code.AUDIT;
	}

	@Override
	public void execute(PushContext<RequestParkingOrderDto> context) {
		RequestParkingOrderDto orderDto = context.getData();
		PushAppParkRelation parkRelation = context.getPushAppParkRelation();

		ChangeOrderInfoRequest request = new ChangeOrderInfoRequest();
		// 车场ID
		request.setParkId(orderDto.getThirdParklotCode());
		// 本地停车记录号
		request.setOrderId(GyzhtcOrderUtils.generateLocalOrderId(orderDto.getId(),orderDto.getEnterTime()));
		// 订单类型:
		request.setOrderType( "exit");

		// 金额转分
		request.setTotalAmount(coverAmount(orderDto.getTotalAmount()));
		request.setDiscountAmount(coverAmount(orderDto.getDiscountAmount()));
		request.setPaidAmount(coverAmount(orderDto.getPaidAmount()));
		request.setUnpayAmount(coverAmount(orderDto.getUnusualAmount()));
		request.setDeductionAmount(coverAmount(orderDto.getDiscountAmount()));
		// 稽核原因
		request.setUpMsg(orderDto.getAuditRemake());
		// 上报时间
		request.setUpString(DateUtil.format(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));

		log.info("推送[贵阳城投黔畅通]车场={},车辆={},稽核修正: {}", parkRelation.getParkName(), orderDto.getPlate(), request);
		boolean flag = guiYangParkingService.changeOrderInfo(request, GyszctParkPushHandler.getProperties(context.getPushAppConfigDomain().getBaseAppConfig()));
		if (flag) {
			log.info("推送[贵阳城投黔]稽核修正成功: {}", orderDto.getPlate());
		} else {
			log.error("推送[贵阳城投黔畅通]稽核修正失败: {}", orderDto.getPlate());
		}
	}

	private String coverAmount(java.math.BigDecimal amount) {
		if (amount == null) return "0";
		return amount.multiply(new java.math.BigDecimal(100)).setScale(0, java.math.RoundingMode.HALF_UP).toString();
	}
}
