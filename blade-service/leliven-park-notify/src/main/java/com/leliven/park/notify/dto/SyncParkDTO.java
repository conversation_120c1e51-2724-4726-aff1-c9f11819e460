package com.leliven.park.notify.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 同步车场请求对象
 */
@Data
@ApiModel(value = "SyncParkDTO对象", description = "同步车场请求对象")
public class SyncParkDTO {

    /**
     * 车场id
     */
    @NotNull(message = "车场id不能为空")
    @ApiModelProperty(value = "车场id", required = true)
    private Long parkId;

    /**
     * 公司id
     */
    @NotNull(message = "公司id不能为空")
    @ApiModelProperty(value = "公司id", required = true)
    private Long companyId;
}
