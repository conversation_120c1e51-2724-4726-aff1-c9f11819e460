package com.leliven.park.notify.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 配置车场关联
 */
@Data
@TableName("push_app_park_relation")
@ApiModel(value = "推送配置车场关联", description = "配置车场关联表")
@EqualsAndHashCode(callSuper = true)
public class PushAppParkRelation extends BaseEntity {

	/**
	 * 车场id
	 */
	@ApiModelProperty(value = "车场id")
	private Long parklotId;

	/**
	 * 第三方车场编码
	 */
	@ApiModelProperty(value = "第三方车场编码")
	private String thirdParklotCode;

	/**
	 * 车场名称
	 */
	@ApiModelProperty(value = "车场名称")
	private String parkName;

	/**
	 * 关联配置id
	 */
	@ApiModelProperty(value = "关联配置id")
	private Long configId;

	/**
	 * 扩展字段
	 */
	@ApiModelProperty(value = "扩展字段")
	private String ext;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
