package com.leliven.park.notify.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;


@Data
@TableName("push_app_config")
@ApiModel(value = "推送应用配置", description = "推送应用配置表")
@EqualsAndHashCode(callSuper = true)
public class PushAppConfig extends BaseEntity {
	@ApiModelProperty(value = "应用名称")
	private String name;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("业务状态：0-禁用，1-启用")
	private Integer status;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "推送数据编码白名单")
	private String codeJson;

}
