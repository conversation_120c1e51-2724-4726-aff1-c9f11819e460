package com.leliven.park.notify.handler.gyszct;

import com.lecent.park.common.constant.ParkMQConstant;
import com.lecent.park.core.thirdparty.guiyangparking.dto.CouponVerificationDetailRequestDTO;
import com.lecent.park.core.thirdparty.guiyangparking.service.IGuiYangParkingService;
import com.lecent.park.core.thirdparty.guiyangparking.util.GyzhtcOrderUtils;
import com.lecent.park.dto.CouponVerifySuccessEventDTO;
import com.lecent.park.en.coupon.CouponTypeEnum;
import com.lecent.park.entity.ParkingOrder;
import com.leliven.park.notify.domain.PushContext;
import com.leliven.park.notify.domain.dto.RequestCouponVerifyDto;
import com.leliven.park.notify.entity.PushAppParkRelation;
import com.leliven.park.notify.handler.gyszct.base.GyszctBasePushHandler;
import com.leliven.park.notify.service.PushAppParkRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 贵阳城投黔畅通优惠券核销推送处理器
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Component
public class GyszctParkPushCouponVerifyHandler extends GyszctBasePushHandler<RequestCouponVerifyDto> {

	@Autowired
	private IGuiYangParkingService guiYangParkingService;
	@Autowired
	private  PushAppParkRelationService pushAppParkRelationService;

	@Override
	public String getPushCode() {
		return ParkMQConstant.Parking.Code.COUPON_VERIFY;
	}

	@Override
	public void execute(PushContext<RequestCouponVerifyDto> context) {



		RequestCouponVerifyDto requestDto = context.getData();
		CouponVerifySuccessEventDTO couponData = requestDto.getCouponData();

		PushAppParkRelation pushAppParkRelation= pushAppParkRelationService.getByParklotIdAndCompanyId(couponData.getParkingOrder().getParklotId(),
			context.getPushAppConfigDomain().getBaseAppConfig().getId());
		if(pushAppParkRelation ==null){
			log.error("第三方车场信息未同步");
			return;
		}

		CouponVerificationDetailRequestDTO request = new CouponVerificationDetailRequestDTO();

		// 平台下发的车场ID
		request.setParkId(pushAppParkRelation.getThirdParklotCode());

		// 总折扣金额（单位分）
		request.setTotalDiscountAmount(coverAmount(couponData.getDiscountAmount()));

		// 上报时间
		request.setUpString(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

		// 优惠券列表（构建优惠券项）
		List<CouponVerificationDetailRequestDTO.CouponVerificationItemDTO> couponItems = new ArrayList<>();
		couponItems.add(buildCouponItem(couponData));
		request.setCouponItems(couponItems);

		log.info("推送[贵阳城投黔畅通]车场={},优惠券={},核销记录: {}",
			pushAppParkRelation.getParkName(), couponData.getCouponName(), request);

		boolean flag = guiYangParkingService.couponVerificationDetail(request,
			GyszctParkPushHandler.getProperties(context.getPushAppConfigDomain().getBaseAppConfig()));

		if (flag) {
			log.info("推送[贵阳城投黔畅通]优惠券核销记录成功: {}", couponData.getCouponName());
		} else {
			log.error("推送[贵阳城投黔畅通]优惠券核销记录失败: {}", couponData.getCouponName());
		}
	}

	/**
	 * 金额转换为分
	 */
	private String coverAmount(BigDecimal amount) {
		if (amount == null) return "0";
		return amount.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toString();
	}

	/**
	 * 构建优惠券项
	 */
	private CouponVerificationDetailRequestDTO.CouponVerificationItemDTO buildCouponItem(CouponVerifySuccessEventDTO couponData) {
		CouponVerificationDetailRequestDTO.CouponVerificationItemDTO item = new CouponVerificationDetailRequestDTO.CouponVerificationItemDTO();

		// 车牌号（从优惠券数据中获取）
		item.setPlate(couponData.getPlate());

		// 与平台下发的优惠券ID一致
		item.setCouponId(couponData.getThirdCouponId());

		// 实际优惠金额（单位分）
		item.setCurrDiscountPrice(coverAmount(couponData.getDiscountAmount()));
		ParkingOrder parkingOrder = couponData.getParkingOrder();

		// 实际优惠时间（单位分钟）- 根据优惠券类型设置
		if (couponData.getCouponType() != null && couponData.getCouponType() == CouponTypeEnum.TIME_LENGTH.getValue()) {
			// 时长券，需要计算实际优惠时长
			BigDecimal discountTime = calculateActualDiscountTime(couponData, parkingOrder);
			item.setCurrDiscountTime(discountTime.toString());
		} else {
			item.setCurrDiscountTime("0");
		}

		// 核销时间
		item.setUseTime(couponData.getUseTime() != null ?
			DateUtil.format(couponData.getUseTime(), "yyyy-MM-dd HH:mm:ss") :
			DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

		// 交易流水号
		item.setTradeNo(String.valueOf(couponData.getTempOrderId()));

		if(parkingOrder != null){
			// 本地停车记录号
			item.setLocalOrderId(GyzhtcOrderUtils.generateLocalOrderId(couponData.getParkingId(), parkingOrder.getEnterTime()));
		}
		return item;
	}

	/**
	 * 计算实际优惠时长（单位分钟）
	 * 如果停车时长小于优惠时长，返回停车时长；否则返回优惠时长
	 *
	 * @param couponData 优惠券数据
	 * @param parkingOrder 停车记录
	 * @return 实际优惠时长（分钟）
	 */
	private BigDecimal calculateActualDiscountTime(CouponVerifySuccessEventDTO couponData, ParkingOrder parkingOrder) {
		// 优惠券的优惠时长（
		BigDecimal couponDiscount= couponData.getDiscountValue();
		if (couponDiscount == null || couponDiscount.compareTo(BigDecimal.ZERO) <= 0) {
			return BigDecimal.ZERO;
		}

		// 转换为分钟
		BigDecimal couponDiscountMinutes = couponDiscount;

		// 如果没有停车记录，直接返回优惠时长
		if (parkingOrder == null) {
			return couponDiscountMinutes;
		}

		// 计算停车时长（分钟）
		BigDecimal parkingDurationMinutes = calculateParkingDurationMinutes(parkingOrder);

		// 如果停车时长小于优惠时长，返回停车时长；否则返回优惠时长
		if (parkingDurationMinutes.compareTo(couponDiscountMinutes) < 0) {
			log.info("停车时长[{}分钟]小于优惠时长[{}分钟]，返回停车时长", parkingDurationMinutes, couponDiscountMinutes);
			return parkingDurationMinutes;
		} else {
			log.info("停车时长[{}分钟]大于等于优惠时长[{}分钟]，返回优惠时长", parkingDurationMinutes, couponDiscountMinutes);
			return couponDiscountMinutes;
		}
	}

	/**
	 * 计算停车时长（分钟）
	 *
	 * @param parkingOrder 停车记录
	 * @return 停车时长（分钟）
	 */
	private BigDecimal calculateParkingDurationMinutes(ParkingOrder parkingOrder) {
		if (parkingOrder == null || parkingOrder.getEnterTime() == null) {
			return BigDecimal.ZERO;
		}

		Date endTime = parkingOrder.getExitTime();
		if (endTime == null) {
			// 如果还没有出场时间，使用当前时间
			endTime = new Date();
		}

		// 计算时间差（毫秒）
		long durationMillis = endTime.getTime() - parkingOrder.getEnterTime().getTime();

		// 转换为分钟
		BigDecimal durationMinutes = BigDecimal.valueOf(durationMillis)
			.divide(BigDecimal.valueOf(60 * 1000), 2, BigDecimal.ROUND_HALF_UP);

		log.debug("停车记录[{}]停车时长：{}分钟，入场时间：{}，出场时间：{}",
			parkingOrder.getId(), durationMinutes,
			DateUtil.format(parkingOrder.getEnterTime(), "yyyy-MM-dd HH:mm:ss"),
			DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));

		return durationMinutes;
	}
}
