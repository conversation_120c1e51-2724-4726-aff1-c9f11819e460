-- ===================================================================
-- 业务参数值表
-- 作用：存储具体的参数值，通过param_key关联元数据
-- ===================================================================

DROP TABLE IF EXISTS `blade_business_param_value`;

CREATE TABLE `blade_business_param_value` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `tenant_id` varchar(12) NOT NULL COMMENT '租户ID',
  `param_key` varchar(100) NOT NULL COMMENT '参数键',
  `param_value` text COMMENT '参数值',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号（用于乐观锁）',
  `status` int(2) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_user` bigint(64) NOT NULL COMMENT '创建人',
  `create_dept` bigint(20) NULL COMMENT '创建部门',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user` bigint(64) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_tenant_param_key` (`tenant_id`, `param_key`) COMMENT '租户参数键唯一约束',
  KEY `idx_param_key` (`param_key`) COMMENT '参数键索引',
  KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务参数值表';

-- 初始化数据：平台级默认参数值（租户ID为000000表示平台级）
INSERT INTO `blade_business_param_value` 
(`id`, `tenant_id`, `param_key`, `param_value`, `version`, `status`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES 
(1, '000000', 'collection.timeout', '30', 1, 1, 1, NOW(), 1, NOW()),
(2, '000000', 'entry.auto_open_gate', 'true', 1, 1, 1, NOW(), 1, NOW()),
(3, '000000', 'discount.max_amount', '100.00', 1, 1, 1, NOW(), 1, NOW()),
(4, '000000', 'device.heartbeat_interval', '60', 1, 1, 1, NOW(), 1, NOW()),
(5, '000000', 'invoice.auto_issue', 'false', 1, 1, 1, NOW(), 1, NOW()); 
