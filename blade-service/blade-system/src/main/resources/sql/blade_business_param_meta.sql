-- ===================================================================
-- 业务参数元数据表
-- 作用：定义参数的结构、类型、验证规则等元信息
-- ===================================================================

DROP TABLE IF EXISTS `blade_business_param_meta`;

CREATE TABLE `blade_business_param_meta` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `business_category` varchar(50) NOT NULL COMMENT '业务分类',
  `param_key` varchar(100) NOT NULL COMMENT '参数键（全局唯一）',
  `param_name` varchar(200) NOT NULL COMMENT '参数名称',
  `param_type` varchar(20) NOT NULL DEFAULT 'STRING' COMMENT '参数类型：STRING、JSON、NUMBER、BOOLEAN',
  `param_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '参数级别（1-平台级， 2-租户级）',
  `default_value` text COMMENT '默认值',
  `validation_rule` text COMMENT '验证规则（JSON格式）',
  `description` varchar(500) COMMENT '参数描述',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0-否，1-是',
  `display_order` int(11) DEFAULT 0 COMMENT '显示顺序',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `status` int(2) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_user` bigint(64) NULL COMMENT '创建人',
  `create_dept` bigint(20) NULL COMMENT '创建部门',
  `create_time` datetime NULL COMMENT '创建时间',
  `update_user` bigint(64) NULL COMMENT '修改人',
  `update_time` datetime NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_param_key` (`param_key`) COMMENT '参数键全局唯一约束',
  KEY `idx_business_category` (`business_category`) COMMENT '业务分类索引',
  KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务参数元数据表';

-- 初始化数据：业务分类枚举数据
INSERT INTO `blade_business_param_meta` 
(`id`, `business_category`, `param_key`, `param_name`, `param_type`, `default_value`, `validation_rule`, `description`, `is_required`, `display_order`, `version`, `status`, `create_user`, `create_time`, `update_user`, `update_time`) 
VALUES 
(1, 'COLLECTION_BUSINESS', 'collection.timeout', '追缴超时时间', 'NUMBER', '30', '{"min":1,"max":365}', '追缴业务超时时间（天）', 1, 1, 1, 1, 1, NOW(), 1, NOW()),
(2, 'ENTRY_BUSINESS', 'entry.auto_open_gate', '自动开闸', 'BOOLEAN', 'true', '{}', '进场时是否自动开闸', 1, 1, 1, 1, 1, NOW(), 1, NOW()),
(3, 'DISCOUNT_BUSINESS', 'discount.max_amount', '最大优惠金额', 'NUMBER', '100.00', '{"min":0,"max":9999.99}', '单次最大优惠金额（元）', 1, 1, 1, 1, 1, NOW(), 1, NOW()),
(4, 'DEVICE_BUSINESS', 'device.heartbeat_interval', '设备心跳间隔', 'NUMBER', '60', '{"min":10,"max":300}', '设备心跳间隔时间（秒）', 1, 1, 1, 1, 1, NOW(), 1, NOW()),
(5, 'INVOICE_BUSINESS', 'invoice.auto_issue', '自动开票', 'BOOLEAN', 'false', '{}', '是否自动开具发票', 0, 1, 1, 1, 1, NOW(), 1, NOW()); 
