package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.ddd.core.event.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.dto.query.BusinessParamMetaQuery;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.enums.BusinessParamLevel;
import org.springblade.system.event.BusinessParamMateCreatedEvent;
import org.springblade.system.mapper.BusinessParamMetaMapper;
import org.springblade.system.service.IBusinessParamCategoryService;
import org.springblade.system.service.IBusinessParamMetaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务参数元数据服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessParamMetaServiceImpl extends BaseServiceImpl<BusinessParamMetaMapper, BusinessParamMeta> implements IBusinessParamMetaService {

	private final DomainEventPublisher domainEventPublisher;
	private final IBusinessParamCategoryService categoryService;

    @Override
    public IPage<BusinessParamMeta> metaPage(BusinessParamMetaQuery query) {
        return baseMapper.selectBusinessParamMetaPage(Condition.getPage(query), query);
    }

    @Override
    public List<BusinessParamMeta> metes(BusinessParamMetaQuery query) {
        return new ArrayList<>();
    }

    @Override
    public BusinessParamMeta detail(Long id) {
        return getById(id);
    }

    @Override
    public List<BusinessParamMeta> selectByCategoryId(Long categoryId, Integer status, boolean includeChildren) {
        if (categoryId == null) {
            return Collections.emptyList();
        }

        if (includeChildren) {
            // 获取所有子分类ID（包含自身）
            List<Long> categoryIds = categoryService.getDescendantIds(categoryId);
            if (categoryIds.isEmpty()) {
                return Collections.emptyList();
            }
            return baseMapper.selectByCategoryIds(categoryIds, status);
        } else {
            return baseMapper.selectByCategoryId(categoryId, status);
        }
    }

    @Override
    public List<BusinessParamMeta> selectByCategoryIds(List<Long> categoryIds, Integer status) {
        if (Func.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByCategoryIds(categoryIds, status);
    }

    @Override
    public List<BusinessParamMeta> listByParamLevel(BusinessParamLevel paramLevel) {
        return Optional.ofNullable(paramLevel)
            .map(t -> this.lambdaQuery().eq(BusinessParamMeta::getParamLevel, paramLevel.getCode()).list())
            .orElseGet(Collections::emptyList);
    }

    @Override
    public BusinessParamMeta getByParamKey(String paramKey) {
        return baseMapper.selectByParamKey(paramKey);
    }

	@Override
	public Optional<BusinessParamMeta> getOptionalByParamKey(String paramKey) {
		if (Func.isBlank(paramKey)) {
			throw new ServiceException("参数[参数键]不能为空");
		}

		return Optional.ofNullable(baseMapper.selectByParamKey(paramKey));
	}

	@Override
	public BusinessParamMeta getOrElseThrow(String paramKey) {
		return getOptionalByParamKey(paramKey)
			.orElseThrow(() -> new ServiceException("参数元数据不存在, 参数键: " + paramKey));
	}

	@Override
    public List<BusinessParamMeta> selectByParamKeys(List<String> paramKeys, Integer status) {
        if (Func.isEmpty(paramKeys)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByParamKeys(paramKeys, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(BusinessParamMeta businessParamMeta) {
        // 检查参数键是否已存在
        verifyParamKeyShouldNotExists(businessParamMeta.getParamKey(), null);

        boolean saved = save(businessParamMeta);

        if (saved) {
			domainEventPublisher.publishEvent(BusinessParamMateCreatedEvent.of(businessParamMeta));
        }

        return saved;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean edit(BusinessParamMeta businessParamMeta) {
        // 检查参数键是否已存在
        verifyParamKeyShouldNotExists(businessParamMeta.getParamKey(), businessParamMeta.getId());

        return updateById(businessParamMeta);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteParamMeta(Long id) {
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteParamMetaBatch(List<Long> ids) {
        if (Func.isEmpty(ids)) {
            return false;
        }
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(Long id, Integer status) {
        BusinessParamMeta businessParamMeta = new BusinessParamMeta();
        businessParamMeta.setId(id);
        businessParamMeta.setStatus(status);
        businessParamMeta.setUpdateTime(new Date());

        return updateById(businessParamMeta);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatusBatch(List<Long> ids, Integer status) {
        if (Func.isEmpty(ids)) {
            return false;
        }

        List<BusinessParamMeta> businessParamMetas = ids.stream().map(id -> {
            BusinessParamMeta businessParamMeta = new BusinessParamMeta();
            businessParamMeta.setId(id);
            businessParamMeta.setStatus(status);
            return businessParamMeta;
        }).collect(Collectors.toList());

        return super.updateBatchById(businessParamMetas);
    }

    /**
     * 验证参数键应该是不存在的
     *
     * @param paramKey  参数键
     * @param excludeId 排除的ID（用于更新时检查）
     * @throws ServiceException 参数键已存在
     */
    private void verifyParamKeyShouldNotExists(String paramKey, Long excludeId) {
        // 检查参数键是否已存在
        if (isParamKeyExists(paramKey, excludeId)) {
            throw new ServiceException("参数键[" + paramKey +"]已存在");
        }
    }

    /**
     * 检查参数键是否存在
     *
     * @param paramKey  参数键
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在 {@code true} 存在 {@code false} 不存在
     */
    private boolean isParamKeyExists(String paramKey, Long excludeId) {
        if (Func.isEmpty(paramKey)) {
            return false;
        }
        int count = baseMapper.checkParamKeyExists(paramKey, excludeId);
        return count > 0;
    }

}
