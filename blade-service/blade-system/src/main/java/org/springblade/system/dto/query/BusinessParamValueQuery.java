package org.springblade.system.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;

/**
 * 业务参数值查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("业务参数值查询参数")
public class BusinessParamValueQuery extends Query {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "000000")
    private String tenantId;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键", example = "collection.timeout")
    private String paramKey;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称", example = "追缴超时时间")
    private String paramName;

    /**
     * 参数级别
     */
    @ApiModelProperty(value = "参数级别", notes="1-平台级 2-租户级", example = "1")
    private Integer paramLevel;

    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID", example = "1")
    private Long categoryId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "1", allowableValues = "0,1")
    private Integer status;
}
