package org.springblade.system.service;

import org.springblade.system.entity.BusinessParamLog;

import java.util.List;
import java.util.Map;

/**
 * 业务参数统一管理服务类
 * 整合元数据、参数值和变更日志的管理功能
 *
 * <AUTHOR>
 */
public interface IBusinessParamManagerService {

	/**
     * 参数回退操作
     *
     * @param changeLogId  变更日志ID
     * @param changeReason 回退原因
     * @return 回退结果
     */
    boolean rollbackParamChange(Long changeLogId, String changeReason);

    /**
     * 验证参数值
     *
     * @param paramKey   参数键
     * @param paramValue 参数值
     * @return 验证结果
     */
    boolean validateParamValue(String paramKey, String paramValue);

    /**
     * 批量验证参数值
     *
     * @param paramValues 参数值映射
     * @return 验证结果映射（键为paramKey，值为验证结果）
     */
    Map<String, Boolean> batchValidateParamValues(Map<String, String> paramValues);

    /**
     * 获取参数变更历史
     *
     * @param paramKey 参数键
     * @param tenantId 租户ID（可为空）
     * @param limit    限制数量
     * @return 变更历史列表
     */
    List<BusinessParamLog> getParamChangeHistory(String paramKey, String tenantId, Integer limit);

}
