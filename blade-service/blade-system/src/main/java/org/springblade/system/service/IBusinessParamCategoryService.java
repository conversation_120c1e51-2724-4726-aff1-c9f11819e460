package org.springblade.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.system.dto.query.BusinessParamCategoryQuery;
import org.springblade.system.entity.BusinessParamCategory;
import org.springblade.system.vo.BusinessParamCategoryVO;

import java.util.List;

/**
 * 业务参数分类服务接口
 *
 * <AUTHOR>
 */
public interface IBusinessParamCategoryService extends IService<BusinessParamCategory> {

    /**
     * 自定义分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BusinessParamCategory> categoryPage(BusinessParamCategoryQuery query);

    /**
     * 获取分类树
     *
     * @param includeParamCount 是否包含参数数量统计
     * @return 分类树列表
     */
    List<BusinessParamCategoryVO> getCategoryTree(boolean includeParamCount);

    /**
     * 根据分类编码获取分类信息
     *
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    BusinessParamCategory getByCategoryCode(String categoryCode);

    /**
     * 根据分类ID获取所有子分类ID（包含自身）
     *
     * @param categoryId 分类ID
     * @return 子分类ID列表
     */
    List<Long> getDescendantIds(Long categoryId);

    /**
     * 根据分类ID获取所有子分类（包含自身）
     *
     * @param categoryId 分类ID
     * @return 子分类列表
     */
    List<BusinessParamCategory> getDescendants(Long categoryId);

    /**
     * 保存分类（自动维护祖先路径）
     *
     * @param category 分类信息
     * @return 保存结果
     */
    boolean saveCategory(BusinessParamCategory category);

    /**
     * 更新分类（自动维护祖先路径）
     *
     * @param category 分类信息
     * @return 更新结果
     */
    boolean updateCategory(BusinessParamCategory category);

    /**
     * 删除分类（检查是否有参数关联）
     *
     * @param categoryId 分类ID
     * @return 删除结果
     */
    boolean removeCategory(Long categoryId);

    /**
     * 批量删除分类
     *
     * @param categoryIds 分类ID列表
     * @return 删除结果
     */
    boolean removeCategoryBatch(List<Long> categoryIds);

    /**
     * 移动分类到新的父分类下
     *
     * @param categoryId   分类ID
     * @param newParentId  新父分类ID
     * @param newSortOrder 新排序
     * @return 移动结果
     */
    boolean moveCategory(Long categoryId, Long newParentId, Integer newSortOrder);

    /**
     * 批量更新分类状态
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 更新结果
     */
    boolean changeStatusBatch(List<Long> categoryIds, Integer status);

    /**
     * 检查分类编码是否存在
     *
     * @param categoryCode 分类编码
     * @param excludeId    排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean checkCategoryCodeExists(String categoryCode, Long excludeId);

    /**
     * 统计分类下的参数数量
     *
     * @param categoryId      分类ID
     * @param includeChildren 是否包含子分类
     * @return 参数数量
     */
    int countParamsByCategory(Long categoryId, boolean includeChildren);

    /**
     * 获取根分类列表
     *
     * @return 根分类列表
     */
    List<BusinessParamCategory> getRootCategories();

    /**
     * 根据父分类ID获取子分类
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<BusinessParamCategory> getChildrenByParentId(Long parentId);

    /**
     * 构建完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    String buildFullPath(Long categoryId);

    /**
     * 验证分类层级关系
     *
     * @param categoryId 分类ID
     * @param parentId   父分类ID
     * @return 验证结果
     */
    boolean validateCategoryHierarchy(Long categoryId, Long parentId);
}
