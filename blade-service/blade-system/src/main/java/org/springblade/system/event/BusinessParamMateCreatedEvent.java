package org.springblade.system.event;

import com.leliven.ddd.core.annotations.DomainEvent;
import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;
import org.springblade.system.entity.BusinessParamMeta;

import javax.annotation.Nonnull;

/**
 * 业务参数元数据已创建事件
 *
 * <AUTHOR>
 */
@DomainEvent
public class BusinessParamMateCreatedEvent extends AbstractPayloadDomainEvent<BusinessParamMeta> {

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	protected BusinessParamMateCreatedEvent(@Nonnull BusinessParamMeta payload) {
		super(payload);
	}

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	public static BusinessParamMateCreatedEvent of(@Nonnull BusinessParamMeta payload) {
		return new BusinessParamMateCreatedEvent(payload);
	}
}
