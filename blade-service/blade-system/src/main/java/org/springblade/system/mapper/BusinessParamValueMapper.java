package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.dto.BusinessParamValueDTO;
import org.springblade.system.dto.query.BusinessParamValueQuery;
import org.springblade.system.entity.BusinessParamValue;

import java.util.List;
import java.util.Map;

/**
 * 业务参数值 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BusinessParamValueMapper extends BaseMapper<BusinessParamValue> {

    /**
     * 参数值分页查询
     *
     * @param page  分页对象
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BusinessParamValueDTO> selectBusinessParamValuePage(IPage<BusinessParamValueDTO> page,
                                                           @Param("query") BusinessParamValueQuery query);

    /**
     * 根据租户ID和参数键查询参数值
     *
     * @param tenantId 租户ID
     * @param paramKey 参数键
     * @return 参数值
     */
    BusinessParamValue selectByTenantAndParamKey(@Param("tenantId") String tenantId,
                                                 @Param("paramKey") String paramKey);

    /**
     * 批量查询租户参数值
     *
     * @param tenantId  租户ID
     * @param paramKeys 参数键列表
     * @param status    状态
     * @return 参数值列表
     */
    List<BusinessParamValue> selectByTenantAndParamKeys(@Param("tenantId") String tenantId,
                                                        @Param("paramKeys") List<String> paramKeys,
                                                        @Param("status") Integer status);

    /**
     * 查询租户所有参数值
     *
     * @param tenantId 租户ID
     * @param status   状态
     * @return 参数值列表
     */
    List<BusinessParamValue> selectByTenant(@Param("tenantId") String tenantId,
                                            @Param("status") Integer status);

    /**
     * 查询参数值（支持继承机制：租户级 -> 平台级）
     *
     * @param tenantId 租户ID
     * @param paramKey 参数键
     * @return 参数值（租户级优先，不存在则返回平台级）
     */
    BusinessParamValue selectParamValueWithInheritance(@Param("tenantId") String tenantId,
                                                       @Param("paramKey") String paramKey);

    /**
     * 批量查询参数值（支持继承机制）
     *
     * @param tenantId  租户ID
     * @param paramKeys 参数键列表
     * @return 参数值映射（键为paramKey，值为paramValue）
     */
    Map<String, String> selectParamValuesWithInheritance(@Param("tenantId") String tenantId,
                                                         @Param("paramKeys") List<String> paramKeys);

    /**
     * 检查租户参数值是否存在
     *
     * @param tenantId  租户ID
     * @param paramKey  参数键
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在数量
     */
    int checkTenantParamExists(@Param("tenantId") String tenantId,
                              @Param("paramKey") String paramKey,
                              @Param("excludeId") Long excludeId);

    /**
     * 根据参数键查询所有租户的参数值
     *
     * @param paramKey 参数键
     * @param status   状态
     * @return 参数值列表
     */
    List<BusinessParamValue> selectByParamKey(@Param("paramKey") String paramKey,
                                              @Param("status") Integer status);

    /**
     * 统计租户参数数量
     *
     * @param tenantId 租户ID
     * @param status   状态
     * @return 参数数量
     */
    int countByTenant(@Param("tenantId") String tenantId,
                     @Param("status") Integer status);

    /**
     * 批量插入参数值
     *
     * @param paramValues 参数值列表
     * @return 插入数量
     */
    int batchInsert(@Param("paramValues") List<BusinessParamValue> paramValues);

    /**
     * 批量更新参数值
     *
     * @param paramValues 参数值列表
     * @return 更新数量
     */
    int batchUpdate(@Param("paramValues") List<BusinessParamValue> paramValues);

} 
