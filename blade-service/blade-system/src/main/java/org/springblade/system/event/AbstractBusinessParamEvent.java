package org.springblade.system.event;

import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;
import lombok.Getter;
import org.springblade.system.enums.BusinessParamOperationType;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * 业务参数变更事件
 *
 * <AUTHOR>
 */
public class AbstractBusinessParamEvent<T> extends AbstractPayloadDomainEvent<T> {

	/**
	 * 变更类型
	 */
	@Getter
	private final BusinessParamOperationType operationType;

	@Getter
	private final String changeReason;

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	protected AbstractBusinessParamEvent(@Nonnull BusinessParamOperationType operationType,
										 @Nullable String changeReason,
										 @Nonnull T payload) {
		super(payload);
		this.operationType = operationType;
		this.changeReason = changeReason;
	}
}
