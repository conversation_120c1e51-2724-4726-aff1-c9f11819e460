package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.dto.query.BusinessParamCategoryQuery;
import org.springblade.system.entity.BusinessParamCategory;
import org.springblade.system.mapper.BusinessParamCategoryMapper;
import org.springblade.system.service.IBusinessParamCategoryService;
import org.springblade.system.vo.BusinessParamCategoryVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务参数分类服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessParamCategoryServiceImpl extends ServiceImpl<BusinessParamCategoryMapper, BusinessParamCategory> 
        implements IBusinessParamCategoryService {

    @Override
    public IPage<BusinessParamCategory> categoryPage(BusinessParamCategoryQuery query) {
        IPage<BusinessParamCategory> page = Condition.getPage(query);
        return baseMapper.selectBusinessParamCategoryPage(page, query);
    }

    @Override
    public List<BusinessParamCategoryVO> getCategoryTree(boolean includeParamCount) {
        List<BusinessParamCategory> categories;
        
        if (includeParamCount) {
            categories = baseMapper.selectCategoryTreeWithParamCount(1);
        } else {
            categories = list(new LambdaQueryWrapper<BusinessParamCategory>()
                    .eq(BusinessParamCategory::getStatus, 1)
                    .orderByAsc(BusinessParamCategory::getLevel)
                    .orderByAsc(BusinessParamCategory::getSortOrder)
                    .orderByDesc(BusinessParamCategory::getCreateTime));
        }
        
        return buildCategoryTree(categories);
    }

    @Override
    public BusinessParamCategory getByCategoryCode(String categoryCode) {
        if (Func.isEmpty(categoryCode)) {
            return null;
        }
        return baseMapper.selectByCategoryCode(categoryCode);
    }

    @Override
    public List<Long> getDescendantIds(Long categoryId) {
        if (categoryId == null) {
            return Collections.emptyList();
        }
        
        BusinessParamCategory category = getById(categoryId);
        if (category == null) {
            return Collections.emptyList();
        }
        
        List<BusinessParamCategory> descendants = baseMapper.selectDescendants(category.getAncestorPath(), 1);
        List<Long> ids = descendants.stream()
                .map(BusinessParamCategory::getId)
                .collect(Collectors.toList());
        
        // 包含自身
        ids.add(categoryId);
        return ids;
    }

    @Override
    public List<BusinessParamCategory> getDescendants(Long categoryId) {
        if (categoryId == null) {
            return Collections.emptyList();
        }
        
        BusinessParamCategory category = getById(categoryId);
        if (category == null) {
            return Collections.emptyList();
        }
        
        List<BusinessParamCategory> descendants = baseMapper.selectDescendants(category.getAncestorPath(), 1);
        // 包含自身
        descendants.add(category);
        return descendants;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCategory(BusinessParamCategory category) {
        // 验证分类编码唯一性
        if (checkCategoryCodeExists(category.getCategoryCode(), null)) {
            throw new ServiceException("分类编码已存在");
        }
        
        // 设置层级和祖先路径
        if (category.getParentId() == null) {
            // 根分类
            category.setLevel(1);
            category.setAncestorPath(null); // 保存后会自动设置
        } else {
            // 子分类
            BusinessParamCategory parent = getById(category.getParentId());
            if (parent == null) {
                throw new ServiceException("父分类不存在");
            }
            category.setLevel(parent.getLevel() + 1);
            category.setAncestorPath(null); // 保存后会自动设置
        }
        
        // 设置默认排序
        if (category.getSortOrder() == null) {
            category.setSortOrder(0);
        }
        
        boolean result = save(category);
        
        if (result) {
            // 更新祖先路径
            updateAncestorPath(category);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(BusinessParamCategory category) {
        if (category.getId() == null) {
            throw new ServiceException("分类ID不能为空");
        }
        
        BusinessParamCategory existingCategory = getById(category.getId());
        if (existingCategory == null) {
            throw new ServiceException("分类不存在");
        }
        
        // 验证分类编码唯一性
        if (checkCategoryCodeExists(category.getCategoryCode(), category.getId())) {
            throw new ServiceException("分类编码已存在");
        }
        
        // 如果父分类发生变化，需要重新计算层级和祖先路径
        if (!Objects.equals(existingCategory.getParentId(), category.getParentId())) {
            // 验证层级关系
            if (!validateCategoryHierarchy(category.getId(), category.getParentId())) {
                throw new ServiceException("不能将分类移动到其子分类下");
            }
            
            // 重新计算层级和祖先路径
            if (category.getParentId() == null) {
                category.setLevel(1);
            } else {
                BusinessParamCategory parent = getById(category.getParentId());
                if (parent == null) {
                    throw new ServiceException("父分类不存在");
                }
                category.setLevel(parent.getLevel() + 1);
            }
        }
        
        boolean result = updateById(category);
        
        if (result && !Objects.equals(existingCategory.getParentId(), category.getParentId())) {
            // 更新祖先路径
            updateAncestorPath(category);
            // 更新所有子分类的祖先路径
            updateDescendantAncestorPaths(category);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCategory(Long categoryId) {
        if (categoryId == null) {
            return false;
        }
        
        BusinessParamCategory category = getById(categoryId);
        if (category == null) {
            return false;
        }
        
        // 检查是否有子分类
        List<BusinessParamCategory> children = baseMapper.selectByParentId(categoryId, null);
        if (!children.isEmpty()) {
            throw new ServiceException("存在子分类，无法删除");
        }
        
        // 检查是否有关联的参数
        int paramCount = baseMapper.countParamsByCategory(categoryId);
        if (paramCount > 0) {
            throw new ServiceException("分类下存在参数，无法删除");
        }
        
        return removeById(categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCategoryBatch(List<Long> categoryIds) {
        if (Func.isEmpty(categoryIds)) {
            return false;
        }
        
        for (Long categoryId : categoryIds) {
            if (!removeCategory(categoryId)) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveCategory(Long categoryId, Long newParentId, Integer newSortOrder) {
        if (categoryId == null) {
            return false;
        }
        
        BusinessParamCategory category = getById(categoryId);
        if (category == null) {
            return false;
        }
        
        // 验证层级关系
        if (!validateCategoryHierarchy(categoryId, newParentId)) {
            throw new ServiceException("不能将分类移动到其子分类下");
        }
        
        category.setParentId(newParentId);
        if (newSortOrder != null) {
            category.setSortOrder(newSortOrder);
        }
        
        return updateCategory(category);
    }

    @Override
    public boolean changeStatusBatch(List<Long> categoryIds, Integer status) {
        if (Func.isEmpty(categoryIds)) {
            return false;
        }
        
        return baseMapper.batchUpdateStatus(categoryIds, status) > 0;
    }

    @Override
    public boolean checkCategoryCodeExists(String categoryCode, Long excludeId) {
        if (Func.isEmpty(categoryCode)) {
            return false;
        }
        
        return baseMapper.checkCategoryCodeExists(categoryCode, excludeId) > 0;
    }

    @Override
    public int countParamsByCategory(Long categoryId, boolean includeChildren) {
        if (categoryId == null) {
            return 0;
        }
        
        if (includeChildren) {
            BusinessParamCategory category = getById(categoryId);
            if (category == null) {
                return 0;
            }
            return baseMapper.countParamsByCategoryTree(category.getAncestorPath());
        } else {
            return baseMapper.countParamsByCategory(categoryId);
        }
    }

    @Override
    public List<BusinessParamCategory> getRootCategories() {
        return baseMapper.selectRootCategories(1);
    }

    @Override
    public List<BusinessParamCategory> getChildrenByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId, 1);
    }

    @Override
    public String buildFullPath(Long categoryId) {
        if (categoryId == null) {
            return "";
        }

        BusinessParamCategory category = getById(categoryId);
        if (category == null) {
            return "";
        }

        List<String> pathNames = new ArrayList<>();
        pathNames.add(category.getCategoryName());

        // 根据祖先路径构建完整路径
        if (Func.isNotEmpty(category.getAncestorPath())) {
            List<Long> ancestorIds = category.getAncestorIds();
            if (!ancestorIds.isEmpty()) {
                // 移除自身ID
                ancestorIds.remove(categoryId);

                if (!ancestorIds.isEmpty()) {
                    List<BusinessParamCategory> ancestors = baseMapper.selectByCategoryIds(ancestorIds, null);
                    Map<Long, String> ancestorNameMap = ancestors.stream()
                            .collect(Collectors.toMap(BusinessParamCategory::getId, BusinessParamCategory::getCategoryName));

                    // 按照祖先路径顺序构建路径
                    for (Long ancestorId : ancestorIds) {
                        String ancestorName = ancestorNameMap.get(ancestorId);
                        if (ancestorName != null) {
                            pathNames.add(0, ancestorName);
                        }
                    }
                }
            }
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < pathNames.size(); i++) {
            if (i > 0) {
                sb.append(" / ");
            }
            sb.append(pathNames.get(i));
        }
        return sb.toString();
    }

    @Override
    public boolean validateCategoryHierarchy(Long categoryId, Long parentId) {
        if (categoryId == null || parentId == null) {
            return true;
        }

        // 不能将分类设置为自己的父分类
        if (categoryId.equals(parentId)) {
            return false;
        }

        // 检查parentId是否是categoryId的子分类
        List<Long> descendantIds = getDescendantIds(categoryId);
        return !descendantIds.contains(parentId);
    }

    // ==================== 私有方法 ====================

    /**
     * 构建分类树
     */
    private List<BusinessParamCategoryVO> buildCategoryTree(List<BusinessParamCategory> categories) {
        if (Func.isEmpty(categories)) {
            return Collections.emptyList();
        }

        // 转换为VO并建立映射
        Map<Long, BusinessParamCategoryVO> categoryMap = categories.stream()
                .map(BusinessParamCategoryVO::fromEntity)
                .collect(Collectors.toMap(BusinessParamCategoryVO::getId, Function.identity()));

        // 构建树形结构
        List<BusinessParamCategoryVO> rootCategories = new ArrayList<>();

        for (BusinessParamCategoryVO category : categoryMap.values()) {
            if (category.getParentId() == null) {
                // 根分类
                rootCategories.add(category);
            } else {
                // 子分类
                BusinessParamCategoryVO parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(category);
                }
            }
        }

        // 对每个层级进行排序
        sortCategoryTree(rootCategories);

        return rootCategories;
    }

    /**
     * 对分类树进行排序
     */
    private void sortCategoryTree(List<BusinessParamCategoryVO> categories) {
        if (Func.isEmpty(categories)) {
            return;
        }

        categories.sort((c1, c2) -> {
            int sortOrderCompare = Integer.compare(
                    c1.getSortOrder() != null ? c1.getSortOrder() : 0,
                    c2.getSortOrder() != null ? c2.getSortOrder() : 0
            );
            if (sortOrderCompare != 0) {
                return sortOrderCompare;
            }
            return c1.getCreateTime().compareTo(c2.getCreateTime());
        });

        // 递归排序子分类
        for (BusinessParamCategoryVO category : categories) {
            if (category.getChildren() != null) {
                sortCategoryTree(category.getChildren());
            }
        }
    }

    /**
     * 更新祖先路径
     */
    private void updateAncestorPath(BusinessParamCategory category) {
        String ancestorPath;

        if (category.getParentId() == null) {
            // 根分类
            ancestorPath = category.getId().toString();
        } else {
            // 子分类
            BusinessParamCategory parent = getById(category.getParentId());
            if (parent != null) {
                ancestorPath = BusinessParamCategory.buildAncestorPath(parent.getAncestorPath(), category.getId());
            } else {
                ancestorPath = category.getId().toString();
            }
        }

        category.setAncestorPath(ancestorPath);
        updateById(category);
    }

    /**
     * 更新所有子分类的祖先路径
     */
    private void updateDescendantAncestorPaths(BusinessParamCategory category) {
        List<BusinessParamCategory> descendants = baseMapper.selectDescendants(category.getAncestorPath(), null);

        for (BusinessParamCategory descendant : descendants) {
            // 重新计算祖先路径
            BusinessParamCategory parent = getById(descendant.getParentId());
            if (parent != null) {
                String newAncestorPath = BusinessParamCategory.buildAncestorPath(parent.getAncestorPath(), descendant.getId());
                descendant.setAncestorPath(newAncestorPath);
                descendant.setLevel(parent.getLevel() + 1);
                updateById(descendant);
            }
        }
    }
}
