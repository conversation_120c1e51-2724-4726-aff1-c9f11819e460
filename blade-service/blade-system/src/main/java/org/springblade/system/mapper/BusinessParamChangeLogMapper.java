package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.entity.BusinessParamLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务参数变更日志 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BusinessParamChangeLogMapper extends BaseMapper<BusinessParamLog> {

    /**
     * 自定义分页查询
     *
     * @param page        分页对象
     * @param logType     日志类型
     * @param tenantId    租户ID
     * @param paramKey    参数键
     * @param changeType  变更类型
     * @param operatorId  操作人ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 分页结果
     */
    IPage<BusinessParamLog> selectBusinessParamChangeLogPage(IPage<BusinessParamLog> page,
															 @Param("logType") String logType,
															 @Param("tenantId") String tenantId,
															 @Param("paramKey") String paramKey,
															 @Param("changeType") String changeType,
															 @Param("operatorId") Long operatorId,
															 @Param("startTime") LocalDateTime startTime,
															 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询参数变更历史
     *
     * @param paramKey  参数键
     * @param tenantId  租户ID（可为空）
     * @param limit     限制数量
     * @return 变更日志列表
     */
    List<BusinessParamLog> selectParamChangeHistory(@Param("paramKey") String paramKey,
													@Param("tenantId") String tenantId,
													@Param("limit") Integer limit);

    /**
     * 查询最近的变更日志
     *
     * @param paramKey  参数键
     * @param tenantId  租户ID（可为空）
     * @return 最近的变更日志
     */
    BusinessParamLog selectLatestChangeLog(@Param("paramKey") String paramKey,
										   @Param("tenantId") String tenantId);

    /**
     * 查询可回退的变更日志
     *
     * @param paramKey  参数键
     * @param tenantId  租户ID（可为空）
     * @param limit     限制数量
     * @return 可回退的变更日志列表
     */
    List<BusinessParamLog> selectRollbackableChanges(@Param("paramKey") String paramKey,
													 @Param("tenantId") String tenantId,
													 @Param("limit") Integer limit);



}
