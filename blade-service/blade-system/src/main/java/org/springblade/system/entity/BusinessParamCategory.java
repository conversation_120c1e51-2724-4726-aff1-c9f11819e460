package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务参数分类实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_business_param_category")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessParamCategory", description = "业务参数分类")
public class BusinessParamCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分类编码（全局唯一）
     */
    @ApiModelProperty(value = "分类编码", required = true, example = "COLLECTION_BUSINESS")
    @NotBlank(message = "分类编码不能为空")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称", required = true, example = "追缴业务类")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String categoryName;

    /**
     * 父分类ID
     */
    @ApiModelProperty(value = "父分类ID", example = "1")
    private Long parentId;

    /**
     * 祖先路径（如：1,2,3）
     */
    @ApiModelProperty(value = "祖先路径", example = "1,2,3")
    @Size(max = 500, message = "祖先路径长度不能超过500个字符")
    private String ancestorPath;

    /**
     * 层级（1为根级别）
     */
    @ApiModelProperty(value = "层级", required = true, example = "1")
    @NotNull(message = "层级不能为空")
    private Integer level;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", example = "1")
    private Integer sortOrder;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标", example = "icon-collection")
    @Size(max = 100, message = "图标长度不能超过100个字符")
    private String icon;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", example = "追缴相关的业务参数分类")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    // ==================== 非数据库字段 ====================

    /**
     * 子分类列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子分类列表")
    private List<BusinessParamCategory> children;

    /**
     * 参数数量
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "参数数量")
    private Integer paramCount;

    /**
     * 父分类名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "父分类名称")
    private String parentName;

    /**
     * 完整路径名称（如：根分类/子分类/孙分类）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "完整路径名称")
    private String fullPath;

    // ==================== 便捷方法 ====================

	/**
     * 获取祖先ID列表
     */
    public List<Long> getAncestorIds() {
        if (ancestorPath == null || ancestorPath.trim().isEmpty()) {
            return new ArrayList<>();
        }
        String[] pathArray = ancestorPath.split(",");
        List<Long> ids = new ArrayList<>();
        for (String idStr : pathArray) {
            ids.add(Long.valueOf(idStr));
        }
        return ids;
    }

    /**
     * 构建祖先路径
     */
    public static String buildAncestorPath(String parentAncestorPath, Long categoryId) {
        if (parentAncestorPath == null || parentAncestorPath.trim().isEmpty()) {
            return categoryId.toString();
        }
        return parentAncestorPath + "," + categoryId;
    }

    /**
     * 创建根分类
     */
    public static BusinessParamCategory createRoot(String categoryCode, String categoryName) {
        BusinessParamCategory category = new BusinessParamCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryName(categoryName);
        category.setParentId(null);
        category.setLevel(1);
        category.setSortOrder(0);
        return category;
    }

    /**
     * 创建子分类
     */
    public static BusinessParamCategory createChild(String categoryCode, String categoryName,
                                                   Long parentId, Integer parentLevel) {
        BusinessParamCategory category = new BusinessParamCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryName(categoryName);
        category.setParentId(parentId);
        category.setLevel(parentLevel + 1);
        category.setSortOrder(0);
        return category;
    }
}
