<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.BusinessParamMetaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessParamMetaResultMap" type="org.springblade.system.entity.BusinessParamMeta">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_name" property="paramName"/>
        <result column="param_type" property="paramType"/>
        <result column="param_level" property="paramLevel"/>
        <result column="default_value" property="defaultValue"/>
        <result column="validation_rule" property="validationRule"/>
        <result column="description" property="description"/>
        <result column="is_required" property="isRequired"/>
        <result column="display_order" property="displayOrder"/>
        <result column="version" property="version"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <!-- 扩展字段 -->
        <result column="category_name" property="categoryName"/>
        <result column="full_category_path" property="fullCategoryPath"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, category_id, param_key, param_name, param_type, param_level, default_value,
        validation_rule, description, is_required, display_order, version, status,
        create_user, create_dept, create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectBusinessParamMetaPage" resultMap="businessParamMetaResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_meta
        <where>
            <if test="query.categoryId != null">
                AND category_id = #{query.categoryId}
            </if>
            <if test="query.paramKey != null and query.paramKey != ''">
                AND param_key LIKE CONCAT('%', #{query.paramKey}, '%')
            </if>
            <if test="query.paramName != null and query.paramName != ''">
                AND param_name LIKE CONCAT('%', #{query.paramName}, '%')
            </if>
            <if test="query.paramLevel != null">
                AND param_level = #{query.paramLevel}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
        </where>
        ORDER BY category_id, display_order, create_time DESC
    </select>

    <!-- 根据分类ID查询参数元数据 -->
    <select id="selectByCategoryId" resultMap="businessParamMetaResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_meta
        WHERE category_id = #{categoryId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY display_order, create_time DESC
    </select>

    <!-- 根据分类ID列表查询参数元数据 -->
    <select id="selectByCategoryIds" resultMap="businessParamMetaResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_meta
        WHERE category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY category_id, display_order, create_time DESC
    </select>

    <!-- 根据参数键查询参数元数据 -->
    <select id="selectByParamKey" resultMap="businessParamMetaResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_meta
        WHERE param_key = #{paramKey}
        LIMIT 1
    </select>

    <!-- 批量查询参数元数据 -->
    <select id="selectByParamKeys" resultMap="businessParamMetaResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_meta
        WHERE param_key IN
        <foreach collection="paramKeys" item="paramKey" open="(" separator="," close=")">
            #{paramKey}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY category_id, display_order
    </select>

    <!-- 检查参数键是否存在 -->
    <select id="checkParamKeyExists" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_meta
        WHERE param_key = #{paramKey}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据分类ID统计参数数量 -->
    <select id="countByCategoryId" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_meta
        WHERE category_id = #{categoryId}
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 根据分类ID列表统计参数数量 -->
    <select id="countByCategoryIds" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_meta
        WHERE category_id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 查询参数元数据（包含分类信息） -->
    <select id="selectBusinessParamMetaPageWithCategory" resultMap="businessParamMetaResultMap">
        SELECT
            pm.<include refid="baseColumnList"/>,
            c.category_name,
            c.category_code as full_category_path
        FROM blade_business_param_meta pm
        LEFT JOIN blade_business_param_category c ON pm.category_id = c.id
        <where>
            <if test="query.categoryId != null">
                AND pm.category_id = #{query.categoryId}
            </if>
            <if test="query.paramKey != null and query.paramKey != ''">
                AND pm.param_key LIKE CONCAT('%', #{query.paramKey}, '%')
            </if>
            <if test="query.paramName != null and query.paramName != ''">
                AND pm.param_name LIKE CONCAT('%', #{query.paramName}, '%')
            </if>
            <if test="query.paramLevel != null">
                AND pm.param_level = #{query.paramLevel}
            </if>
            <if test="query.status != null">
                AND pm.status = #{query.status}
            </if>
        </where>
        ORDER BY pm.category_id, pm.display_order, pm.create_time DESC
    </select>

</mapper> 
