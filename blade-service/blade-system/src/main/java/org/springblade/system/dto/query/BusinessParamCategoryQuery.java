package org.springblade.system.dto.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;

/**
 * 业务参数分类查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("业务参数分类查询参数")
public class BusinessParamCategoryQuery extends Query {

    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码", example = "COLLECTION_BUSINESS")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称", example = "追缴业务类")
    private String categoryName;

    /**
     * 父分类ID
     */
    @ApiModelProperty(value = "父分类ID", example = "1")
    private Long parentId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级", example = "1")
    private Integer level;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "1", allowableValues = "0,1")
    private Integer status;

    /**
     * 是否包含子分类
     */
    @ApiModelProperty(value = "是否包含子分类", example = "true")
    private Boolean includeChildren;

    /**
     * 是否只查询根分类
     */
    @ApiModelProperty(value = "是否只查询根分类", example = "false")
    private Boolean rootOnly;
}
