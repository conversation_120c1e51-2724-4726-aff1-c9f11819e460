<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.BusinessParamValueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessParamValueResultMap" type="org.springblade.system.entity.BusinessParamValue">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_value" property="paramValue"/>
        <result column="version" property="version"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- BusinessParamValueDTO查询映射结果 -->
    <resultMap id="businessParamValueDTOResultMap" type="org.springblade.system.dto.BusinessParamValueDTO">
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="param_name" property="paramName"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_value" property="paramValue"/>
        <result column="param_type" property="paramType"/>
        <result column="param_level" property="paramLevel"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="description" property="description"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, param_key, param_value, version, status,
        create_user, create_time, update_user, update_time
    </sql>

    <!-- 参数值分页查询 -->
    <select id="selectBusinessParamValuePage" resultMap="businessParamValueDTOResultMap">
        SELECT
            v.param_key,
            v.param_value,
            v.version,
            v.tenant_id,
            v.status,
            m.param_name,
            m.param_type,
            m.param_level,
            m.category_id,
            m.description,
            m.display_order,
            c.category_name,
            c.category_code
        FROM blade_business_param_value v
        LEFT JOIN blade_business_param_meta m ON v.param_key = m.param_key
        LEFT JOIN blade_business_param_category c ON m.category_id = c.id
        <where>
            <if test="query.tenantId != null and query.tenantId != ''">
                AND v.tenant_id = #{query.tenantId}
            </if>
            <if test="query.paramKey != null and query.paramKey != ''">
                AND v.param_key LIKE CONCAT('%', #{query.paramKey}, '%')
            </if>
            <if test="query.paramName != null and query.paramName != ''">
                AND m.param_name LIKE CONCAT('%', #{query.paramName}, '%')
            </if>
            <if test="query.paramLevel != null">
                AND m.param_level = #{query.paramLevel}
            </if>
            <if test="query.categoryId != null">
                AND m.category_id = #{query.categoryId}
            </if>
            <if test="query.status != null">
                AND v.status = #{query.status}
            </if>
        </where>
        ORDER BY m.param_level, c.sort_order, m.display_order
    </select>

    <!-- 根据租户ID和参数键查询参数值 -->
    <select id="selectByTenantAndParamKey" resultMap="businessParamValueResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_value
        WHERE tenant_id = #{tenantId} AND param_key = #{paramKey}
        LIMIT 1
    </select>

    <!-- 批量查询租户参数值 -->
    <select id="selectByTenantAndParamKeys" resultMap="businessParamValueResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_value
        WHERE tenant_id = #{tenantId}
        AND param_key IN
        <foreach collection="paramKeys" item="paramKey" open="(" separator="," close=")">
            #{paramKey}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY param_key
    </select>

    <!-- 查询租户所有参数值 -->
    <select id="selectByTenant" resultMap="businessParamValueResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_value
        WHERE tenant_id = #{tenantId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY param_key
    </select>

    <!-- 查询参数值（支持继承机制：租户级 -> 平台级） -->
    <select id="selectParamValueWithInheritance" resultMap="businessParamValueResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_value
        WHERE param_key = #{paramKey}
        AND tenant_id IN (#{tenantId}, '000000')
        ORDER BY CASE WHEN tenant_id = #{tenantId} THEN 1 ELSE 2 END
        LIMIT 1
    </select>

    <!-- 批量查询参数值（支持继承机制） -->
    <select id="selectParamValuesWithInheritance" resultType="map">
        SELECT 
            param_key,
            param_value
        FROM (
            SELECT 
                param_key,
                param_value,
                ROW_NUMBER() OVER (PARTITION BY param_key ORDER BY CASE WHEN tenant_id = #{tenantId} THEN 1 ELSE 2 END) as rn
            FROM blade_business_param_value
            WHERE param_key IN
            <foreach collection="paramKeys" item="paramKey" open="(" separator="," close=")">
                #{paramKey}
            </foreach>
            AND tenant_id IN (#{tenantId}, '000000')
            AND status = 1
        ) t
        WHERE t.rn = 1
    </select>

    <!-- 检查租户参数值是否存在 -->
    <select id="checkTenantParamExists" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_value
        WHERE tenant_id = #{tenantId} AND param_key = #{paramKey}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据参数键查询所有租户的参数值 -->
    <select id="selectByParamKey" resultMap="businessParamValueResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_value
        WHERE param_key = #{paramKey}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY tenant_id
    </select>

    <!-- 统计租户参数数量 -->
    <select id="countByTenant" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_value
        WHERE tenant_id = #{tenantId}
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 批量插入参数值 -->
    <insert id="batchInsert">
        INSERT INTO blade_business_param_value 
        (id, tenant_id, param_key, param_value, version, status, create_user, create_time, update_user, update_time)
        VALUES
        <foreach collection="paramValues" item="item" separator=",">
            (#{item.id}, #{item.tenantId}, #{item.paramKey}, #{item.paramValue}, #{item.version}, 
             #{item.status}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 批量更新参数值 -->
    <update id="batchUpdate">
        <foreach collection="paramValues" item="item" separator=";">
            UPDATE blade_business_param_value
            SET param_value = #{item.paramValue},
                version = #{item.version},
                status = #{item.status},
                update_user = #{item.updateUser},
                update_time = #{item.updateTime}
            WHERE id = #{item.id}
        </foreach>
    </update>

</mapper> 
