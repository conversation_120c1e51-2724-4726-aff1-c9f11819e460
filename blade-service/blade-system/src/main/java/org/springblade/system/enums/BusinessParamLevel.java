package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 业务参数级别
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessParamLevel {

	/**
	 * 平台级参数
	 */
	PLATFORM(1, "平台级"),

	/**
	 * 租户级参数
	 */
	TENANT(2, "租户级");

	private final int code;
	private final String name;

	public static BusinessParamLevel resolve(Integer code) {
		for (BusinessParamLevel value : values()) {
			if (Objects.equals(value.code, code)) {
				return value;
			}
		}

		return null;
	}
}
