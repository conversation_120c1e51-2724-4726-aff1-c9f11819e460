package org.springblade.system.enums;

import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;

import java.util.regex.Pattern;

/**
 * 参数类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessParamType {

    /**
     * 字符串类型
     */
    STRING("STRING", "字符串类型"),

	/**
	 * 数字类型
	 */
	NUMBER("NUMBER", "数字类型"),

	/**
	 * 布尔类型
	 */
	BOOLEAN("BOOLEAN", "布尔类型"),

	/**
     * 数组类型
     */
	ARRAY("ARRAY", "数组类型"),

	/**
	 * JSON类型
	 */
	JSON("JSON", "JSON类型"),

	;

	/**
	 * 数组正则 以逗号分隔的，如：{@code 1,2,3} 或 {@code a,b,c} 等
	 */
	private static final Pattern ARRAY_PATTERN = Pattern.compile("^[^,]+(,[^,]+)*$");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static BusinessParamType resolve(String code) {
        for (BusinessParamType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return resolve(code) != null;
    }

	/**
	 * 传入一个字符串值，校验该字符串值是否为有效的业务参数类型
	 *
	 * @param value 字符串值
	 * @return 是否符合对应的枚举类型格式
	 */
	public boolean isValidValue(String value) {
		if (Func.isBlank(value)) {
			return false;
		}

		switch (this) {
			case STRING:
				// 字符串类型，一般都返回true
				return true;
			case NUMBER:
				// 校验是否为数字
				try {
					NumberUtil.parseNumber(value);
					return true;
				} catch (NumberFormatException e) {
					return false;
				}
			case BOOLEAN:
				// 校验是否为布尔值
				return "true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value);
			case JSON:
				// 校验是否为有效的JSON格式
				try {
					JsonUtil.readTree(value);
					return true;
				} catch (Exception e) {
					return false;
				}
			case ARRAY:
				// 以逗号分隔
				return ARRAY_PATTERN.matcher(value.trim()).matches();
			default:
				return false;
		}
	}

}
