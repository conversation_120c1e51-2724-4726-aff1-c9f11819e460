package org.springblade.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.system.dto.BusinessParamValueDTO;
import org.springblade.system.dto.cmd.BusinessParamValueChangeCommand;
import org.springblade.system.dto.query.BusinessParamCategoryQuery;
import org.springblade.system.dto.query.BusinessParamMetaQuery;
import org.springblade.system.dto.query.BusinessParamValueQuery;
import org.springblade.system.entity.BusinessParamCategory;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.service.IBusinessParamCategoryService;
import org.springblade.system.service.IBusinessParamMetaService;
import org.springblade.system.service.IBusinessParamValueService;
import org.springblade.system.vo.BusinessParamCategoryVO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务参数控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/business-param")
@Api(value = "业务参数", tags = "业务参数接口")
public class BusinessParamController extends BladeController {

    private final IBusinessParamMetaService businessParamMetaService;
    private final IBusinessParamValueService businessParamValueService;
    private final IBusinessParamCategoryService businessParamCategoryService;

    // ==================== 元数据相关接口 ====================

    /**
     * 元数据分页查询
     */
    @GetMapping("/metas/page")
    @ApiOperation(value = "元数据分页查询", notes = "传入businessParamMetaQuery")
    public R<IPage<BusinessParamMeta>> metaPage(BusinessParamMetaQuery businessParamMetaQuery) {
        return R.data(businessParamMetaService.metaPage(businessParamMetaQuery));
    }

    /**
     * 元数据列表查询
     */
    @GetMapping("/metas")
    @ApiOperation(value = "元数据列表查询", notes = "传入查询条件")
    public R<List<BusinessParamMeta>> metas(BusinessParamMetaQuery query) {
        return R.data(businessParamMetaService.metes(query));
    }

    /**
     * 元数据详情
     */
    @GetMapping("/metas/{id}")
    @ApiOperation(value = "元数据详情", notes = "传入businessParamMeta")
    public R<BusinessParamMeta> metaDetail(@ApiParam(value = "元数据ID", required = true) @PathVariable("id") Long id) {
        BusinessParamMeta detail = businessParamMetaService.detail(id);
        return R.data(detail);
    }

    /**
     * 元数据新增
     */
    @PostMapping("/metas")
    @ApiOperation(value = "元数据新增", notes = "传入businessParamMeta")
    public R<Boolean> addMeta(@Valid @RequestBody BusinessParamMeta businessParamMeta) {
        return R.status(businessParamMetaService.add(businessParamMeta));
    }

    /**
     * 元数据修改
     */
    @PutMapping("/metas/{id}")
    @ApiOperation(value = "元数据修改", notes = "传入businessParamMeta")
    public R<Boolean> editMeta(@PathVariable("id") Long id, @Valid @RequestBody BusinessParamMeta businessParamMeta) {
        return R.status(businessParamMetaService.edit(businessParamMeta));
    }

    /**
     * 元数据批量启用/禁用
     */
    @PostMapping("/metas/change-status")
    @ApiOperation(value = "元数据批量启用/禁用", notes = "传入ids和status")
    public R<Boolean> changeMetaStatusBatch(
        @ApiParam(value = "主键集合", required = true) @RequestParam List<Long> ids,
        @ApiParam(value = "状态", required = true) @RequestParam Integer status) {
        return R.status(businessParamMetaService.changeStatusBatch(ids, status));
    }

    // ==================== 业务参数值相关接口 ====================

    /**
     * 参数值分页查询
     */
    @GetMapping("/values/page")
    @ApiOperation(value = "参数值分页查询", notes = "传入businessParamValueQuery")
    public R<IPage<BusinessParamValueDTO>> valuePage(BusinessParamValueQuery businessParamValueQuery) {
        return R.data(businessParamValueService.valuePage(businessParamValueQuery));
    }

    /**
     * 参数值列表查询
     */
    @GetMapping("/values")
    @ApiOperation(value = "参数值列表查询", notes = "传入查询条件")
    public R<List<BusinessParamValue>> values(BusinessParamValueQuery query) {
        return R.data(businessParamValueService.values(query));
    }

    /**
     * 参数值详情
     */
    @GetMapping("/values/{id}")
    @ApiOperation(value = "参数值详情", notes = "传入参数值ID")
    public R<BusinessParamValue> valueDetail(@ApiParam(value = "参数值ID", required = true) @PathVariable("id") Long id) {
        BusinessParamValue detail = businessParamValueService.detail(id);
        return R.data(detail);
    }

    /**
     * 参数值修改
     */
    @PutMapping("/values")
    @ApiOperation(value = "参数值修改", notes = "传入businessParamValue")
    public R<Boolean> updateValue(@Valid @RequestBody BusinessParamValueChangeCommand changeCommand) {
        return R.status(businessParamValueService.changeParamValue(changeCommand));
    }

	/**
	 * 参数值重置
	 */
	@PostMapping("/values/{id}/reset")
	@ApiOperation(value = "参数值修改", notes = "传入businessParamValue")
	public R<Boolean> resetValue(@PathVariable("id") Long id) {
		return R.status(businessParamValueService.resetParamValue(id));
	}

    /**
     * 参数值批量启用/禁用
     */
    @PostMapping("/values/change-status")
    @ApiOperation(value = "参数值批量启用/禁用", notes = "传入ids和status")
    public R<Boolean> changeValueStatusBatch(
        @ApiParam(value = "主键集合", required = true) @RequestParam List<Long> ids,
        @ApiParam(value = "状态", required = true) @RequestParam Integer status) {
        return R.status(businessParamValueService.changeStatusBatch(ids, status));
    }

    // ==================== 分类管理相关接口 ====================

    /**
     * 分类树形结构查询
     */
    @GetMapping("/categories/tree")
    @ApiOperation(value = "获取分类树", notes = "获取完整的分类树形结构")
    public R<List<BusinessParamCategoryVO>> getCategoryTree(
            @ApiParam(value = "是否包含参数数量统计", defaultValue = "false") @RequestParam(defaultValue = "false") boolean includeParamCount) {
        return R.data(businessParamCategoryService.getCategoryTree(includeParamCount));
    }

    /**
     * 分类分页查询
     */
    @GetMapping("/categories/page")
    @ApiOperation(value = "分类分页查询", notes = "传入businessParamCategoryQuery")
    public R<IPage<BusinessParamCategory>> categoryPage(BusinessParamCategoryQuery businessParamCategoryQuery) {
        return R.data(businessParamCategoryService.categoryPage(businessParamCategoryQuery));
    }

    /**
     * 分类详情
     */
    @GetMapping("/categories/{id}")
    @ApiOperation(value = "分类详情", notes = "传入分类ID")
    public R<BusinessParamCategory> categoryDetail(@ApiParam(value = "分类ID", required = true) @PathVariable Long id) {
        BusinessParamCategory detail = businessParamCategoryService.getById(id);
        return R.data(detail);
    }

    /**
     * 新增分类
     */
    @PostMapping("/categories")
    @ApiOperation(value = "新增分类", notes = "传入businessParamCategory")
    public R<Boolean> saveCategory(@Valid @RequestBody BusinessParamCategory businessParamCategory) {
        return R.status(businessParamCategoryService.saveCategory(businessParamCategory));
    }

    /**
     * 修改分类
     */
    @PutMapping("/categories/{id}")
    @ApiOperation(value = "修改分类", notes = "传入businessParamCategory")
    public R<Boolean> updateCategory(@PathVariable Long id, @Valid @RequestBody BusinessParamCategory businessParamCategory) {
        businessParamCategory.setId(id);
        return R.status(businessParamCategoryService.updateCategory(businessParamCategory));
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    @ApiOperation(value = "删除分类", notes = "传入分类ID")
    public R<Boolean> removeCategory(@ApiParam(value = "分类ID", required = true) @PathVariable Long id) {
        return R.status(businessParamCategoryService.removeCategory(id));
    }

    /**
     * 批量删除分类
     */
    @DeleteMapping("/categories")
    @ApiOperation(value = "批量删除分类", notes = "传入分类ID集合")
    public R<Boolean> removeCategoryBatch(@ApiParam(value = "分类ID集合", required = true) @RequestParam List<Long> ids) {
        return R.status(businessParamCategoryService.removeCategoryBatch(ids));
    }

    /**
     * 分类批量启用/禁用
     */
    @PostMapping("/categories/change-status")
    @ApiOperation(value = "分类批量启用/禁用", notes = "传入ids和status")
    public R<Boolean> changeCategoryStatusBatch(
            @ApiParam(value = "分类ID集合", required = true) @RequestParam List<Long> ids,
            @ApiParam(value = "状态", required = true) @RequestParam Integer status) {
        return R.status(businessParamCategoryService.changeStatusBatch(ids, status));
    }

    /**
     * 移动分类
     */
    @PostMapping("/categories/{id}/move")
    @ApiOperation(value = "移动分类", notes = "移动分类到新的父分类下")
    public R<Boolean> moveCategory(
            @ApiParam(value = "分类ID", required = true) @PathVariable Long id,
            @ApiParam(value = "新父分类ID") @RequestParam(required = false) Long newParentId,
            @ApiParam(value = "新排序") @RequestParam(required = false) Integer newSortOrder) {
        return R.status(businessParamCategoryService.moveCategory(id, newParentId, newSortOrder));
    }

    /**
     * 根据分类查询参数
     */
    @GetMapping("/categories/{categoryId}/params")
    @ApiOperation(value = "根据分类查询参数", notes = "查询指定分类下的所有参数")
    public R<List<BusinessParamMeta>> getParamsByCategory(
            @ApiParam(value = "分类ID", required = true) @PathVariable Long categoryId,
            @ApiParam(value = "是否包含子分类", defaultValue = "true") @RequestParam(defaultValue = "true") boolean includeChildren) {
        return R.data(businessParamMetaService.selectByCategoryId(categoryId, 1, includeChildren));
    }

    /**
     * 统计分类下的参数数量
     */
    @GetMapping("/categories/{categoryId}/param-count")
    @ApiOperation(value = "统计分类下的参数数量", notes = "统计指定分类下的参数数量")
    public R<Integer> countParamsByCategory(
            @ApiParam(value = "分类ID", required = true) @PathVariable Long categoryId,
            @ApiParam(value = "是否包含子分类", defaultValue = "true") @RequestParam(defaultValue = "true") boolean includeChildren) {
        return R.data(businessParamCategoryService.countParamsByCategory(categoryId, includeChildren));
    }

}
