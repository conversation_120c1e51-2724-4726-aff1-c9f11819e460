package org.springblade.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.dto.BusinessParamValueDTO;
import org.springblade.system.dto.cmd.BusinessParamValueChangeCommand;
import org.springblade.system.dto.query.BusinessParamValueQuery;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.BusinessParamValue;

import java.util.List;

/**
 * 业务参数值服务类
 *
 * <AUTHOR>
 */
public interface IBusinessParamValueService extends BaseService<BusinessParamValue> {

    /**
     * 参数值分页查询
     *
     * @param query 查询参数
     * @return 分页结果 {@link IPage}<{@link BusinessParamValueDTO}>
     */
    IPage<BusinessParamValueDTO> valuePage(BusinessParamValueQuery query);

    /**
     * 查询参数值列表
     *
     * @param query 查询参数
     * @return 参数值列表 {@link List}<{@link BusinessParamValue}>
     */
    List<BusinessParamValue> values(BusinessParamValueQuery query);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return 参数值 {@link BusinessParamValue}
     */
    BusinessParamValue detail(Long id);

    /**
     * 根据租户ID和参数键查询参数值
     *
     * @param tenantId 租户ID
     * @param paramKey 参数键
     * @return 参数值
     */
    BusinessParamValue getByTenantAndParamKey(String tenantId, String paramKey);

    /**
     * 批量查询租户参数值
     *
     * @param tenantId  租户ID
     * @param paramKeys 参数键列表
     * @param status    状态
     * @return 参数值列表
     */
    List<BusinessParamValue> selectByTenantAndParamKeys(String tenantId, List<String> paramKeys, Integer status);

	/**
     * 创建参数值
     *
     * @param meta 参数元数据 {@link BusinessParamMeta}
     */
	void createParamValues(BusinessParamMeta meta);

    /**
     * 为指定租户初始化参数值
     *
     * @param metas   参数元数据列表
     * @param tenantId 指定租户ID
     */
    void initTenantParamValuesForTenant(List<BusinessParamMeta> metas, String tenantId);

    /**
     * 更新参数值
     *
     * @param changeCommand 参数值变更命令
     * @return 更新结果
     */
    boolean changeParamValue(BusinessParamValueChangeCommand changeCommand);

    /**
     * 批量删除参数值
     *
     * @param ids 主键ID列表
     * @return 删除结果
     */
    boolean deleteParamValueBatch(List<Long> ids);

	/**
     * 启用/禁用参数值
     *
     * @param id     主键ID
     * @param status 状态
     * @return 操作结果
     */
    boolean changeStatus(Long id, Integer status);

    /**
     * 批量启用/禁用参数值
     *
     * @param ids    主键ID列表
     * @param status 状态
     * @return 操作结果
     */
    boolean changeStatusBatch(List<Long> ids, Integer status);

	/**
     * 重置参数值
     *
     * @param id 主键ID
     * @return 重置结果
     */
	boolean resetParamValue(Long id);


}
