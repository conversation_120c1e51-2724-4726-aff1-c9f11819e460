<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.BusinessParamChangeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessParamChangeLogResultMap" type="org.springblade.system.entity.BusinessParamLog">
        <id column="id" property="id"/>
        <result column="log_type" property="logType"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="param_key" property="paramKey"/>
        <result column="old_data" property="oldData"/>
        <result column="new_data" property="newData"/>
        <result column="old_version" property="oldVersion"/>
        <result column="new_version" property="newVersion"/>
        <result column="change_type" property="changeType"/>
        <result column="change_reason" property="changeReason"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="change_time" property="changeTime"/>
        <result column="rollback_flag" property="rollbackFlag"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, log_type, tenant_id, param_key, old_data, new_data, old_version, new_version,
        change_type, change_reason, operator_id, operator_name, change_time, rollback_flag,
        create_user, create_time, update_user, update_time, status
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectBusinessParamChangeLogPage" resultMap="businessParamChangeLogResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_change_log
        <where>
            <if test="logType != null and logType != ''">
                AND log_type = #{logType}
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            <if test="paramKey != null and paramKey != ''">
                AND param_key LIKE CONCAT('%', #{paramKey}, '%')
            </if>
            <if test="changeType != null and changeType != ''">
                AND change_type = #{changeType}
            </if>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startTime != null">
                AND change_time &gt;> #{startTime}
            </if>
            <if test="endTime != null">
                AND change_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY change_time DESC
    </select>

    <!-- 查询参数变更历史 -->
    <select id="selectParamChangeHistory" resultMap="businessParamChangeLogResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_change_log
        WHERE param_key = #{paramKey}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY change_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询最近的变更日志 -->
    <select id="selectLatestChangeLog" resultMap="businessParamChangeLogResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_change_log
        WHERE param_key = #{paramKey}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY change_time DESC
        LIMIT 1
    </select>

    <!-- 查询可回退的变更日志 -->
    <select id="selectRollbackableChanges" resultMap="businessParamChangeLogResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_change_log
        WHERE param_key = #{paramKey}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        AND rollback_flag = 0
        AND change_type IN ('CREATE', 'UPDATE')
        ORDER BY change_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>


</mapper>
