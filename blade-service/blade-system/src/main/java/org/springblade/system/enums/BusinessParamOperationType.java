package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务参数值变更类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessParamOperationType {

	/**
     * 创建
     */
	CREATE("CREATE", "新增"),

	/**
     * 修改/变更
     */
	MODIFY("MODIFY", "变更"),

    /**
     * 重置
     */
    RESET("RESET", "重置参数值"),

	/**
	 * 初始化租户参数
	 */
	INIT_TENANT_PARAM("INIT_TENANT_PARAM", "初始化租户参数"),

	;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

}
