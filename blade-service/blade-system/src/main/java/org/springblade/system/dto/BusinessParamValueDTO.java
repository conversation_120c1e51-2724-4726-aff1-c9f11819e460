package org.springblade.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务参数值DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "业务参数值DTO")
public class BusinessParamValueDTO {

    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    private Long categoryId;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String categoryName;

    /**
     * 分类编码
     */
    @ApiModelProperty("分类编码")
    private String categoryCode;

    /**
     * 参数名称
     */
    @ApiModelProperty("参数名称")
    private String paramName;

    /**
     * 参数键
     */
    @ApiModelProperty("参数键")
    private String paramKey;

    /**
     * 参数值
     */
    @ApiModelProperty("参数值")
    private String paramValue;

    /**
     * 参数类型
     */
    @ApiModelProperty("参数类型")
    private String paramType;

    /**
     * 参数级别
     */
    @ApiModelProperty(value = "参数级别", notes = "1:平台级参数 2:租户级参数")
    private Integer paramLevel;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    /**
     * 参数描述
     */
    @ApiModelProperty("参数描述")
    private String description;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
}
