package org.springblade.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.commons.lang3.tuple.Pair;
import org.springblade.system.entity.BusinessParamLog;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.enums.BusinessParamOperationType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务参数变更日志服务类
 *
 * <AUTHOR>
 */
public interface IBusinessParamLogService extends IService<BusinessParamLog> {

    /**
     * 自定义分页查询
     *
     * @param page       分页对象
     * @param logType    日志类型
     * @param tenantId   租户ID
     * @param paramKey   参数键
     * @param changeType 变更类型
     * @param operatorId 操作人ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 分页结果
     */
    IPage<BusinessParamLog> selectBusinessParamChangeLogPage(IPage<BusinessParamLog> page,
															 String logType,
															 String tenantId,
															 String paramKey,
															 String changeType,
															 Long operatorId,
															 LocalDateTime startTime,
															 LocalDateTime endTime);

    /**
     * 查询参数变更历史
     *
     * @param paramKey 参数键
     * @param tenantId 租户ID（可为空）
     * @param limit    限制数量
     * @return 变更日志列表
     */
    List<BusinessParamLog> selectParamChangeHistory(String paramKey, String tenantId, Integer limit);

    /**
     * 查询最近的变更日志
     *
     * @param paramKey 参数键
     * @param tenantId 租户ID（可为空）
     * @return 最近的变更日志
     */
    BusinessParamLog selectLatestChangeLog(String paramKey, String tenantId);

	void createMetaLog(Pair<BusinessParamMeta, BusinessParamMeta> metePair,
					   BusinessParamOperationType operationType,
					   String changeReason);

	/**
	 * 创建参数值变更日志
	 *
	 * @param paramValues 参数值列表
	 * @param operationType 操作类型
	 * @param batchNo 批次号
	 * @param changeReason 变更原因
	 */
	void createValueLogs(List<BusinessParamValue> paramValues,
						 BusinessParamOperationType operationType,
						 String batchNo,
						 String changeReason);

	/**
	 * 创建参数值变更日志
	 *
	 * @param valuePairs 参数值对 <{@code 旧值 }, {@code 新值 }>
	 * @param operationType 操作类型
	 * @param batchNo 批次号
	 * @param changeReason 变更原因
	 */
	void createValueLogsByPairs(List<Pair<BusinessParamValue, BusinessParamValue>> valuePairs,
								BusinessParamOperationType operationType,
								String batchNo,
								String changeReason);


}
