package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.enums.EnableStatus;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务参数值实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_business_param_value")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessParamValue对象", description = "业务参数值")
public class BusinessParamValue extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键", required = true, example = "collection.timeout")
    @NotBlank(message = "参数键不能为空")
    @Size(max = 100, message = "参数键长度不能超过100个字符")
    private String paramKey;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值", example = "30")
    private String paramValue;

    /**
     * 版本号（用于乐观锁）
     */
    @ApiModelProperty(value = "版本号（用于乐观锁）", required = true, example = "1")
    @NotNull(message = "版本号不能为空")
    @Version
    private Integer version;

    @TableField(exist = false)
    @ApiModelProperty("是否已删除")
    private Integer isDeleted;

	/**
	 * 创建业务参数值
     *
     * @param paramKey 参数键
     * @param paramValue 默认值
     * @param tenantId 租户ID
     *
     * @return 业务参数值 {@link BusinessParamValue}
	 */
	public static BusinessParamValue create(String paramKey, String paramValue, String tenantId) {
		BusinessParamValue businessParamValue = new BusinessParamValue();
		businessParamValue.setParamKey(paramKey);
		businessParamValue.setParamValue(paramValue);
		businessParamValue.setTenantId(tenantId);
		businessParamValue.setVersion(1);
		businessParamValue.setStatus(EnableStatus.ENABLE.getCode());
		return businessParamValue;
	}

	/**
	 * 创建平台业务参数值 (默认归属为[000000]租户下)
     *
     * @param paramKey 参数键
     * @param defaultValue 默认值
     *
     * @return 业务参数值 {@link BusinessParamValue}
	 */
	public static BusinessParamValue createPlatformParamValue(String paramKey, String defaultValue) {
		return BusinessParamValue.create(paramKey, defaultValue, TenantConstant.ADMIN_CODE);
	}
	
	/**
	 * 创建租户业务参数值
     *
     * @param paramKey 参数键
     * @param defaultValue 默认值
     * @param tenantIds 租户ID
     *
     * @return 业务参数值 {@link BusinessParamValue}
	 */
	public static List<BusinessParamValue> createTenantParamValuesForTenants(String paramKey,
																			 String defaultValue,
																			 List<String> tenantIds) {
		return tenantIds.stream()
			.map(tenantId -> BusinessParamValue.create(paramKey, defaultValue, tenantId))
			.collect(Collectors.toList());
	}

}
