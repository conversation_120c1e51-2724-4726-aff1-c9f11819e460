package org.springblade.system.event;


import com.leliven.ddd.core.event.DomainEvent;
import org.apache.commons.lang3.tuple.Pair;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.enums.BusinessParamOperationType;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;

/**
 * 业务参数值已变更事件
 *
 * <AUTHOR>
 */
public class BusinessParamValueChangedEvent extends AbstractBusinessParamEvent<List<Pair<BusinessParamValue, BusinessParamValue>>> {

	/**
	 * 构造函数
	 *
	 * @param operationType 变更类型
	 * @param changeReason 变更原因
	 * @param payload 业务参数值列表
	 */
	protected BusinessParamValueChangedEvent(@Nonnull BusinessParamOperationType operationType,
											 @Nullable String changeReason,
											 @Nonnull List<Pair<BusinessParamValue, BusinessParamValue>> payload) {
		super(operationType, changeReason, payload);
	}

	/**
	 * 创建一个参数值改变事件
	 *
	 * @param operationType 参数值改变类型
	 * @param changeReason 变更原因
	 * @param valuePairs 一堆参数值对 <{@code 旧值 }, {@code 新值 }>
	 * @return {@link BusinessParamValueChangedEvent}
	 */
	public static DomainEvent of(@Nonnull BusinessParamOperationType operationType,
								 @Nullable String changeReason,
								 @Nonnull List<Pair<BusinessParamValue, BusinessParamValue>> valuePairs) {
		return new BusinessParamValueChangedEvent(operationType, changeReason, valuePairs);
	}

	/**
	 * 创建一个参数值改变事件
	 *
	 * @param operationType 参数值改变类型
	 * @param changeReason 变更原因
	 * @param valuePair 参数值对 <{@code 旧值 }, {@code 新值 }>
	 * @return {@link BusinessParamValueChangedEvent}
	 */
	public static DomainEvent ofSingleton(@Nonnull BusinessParamOperationType operationType,
										  @Nullable String changeReason,
										  @Nonnull Pair<BusinessParamValue, BusinessParamValue> valuePair) {
		return new BusinessParamValueChangedEvent(operationType, changeReason, Collections.singletonList(valuePair));
	}



}
