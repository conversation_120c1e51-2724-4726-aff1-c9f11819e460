package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.BusinessParamLog;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.enums.BusinessParamOperationType;
import org.springblade.system.event.BusinessParamValueChangedEvent;
import org.springblade.system.mapper.BusinessParamChangeLogMapper;
import org.springblade.system.service.IBusinessParamLogService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务参数变更日志服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessParamLogServiceImpl extends ServiceImpl<BusinessParamChangeLogMapper, BusinessParamLog> implements IBusinessParamLogService {

    @Override
    public IPage<BusinessParamLog> selectBusinessParamChangeLogPage(IPage<BusinessParamLog> page,
																	String logType,
																	String tenantId,
																	String paramKey,
																	String changeType,
																	Long operatorId,
																	LocalDateTime startTime,
																	LocalDateTime endTime) {
        return baseMapper.selectBusinessParamChangeLogPage(page, logType, tenantId, paramKey, changeType, operatorId, startTime, endTime);
    }

    @Override
    public List<BusinessParamLog> selectParamChangeHistory(String paramKey, String tenantId, Integer limit) {
        return baseMapper.selectParamChangeHistory(paramKey, tenantId, limit);
    }

    @Override
    public BusinessParamLog selectLatestChangeLog(String paramKey, String tenantId) {
        return baseMapper.selectLatestChangeLog(paramKey, tenantId);
    }

	@Override
	public void createMetaLog(Pair<BusinessParamMeta, BusinessParamMeta> metePair,
							  BusinessParamOperationType operationType,
							  String changeReason) {
		BusinessParamLog businessParamLog = assembleMetaLog(metePair, operationType, changeReason);
		boolean saved = super.save(businessParamLog);

		if (!saved) {
			throw new ServiceException("保存业务参数元数据相关日志失败");
		}
	}

	@Override
	public void createValueLogs(List<BusinessParamValue> paramValues,
								BusinessParamOperationType operationType,
								String batchNo,
								String changeReason) {
		List<BusinessParamLog> logs = paramValues.stream()
			.map(value -> assembleValueLog(Pair.of(null, value), operationType, batchNo, changeReason))
			.collect(Collectors.toList());

		boolean savedBatch = super.saveBatch(logs);

		if (!savedBatch) {
			throw new ServiceException("保存业务参数值相关日志失败");
		}
	}

	@Override
	public void createValueLogsByPairs(List<Pair<BusinessParamValue, BusinessParamValue>> valuePairs,
									   BusinessParamOperationType operationType,
									   String batchNo,
									   String changeReason) {
		List<BusinessParamLog> logs = valuePairs.stream()
			.map(valuePair ->
				assembleValueLog(valuePair, operationType, batchNo, changeReason))
			.collect(Collectors.toList());

		boolean savedBatch = super.saveBatch(logs);

		if (!savedBatch) {
			throw new ServiceException("保存业务参数值日志失败");
		}

	}

	private BusinessParamLog assembleMetaLog(Pair<BusinessParamMeta, BusinessParamMeta> metePair,
											BusinessParamOperationType operationType,
											String changeReason) {
		BusinessParamLog log = BusinessParamLog.createMetaLog(operationType, AuthUtil.getUser());
		log.setChangeReason(changeReason);

		BusinessParamMeta oldMeta = metePair.getLeft();
		BusinessParamMeta newMeta = metePair.getRight();

		log.setParamKey(newMeta.getParamKey());
		log.setNewData(Func.toJson(newMeta));
		log.setNewVersion(newMeta.getVersion());
		if (null != oldMeta) {
			log.setOldData(Func.toJson(oldMeta));
			log.setOldVersion(oldMeta.getVersion());
		}

		return log;
	}

	private BusinessParamLog assembleValueLog(Pair<BusinessParamValue, BusinessParamValue> valuePair,
											 BusinessParamOperationType operationType,
											 String batchNo,
											 String changeReason) {

		BusinessParamValue oldValue = valuePair.getLeft();
		BusinessParamValue newValue = valuePair.getRight();

		BusinessParamLog log = BusinessParamLog.createValueLog(operationType, AuthUtil.getUser());
		log.setChangeReason(changeReason);
		log.setBatchNo(batchNo);
		log.setTenantId(newValue.getTenantId());

		log.setParamKey(newValue.getParamKey());
		log.setNewData(Func.toJson(newValue));
		log.setNewVersion(newValue.getVersion());
		if (null != oldValue) {
			log.setOldData(Func.toJson(oldValue));
			log.setOldVersion(oldValue.getVersion());
		}

		return log;
	}

}
