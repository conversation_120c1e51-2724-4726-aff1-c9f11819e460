package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.dto.query.BusinessParamCategoryQuery;
import org.springblade.system.entity.BusinessParamCategory;

import java.util.List;

/**
 * 业务参数分类 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BusinessParamCategoryMapper extends BaseMapper<BusinessParamCategory> {

    /**
     * 自定义分页查询
     *
     * @param page  分页对象
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BusinessParamCategory> selectBusinessParamCategoryPage(IPage<BusinessParamCategory> page,
                                                                 @Param("query") BusinessParamCategoryQuery query);

    /**
     * 根据分类编码查询分类
     *
     * @param categoryCode 分类编码
     * @return 分类信息
     */
    BusinessParamCategory selectByCategoryCode(@Param("categoryCode") String categoryCode);

    /**
     * 根据父分类ID查询子分类
     *
     * @param parentId 父分类ID
     * @param status   状态
     * @return 子分类列表
     */
    List<BusinessParamCategory> selectByParentId(@Param("parentId") Long parentId,
                                                 @Param("status") Integer status);

    /**
     * 根据祖先路径查询所有后代分类
     *
     * @param ancestorPath 祖先路径
     * @param status       状态
     * @return 后代分类列表
     */
    List<BusinessParamCategory> selectDescendants(@Param("ancestorPath") String ancestorPath,
                                                  @Param("status") Integer status);

    /**
     * 根据分类ID列表查询分类
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 分类列表
     */
    List<BusinessParamCategory> selectByCategoryIds(@Param("categoryIds") List<Long> categoryIds,
                                                    @Param("status") Integer status);

    /**
     * 查询所有根分类
     *
     * @param status 状态
     * @return 根分类列表
     */
    List<BusinessParamCategory> selectRootCategories(@Param("status") Integer status);

    /**
     * 查询分类树（包含参数数量统计）
     *
     * @param status 状态
     * @return 分类树列表
     */
    List<BusinessParamCategory> selectCategoryTreeWithParamCount(@Param("status") Integer status);

    /**
     * 检查分类编码是否存在
     *
     * @param categoryCode 分类编码
     * @param excludeId    排除的ID（用于更新时检查）
     * @return 存在数量
     */
    int checkCategoryCodeExists(@Param("categoryCode") String categoryCode,
                                @Param("excludeId") Long excludeId);

    /**
     * 统计分类下的参数数量
     *
     * @param categoryId 分类ID
     * @return 参数数量
     */
    int countParamsByCategory(@Param("categoryId") Long categoryId);

    /**
     * 统计分类下的参数数量（包含子分类）
     *
     * @param ancestorPath 祖先路径
     * @return 参数数量
     */
    int countParamsByCategoryTree(@Param("ancestorPath") String ancestorPath);

    /**
     * 根据层级查询分类
     *
     * @param level  层级
     * @param status 状态
     * @return 分类列表
     */
    List<BusinessParamCategory> selectByLevel(@Param("level") Integer level,
                                              @Param("status") Integer status);

    /**
     * 更新子分类的祖先路径
     *
     * @param oldAncestorPath 旧祖先路径
     * @param newAncestorPath 新祖先路径
     * @return 更新数量
     */
    int updateDescendantAncestorPath(@Param("oldAncestorPath") String oldAncestorPath,
                                     @Param("newAncestorPath") String newAncestorPath);

    /**
     * 批量更新分类状态
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("categoryIds") List<Long> categoryIds,
                          @Param("status") Integer status);
}
