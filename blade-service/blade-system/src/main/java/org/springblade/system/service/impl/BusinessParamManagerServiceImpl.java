package org.springblade.system.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.BusinessParamLog;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.Tenant;
import org.springblade.system.enums.BusinessParamLevel;
import org.springblade.system.enums.BusinessParamType;
import org.springblade.system.event.BusinessParamMateCreatedEvent;
import org.springblade.system.event.TenantCreatedEvent;
import org.springblade.system.service.IBusinessParamLogService;
import org.springblade.system.service.IBusinessParamManagerService;
import org.springblade.system.service.IBusinessParamMetaService;
import org.springblade.system.service.IBusinessParamValueService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.*;

/**
 * 业务参数统一管理服务实现类
 *
 * <p>
 * 整合元数据、参数值和变更日志的管理功能
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessParamManagerServiceImpl implements IBusinessParamManagerService {

    private final IBusinessParamMetaService paramMetaService;
    private final IBusinessParamValueService paramValueService;
    private final IBusinessParamLogService paramChangeLogService;

	@TransactionalEventListener(phase= TransactionPhase.BEFORE_COMMIT)
	public void onBusinessParamMeteCreatedEvent(BusinessParamMateCreatedEvent event) {
		paramValueService.createParamValues(event.getPayload());
	}

	@TransactionalEventListener(phase= TransactionPhase.BEFORE_COMMIT)
	public void onTenantCreatedEvent(TenantCreatedEvent event) {
		Tenant tenant = event.getPayload();
		// 查询所有的租户级别的参数元数据
        List<BusinessParamMeta> metas = paramMetaService.listByParamLevel(BusinessParamLevel.TENANT);
        // 初始化租户参数值
        paramValueService.initTenantParamValuesForTenant(metas, tenant.getTenantId());
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackParamChange(Long changeLogId, String changeReason) {
		BladeUser user = AuthUtil.getUser();

		// 获取变更日志
		BusinessParamLog changeLog = paramChangeLogService.getById(changeLogId);
		if (changeLog == null) {
			throw new ServiceException("变更日志不存在");
		}

		// TODO: 实现回退逻辑
		// 这里需要根据变更日志的类型和内容进行相应的回退操作
		log.warn("参数回退功能待实现，变更日志ID: {}", changeLogId);

		return false;
    }

    @Override
    public boolean validateParamValue(String paramKey, String paramValue) {
		BusinessParamMeta paramMeta = paramMetaService.getByParamKey(paramKey);
		if (paramMeta == null) {
			return false;
		}

		// 检查必填
		if (paramMeta.getIsRequired() == 1 && Func.isEmpty(paramValue)) {
			return false;
		}

		// 根据参数类型验证
		BusinessParamType paramType = BusinessParamType.resolve(paramMeta.getParamType());
		if (paramType == null) {
			return false;
		}

		switch (paramType) {
			case NUMBER:
				try {
					Double.parseDouble(paramValue);
				} catch (NumberFormatException e) {
					return false;
				}
				break;
			case BOOLEAN:
				if (!"true".equalsIgnoreCase(paramValue) && !"false".equalsIgnoreCase(paramValue)) {
					return false;
				}
				break;
			case JSON:
				try {
					JSON.parse(paramValue);
				} catch (Exception e) {
					return false;
				}
				break;
			case STRING:
			default:
				// 字符串类型不需要特殊验证
				break;
		}

		// TODO: 根据验证规则进行更详细的验证
		// 这里可以解析 paramMeta.getValidationRule() 进行更复杂的验证

		return true;
    }

    @Override
    public Map<String, Boolean> batchValidateParamValues(Map<String, String> paramValues) {
        Map<String, Boolean> result = new HashMap<>();
        if (Func.isEmpty(paramValues)) {
            return result;
        }

        for (Map.Entry<String, String> entry : paramValues.entrySet()) {
            boolean isValid = validateParamValue(entry.getKey(), entry.getValue());
            result.put(entry.getKey(), isValid);
        }

        return result;
    }

    @Override
    public List<BusinessParamLog> getParamChangeHistory(String paramKey, String tenantId, Integer limit) {
        return paramChangeLogService.selectParamChangeHistory(paramKey, tenantId, limit);
    }

}
