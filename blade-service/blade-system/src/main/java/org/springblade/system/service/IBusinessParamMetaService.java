package org.springblade.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.dto.query.BusinessParamMetaQuery;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.enums.BusinessParamLevel;

import java.util.List;
import java.util.Optional;

/**
 * 业务参数元数据服务类
 *
 * <AUTHOR>
 */
public interface IBusinessParamMetaService extends BaseService<BusinessParamMeta> {

    /**
     * 自定义分页查询
     *
     * @param query 查询参数
     * @return 分页结果 {@link IPage}<{@link BusinessParamMeta}>
     */
    IPage<BusinessParamMeta> metaPage(BusinessParamMetaQuery query);

    /**
     * 查询参数元数据列表
     *
     * @param query 查询参数
     * @return 参数元数据列表 {@link List}<{@link BusinessParamMeta}>
     */
    List<BusinessParamMeta> metes(BusinessParamMetaQuery query);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return 参数元数据 {@link BusinessParamMeta}
     */
    BusinessParamMeta detail(Long id);

    /**
     * 根据分类ID查询参数元数据
     *
     * @param categoryId      分类ID
     * @param status          状态
     * @param includeChildren 是否包含子分类
     * @return 参数元数据列表 {@link List}<{@link BusinessParamMeta}>
     */
    List<BusinessParamMeta> selectByCategoryId(Long categoryId, Integer status, boolean includeChildren);

    /**
     * 根据分类ID列表查询参数元数据
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 参数元数据列表 {@link List}<{@link BusinessParamMeta}>
     */
    List<BusinessParamMeta> selectByCategoryIds(List<Long> categoryIds, Integer status);

    /**
     * 根据参数级别查询参数元数据
     *
     * @param paramLevel 参数级别 {@link BusinessParamLevel}
     * @return 参数元数据列表 {@link List}<{@link BusinessParamMeta}>
     */
    List<BusinessParamMeta> listByParamLevel(BusinessParamLevel paramLevel);

    /**
     * 根据参数键查询参数元数据
     *
     * @param paramKey 参数键
     * @return 参数元数据 {@link BusinessParamMeta}
     */
    BusinessParamMeta getByParamKey(String paramKey);

	/**
     * 根据参数键查询参数元数据
     *
     * @param paramKey 参数键
     * @return 参数元数据 {@link Optional}<{@link BusinessParamMeta}>
     */
	Optional<BusinessParamMeta> getOptionalByParamKey(String paramKey);

	/**
     * 根据参数键查询参数元数据
     *
     * @param paramKey 参数键
     * @return 参数元数据 {@link BusinessParamMeta}
     */
	BusinessParamMeta getOrElseThrow(String paramKey);

	/**
     * 批量查询参数元数据
     *
     * @param paramKeys 参数键列表
     * @param status    状态
     * @return 参数元数据列表 {@link List}<{@link BusinessParamMeta}>
     */
    List<BusinessParamMeta> selectByParamKeys(List<String> paramKeys, Integer status);

    /**
     * 创建参数元数据
     *
     * @param businessParamMeta 参数元数据
     * @return 创建结果 {@code true} 成功, {@code false} 失败
     */
    boolean add(BusinessParamMeta businessParamMeta);

    /**
     * 更新参数元数据
     *
     * @param businessParamMeta 参数元数据
     * @return 更新结果 {@code true} 成功, {@code false} 失败
     */
    boolean edit(BusinessParamMeta businessParamMeta);

    /**
     * 删除参数元数据
     *
     * @param id 主键ID
     * @return 删除结果 {@code true} 成功, {@code false} 失败
     */
    boolean deleteParamMeta(Long id);

    /**
     * 批量删除参数元数据
     *
     * @param ids 主键ID列表
     * @return 删除结果 {@code true} 成功, {@code false} 失败
     */
    boolean deleteParamMetaBatch(List<Long> ids);

    /**
     * 启用/禁用参数元数据
     *
     * @param id     主键ID
     * @param status 状态
     * @return 操作结果 {@code true} 成功, {@code false} 失败
     */
    boolean changeStatus(Long id, Integer status);

    /**
     * 批量启用/禁用参数元数据
     *
     * @param ids    主键ID列表
     * @param status 状态
     * @return 操作结果 {@code true} 成功, {@code false} 失败
     */
    boolean changeStatusBatch(List<Long> ids, Integer status);


}
