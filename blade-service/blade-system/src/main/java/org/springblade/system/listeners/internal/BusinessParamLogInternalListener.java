package org.springblade.system.listeners.internal;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springblade.system.enums.BusinessParamOperationType;
import org.springblade.system.event.BusinessParamMateCreatedEvent;
import org.springblade.system.event.BusinessParamValueBatchCreatedEvent;
import org.springblade.system.event.BusinessParamValueChangedEvent;
import org.springblade.system.service.IBusinessParamLogService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 业务参数日志内部事件监听器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class BusinessParamLogInternalListener {

	public final IBusinessParamLogService businessParamLogService;


	@TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
	public void onParamMateCreatedEvent(BusinessParamMateCreatedEvent event) {
		businessParamLogService.createMetaLog(
			Pair.of(null, event.getPayload()), BusinessParamOperationType.CREATE, "");
	}

	@TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
	public void onParamValueBatchCreatedEvent(BusinessParamValueBatchCreatedEvent event) {
		businessParamLogService.createValueLogs(
			event.getPayload(), BusinessParamOperationType.CREATE, event.getId(), event.getChangeReason());
	}

	@TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
	public void onParamValueChangedEvent(BusinessParamValueChangedEvent event) {
		businessParamLogService.createValueLogsByPairs(
			event.getPayload(), event.getOperationType(), event.getId(), event.getChangeReason());
	}
}
