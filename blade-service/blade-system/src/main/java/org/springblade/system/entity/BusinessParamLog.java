package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.constant.TenantConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.system.enums.BusinessParamLogCategory;
import org.springblade.system.enums.BusinessParamOperationType;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 业务参数变更日志实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_business_param_log")
@ApiModel(value = "BusinessParamChangeLog对象", description = "业务参数日志")
public class BusinessParamLog implements Serializable {

    private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;

    /**
     * 日志类型
	 *
	 * @see org.springblade.system.enums.BusinessParamLogCategory
     */
    @ApiModelProperty(value = "日志类型", example = "1", allowableValues = "1,2")
    private Integer category;

	/**
	 * 操作类型
	 */
	@ApiModelProperty(value = "操作类型", required = true, example = "UPDATE", allowableValues = "CREATE,UPDATE,DELETE")
	private String operationType;

    /**
     * 参数键
     */
    @ApiModelProperty(value = "参数键", example = "collection.timeout")
    private String paramKey;

    /**
     * 变更前数据（JSON格式）
     */
    @ApiModelProperty(value = "变更前数据（JSON格式）", example = "{\"paramValue\":\"30\",\"version\":1}")
    private String oldData;

    /**
     * 变更后数据（JSON格式）
     */
    @ApiModelProperty(value = "变更后数据（JSON格式）", example = "{\"paramValue\":\"60\",\"version\":2}")
    private String newData;

    /**
     * 变更前版本号
     */
    @ApiModelProperty(value = "变更前版本号", example = "1")
    private Integer oldVersion;

    /**
     * 变更后版本号
     */
    @ApiModelProperty(value = "变更后版本号", example = "2")
    private Integer newVersion;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", example = "业务需求调整")
    private String changeReason;

	/**
     * 批次号
     */
	@ApiModelProperty(value = "批次号")
	private String batchNo;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID", required = true, example = "1123598821738675201")
    @NotNull(message = "操作人ID不能为空")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名", required = true, example = "admin")
    private String operatorName;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private LocalDateTime operatorTime;

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;


	/**
	 * 创建参数元数据日志
	 *
	 * @param operationType 操作类型
	 * @param user 操作人
	 * @return 日志
	 */
	public static BusinessParamLog createMetaLog(BusinessParamOperationType operationType, BladeUser user) {
		BusinessParamLog log = createLog(BusinessParamLogCategory.META, operationType, user);
		log.setTenantId(TenantConstant.ADMIN_CODE);
		return log;
	}

	/**
	 * 创建参数值日志
	 *
	 * @param operationType 操作类型
	 * @param user 操作人
	 * @return 日志
	 */
	public static BusinessParamLog createValueLog(BusinessParamOperationType operationType, BladeUser user) {
		return createLog(BusinessParamLogCategory.VALUE, operationType, user);
	}

	/**
	 * 创建日志
	 *
	 * @param category 分类
	 * @param operationType 操作类型
	 * @param user 操作人
	 * @return 日志
	 */
	public static BusinessParamLog createLog(BusinessParamLogCategory category,
											 BusinessParamOperationType operationType,
											 BladeUser user) {
		BusinessParamLog log = new BusinessParamLog();
		log.setCategory(category.getCode());
		log.setOperationType(operationType.getCode());
		log.setOperatorTime(LocalDateTime.now());
		if (user != null) {
			log.setOperatorId(AuthUtil.getUserId());
			log.setOperatorName(AuthUtil.getNickName());
		}

		log.setOldVersion(0);
		log.setOldData("{}");

		return log;
	}



}
