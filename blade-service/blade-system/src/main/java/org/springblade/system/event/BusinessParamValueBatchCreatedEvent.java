package org.springblade.system.event;

import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.enums.BusinessParamOperationType;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;

/**
 * 业务参数值批量已创建事件
 *
 * <AUTHOR>
 */
public class BusinessParamValueBatchCreatedEvent extends AbstractBusinessParamEvent<List<BusinessParamValue>> {

    /**
     * Create a new DomainEvent.
     *
     * @param payload the payload object (never {@code null})
     */
    protected BusinessParamValueBatchCreatedEvent(@Nonnull BusinessParamOperationType operationType,
												  @Nonnull List<BusinessParamValue> payload) {
		super(operationType, "", payload);
    }

    /**
     * 创建事件
     *
     * @param payload 一堆已创建的业务参数值
     * @return 业务参数值批量已创建事件 {@link BusinessParamValueBatchCreatedEvent}
     */
    public static BusinessParamValueBatchCreatedEvent of(@Nonnull BusinessParamOperationType operationType,
														 @Nonnull List<BusinessParamValue> payload) {
        return new BusinessParamValueBatchCreatedEvent(operationType, payload);
    }
}
