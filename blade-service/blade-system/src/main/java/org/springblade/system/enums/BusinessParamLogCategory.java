package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessParamLogCategory {

    /**
     * 元数据
     */
    META(1, "元数据"),

    /**
     * 参数值
     */
    VALUE(2, "参数值");

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


}
