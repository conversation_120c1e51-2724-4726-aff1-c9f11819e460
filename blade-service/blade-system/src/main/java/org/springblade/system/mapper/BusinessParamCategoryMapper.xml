<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.BusinessParamCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessParamCategoryResultMap" type="org.springblade.system.entity.BusinessParamCategory">
        <id column="id" property="id"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestor_path" property="ancestorPath"/>
        <result column="level" property="level"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="icon" property="icon"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <!-- 扩展字段 -->
        <result column="param_count" property="paramCount"/>
        <result column="parent_name" property="parentName"/>
        <result column="full_path" property="fullPath"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, category_code, category_name, parent_id, ancestor_path, level, sort_order, 
        icon, description, status, create_user, create_dept, create_time, update_user, update_time
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectBusinessParamCategoryPage" resultMap="businessParamCategoryResultMap">
        SELECT 
            c.<include refid="baseColumnList"/>,
            p.category_name as parent_name,
            COUNT(pm.id) as param_count
        FROM blade_business_param_category c
        LEFT JOIN blade_business_param_category p ON c.parent_id = p.id
        LEFT JOIN blade_business_param_meta pm ON c.id = pm.category_id
        <where>
            <if test="query.categoryCode != null and query.categoryCode != ''">
                AND c.category_code LIKE CONCAT('%', #{query.categoryCode}, '%')
            </if>
            <if test="query.categoryName != null and query.categoryName != ''">
                AND c.category_name LIKE CONCAT('%', #{query.categoryName}, '%')
            </if>
            <if test="query.parentId != null">
                AND c.parent_id = #{query.parentId}
            </if>
            <if test="query.level != null">
                AND c.level = #{query.level}
            </if>
            <if test="query.status != null">
                AND c.status = #{query.status}
            </if>
            <if test="query.rootOnly != null and query.rootOnly">
                AND c.parent_id IS NULL
            </if>
        </where>
        GROUP BY c.id
        ORDER BY c.level, c.sort_order, c.create_time DESC
    </select>

    <!-- 根据分类编码查询分类 -->
    <select id="selectByCategoryCode" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE category_code = #{categoryCode}
        LIMIT 1
    </select>

    <!-- 根据父分类ID查询子分类 -->
    <select id="selectByParentId" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE parent_id = #{parentId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order, create_time DESC
    </select>

    <!-- 根据祖先路径查询所有后代分类 -->
    <select id="selectDescendants" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE ancestor_path LIKE CONCAT(#{ancestorPath}, ',%')
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY level, sort_order, create_time DESC
    </select>

    <!-- 根据分类ID列表查询分类 -->
    <select id="selectByCategoryIds" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY level, sort_order, create_time DESC
    </select>

    <!-- 查询所有根分类 -->
    <select id="selectRootCategories" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE parent_id IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order, create_time DESC
    </select>

    <!-- 查询分类树（包含参数数量统计） -->
    <select id="selectCategoryTreeWithParamCount" resultMap="businessParamCategoryResultMap">
        SELECT 
            c.<include refid="baseColumnList"/>,
            COUNT(pm.id) as param_count
        FROM blade_business_param_category c
        LEFT JOIN blade_business_param_meta pm ON c.id = pm.category_id
        <if test="status != null">
            WHERE c.status = #{status}
        </if>
        GROUP BY c.id
        ORDER BY c.level, c.sort_order, c.create_time DESC
    </select>

    <!-- 检查分类编码是否存在 -->
    <select id="checkCategoryCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_category
        WHERE category_code = #{categoryCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 统计分类下的参数数量 -->
    <select id="countParamsByCategory" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_meta
        WHERE category_id = #{categoryId}
    </select>

    <!-- 统计分类下的参数数量（包含子分类） -->
    <select id="countParamsByCategoryTree" resultType="int">
        SELECT COUNT(1)
        FROM blade_business_param_meta pm
        JOIN blade_business_param_category c ON pm.category_id = c.id
        WHERE c.ancestor_path LIKE CONCAT(#{ancestorPath}, '%')
    </select>

    <!-- 根据层级查询分类 -->
    <select id="selectByLevel" resultMap="businessParamCategoryResultMap">
        SELECT <include refid="baseColumnList"/>
        FROM blade_business_param_category
        WHERE level = #{level}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order, create_time DESC
    </select>

    <!-- 更新子分类的祖先路径 -->
    <update id="updateDescendantAncestorPath">
        UPDATE blade_business_param_category
        SET ancestor_path = REPLACE(ancestor_path, #{oldAncestorPath}, #{newAncestorPath}),
            update_time = NOW()
        WHERE ancestor_path LIKE CONCAT(#{oldAncestorPath}, ',%')
    </update>

    <!-- 批量更新分类状态 -->
    <update id="batchUpdateStatus">
        UPDATE blade_business_param_category
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </update>

</mapper>
