package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.system.dto.query.BusinessParamMetaQuery;
import org.springblade.system.entity.BusinessParamMeta;

import java.util.List;

/**
 * 业务参数元数据 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BusinessParamMetaMapper extends BaseMapper<BusinessParamMeta> {

    /**
     * 自定义分页查询
     *
     * @param page  分页对象
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BusinessParamMeta> selectBusinessParamMetaPage(IPage<BusinessParamMeta> page,
                                                         @Param("query") BusinessParamMetaQuery query);

    /**
     * 根据分类ID查询参数元数据
     *
     * @param categoryId 分类ID
     * @param status     状态
     * @return 参数元数据列表
     */
    List<BusinessParamMeta> selectByCategoryId(@Param("categoryId") Long categoryId,
                                               @Param("status") Integer status);

    /**
     * 根据分类ID列表查询参数元数据
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 参数元数据列表
     */
    List<BusinessParamMeta> selectByCategoryIds(@Param("categoryIds") List<Long> categoryIds,
                                                @Param("status") Integer status);

    /**
     * 根据参数键查询参数元数据
     *
     * @param paramKey 参数键
     * @return 参数元数据
     */
    BusinessParamMeta selectByParamKey(@Param("paramKey") String paramKey);

    /**
     * 批量查询参数元数据
     *
     * @param paramKeys 参数键列表
     * @param status    状态
     * @return 参数元数据列表
     */
    List<BusinessParamMeta> selectByParamKeys(@Param("paramKeys") List<String> paramKeys,
                                              @Param("status") Integer status);

    /**
     * 检查参数键是否存在
     *
     * @param paramKey 参数键
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在数量
     */
    int checkParamKeyExists(@Param("paramKey") String paramKey,
                           @Param("excludeId") Long excludeId);

    /**
     * 根据分类ID统计参数数量
     *
     * @param categoryId 分类ID
     * @param status     状态
     * @return 参数数量
     */
    int countByCategoryId(@Param("categoryId") Long categoryId,
                          @Param("status") Integer status);

    /**
     * 根据分类ID列表统计参数数量
     *
     * @param categoryIds 分类ID列表
     * @param status      状态
     * @return 参数数量
     */
    int countByCategoryIds(@Param("categoryIds") List<Long> categoryIds,
                           @Param("status") Integer status);

    /**
     * 查询参数元数据（包含分类信息）
     *
     * @param page  分页对象
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BusinessParamMeta> selectBusinessParamMetaPageWithCategory(IPage<BusinessParamMeta> page,
                                                                     @Param("query") BusinessParamMetaQuery query);

}
