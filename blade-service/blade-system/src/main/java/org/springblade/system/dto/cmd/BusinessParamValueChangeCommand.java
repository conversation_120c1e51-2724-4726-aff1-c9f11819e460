package org.springblade.system.dto.cmd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 业务参数值修改命令
 *
 * <AUTHOR>
 */
@Data
public class BusinessParamValueChangeCommand {

	@NotNull(message = "参数值ID不能为空")
	@ApiModelProperty("参数值ID")
	private Long id;

	@NotBlank(message = "参数值不能为空")
	@ApiModelProperty("参数值版本")
	private Integer version;

	@NotBlank(message = "参数值不能为空")
	@ApiModelProperty("参数值")
	private String paramValue;

}
