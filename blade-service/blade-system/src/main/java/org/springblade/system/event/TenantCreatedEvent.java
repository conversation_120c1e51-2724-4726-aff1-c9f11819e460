package org.springblade.system.event;

import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;
import org.springblade.system.entity.Tenant;

import javax.annotation.Nonnull;

/**
 * 租户已创建创建事件
 *
 * <AUTHOR>
 */
public class TenantCreatedEvent extends AbstractPayloadDomainEvent<Tenant> {
	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	protected TenantCreatedEvent(@Nonnull Tenant payload) {
		super(payload);
	}

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload the payload object (never {@code null})
	 */
	public static TenantCreatedEvent of(@Nonnull Tenant payload) {
		return new TenantCreatedEvent(payload);
	}
}
