package org.springblade.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.ddd.core.event.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springblade.common.constant.TenantConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.dto.BusinessParamValueDTO;
import org.springblade.system.dto.cmd.BusinessParamValueChangeCommand;
import org.springblade.system.dto.query.BusinessParamValueQuery;
import org.springblade.system.entity.BusinessParamMeta;
import org.springblade.system.entity.BusinessParamValue;
import org.springblade.system.enums.BusinessParamLevel;
import org.springblade.system.enums.BusinessParamOperationType;
import org.springblade.system.event.BusinessParamValueBatchCreatedEvent;
import org.springblade.system.event.BusinessParamValueChangedEvent;
import org.springblade.system.mapper.BusinessParamValueMapper;
import org.springblade.system.service.IBusinessParamMetaService;
import org.springblade.system.service.IBusinessParamValueService;
import org.springblade.system.service.ITenantService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务参数值服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessParamValueServiceImpl extends BaseServiceImpl<BusinessParamValueMapper, BusinessParamValue> implements IBusinessParamValueService {

    private final ITenantService tenantService;
    private final DomainEventPublisher domainEventPublisher;
    private final IBusinessParamMetaService businessParamMetaService;

    @Override
    public IPage<BusinessParamValueDTO> valuePage(BusinessParamValueQuery query) {
        return baseMapper.selectBusinessParamValuePage(Condition.getPage(query), query);
    }

    @Override
    public List<BusinessParamValue> values(BusinessParamValueQuery query) {
        return Collections.emptyList();
    }

    @Override
    public BusinessParamValue detail(Long id) {
        return getById(id);
    }

	public Optional<BusinessParamValue> getOptionalById(Long id) {
        return Optional.ofNullable(getById(id));
    }

	public BusinessParamValue getOrElseThrow(Long id) {
		return getOptionalById(id).orElseThrow(() -> new ServiceException("参数值不存在, id: " + id));
	}

    @Override
    public BusinessParamValue getByTenantAndParamKey(String tenantId, String paramKey) {
        return baseMapper.selectByTenantAndParamKey(tenantId, paramKey);
    }

    @Override
    public List<BusinessParamValue> selectByTenantAndParamKeys(String tenantId, List<String> paramKeys, Integer status) {
        if (Func.isEmpty(paramKeys)) {
            return Collections.emptyList();
        }
        return baseMapper.selectByTenantAndParamKeys(tenantId, paramKeys, status);
    }

	@Override
    public void createParamValues(BusinessParamMeta meta) {
        BusinessParamLevel businessParamLevel = BusinessParamLevel.resolve(meta.getParamLevel());
        List<BusinessParamValue> paramValues = new ArrayList<>();
        if (BusinessParamLevel.PLATFORM == businessParamLevel) {
            paramValues.add(BusinessParamValue.createPlatformParamValue(meta.getParamKey(), meta.getDefaultValue()));
        } else if (BusinessParamLevel.TENANT == businessParamLevel) {
            List<String> tenantIds = this.tenantService.listAllTenantIds().stream()
                .filter(tenantId -> !TenantConstant.ADMIN_CODE.equals(tenantId))
                .collect(Collectors.toList());
            paramValues.addAll(BusinessParamValue.createTenantParamValuesForTenants(
				meta.getParamKey(), meta.getDefaultValue(), tenantIds));
        }

        boolean saved = super.saveBatch(paramValues);

        if (!saved) {
            throw new ServiceException("创建参数值失败");
        }

        this.domainEventPublisher.publishEvent(
			BusinessParamValueBatchCreatedEvent.of(BusinessParamOperationType.CREATE, paramValues));
    }

    @Override
    public void initTenantParamValuesForTenant(List<BusinessParamMeta> metas, String tenantId) {
        List<BusinessParamValue> paramValues = metas.stream()
            .map(meta -> BusinessParamValue.create(meta.getParamKey(), meta.getDefaultValue(), tenantId))
            .collect(Collectors.toList());

        boolean saved = super.saveBatch(paramValues);

        if (!saved) {
            throw new ServiceException("初始化租户参数值失败");
        }

        this.domainEventPublisher.publishEvent(
			BusinessParamValueBatchCreatedEvent.of(BusinessParamOperationType.INIT_TENANT_PARAM, paramValues));
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
    public boolean changeParamValue(BusinessParamValueChangeCommand changeCommand) {
		BusinessParamValue oldParamValue = getOrElseThrow(changeCommand.getId());
		verifyVersionShouldValid(oldParamValue.getVersion(), changeCommand.getVersion());

		BusinessParamMeta businessParamMeta = businessParamMetaService.getOrElseThrow(oldParamValue.getParamKey());
		businessParamMeta.verifyValueShouldValid(changeCommand.getParamValue());

		BusinessParamValue newParamValue = new BusinessParamValue();
		newParamValue.setId(oldParamValue.getId());
		newParamValue.setVersion(oldParamValue.getVersion());
		newParamValue.setParamValue(changeCommand.getParamValue());
		newParamValue.setTenantId(oldParamValue.getTenantId());

		return this.updateParamValue(BusinessParamOperationType.MODIFY, oldParamValue, newParamValue);
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean resetParamValue(Long id) {
		BusinessParamValue oldParamValue = getOrElseThrow(id);

		BusinessParamMeta businessParamMeta = businessParamMetaService.getOrElseThrow(oldParamValue.getParamKey());
		businessParamMeta.verifyValueShouldValid(oldParamValue.getParamValue());

		BusinessParamValue newParamValue = new BusinessParamValue();
		newParamValue.setId(oldParamValue.getId());
		newParamValue.setVersion(oldParamValue.getVersion());
		newParamValue.setParamValue(businessParamMeta.getDefaultValue());
		newParamValue.setTenantId(oldParamValue.getTenantId());

		return this.updateParamValue(BusinessParamOperationType.RESET, oldParamValue, newParamValue);
	}

    @Override
    public boolean deleteParamValueBatch(List<Long> ids) {
        if (Func.isEmpty(ids)) {
            return false;
        }
        return removeByIds(ids);
    }

	@Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatus(Long id, Integer status) {
        BusinessParamValue businessParamValue = new BusinessParamValue();
        businessParamValue.setId(id);
        businessParamValue.setStatus(status);
        businessParamValue.setUpdateTime(new Date());

        return updateById(businessParamValue);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStatusBatch(List<Long> ids, Integer status) {

		if (Func.isEmpty(ids)) {
			return false;
		}

		List<BusinessParamValue> businessParamValues = ids.stream().map(id -> {
			BusinessParamValue businessParamValue = new BusinessParamValue();
			businessParamValue.setId(id);
			businessParamValue.setStatus(status);
			businessParamValue.setUpdateTime(new Date());
			return businessParamValue;
		}).collect(Collectors.toList());

		return super.updateBatchById(businessParamValues);
    }

	/**
	 * 统一更新业务参数值并发布变更事件
	 *
	 * @param oldParamValue 老的参数值对象
	 * @param newParamValue 新的参数值对象
	 * @return 是否更新成功
	 * @throws ServiceException 更新失败时抛出异常
	 */
	private boolean updateParamValue(BusinessParamOperationType changeType,
									 BusinessParamValue oldParamValue, BusinessParamValue newParamValue) {
		// 执行更新操作
		boolean updated = updateById(newParamValue);

		if (!updated) {
			throw new ServiceException("更新业务参数值失败");
		}

		// 发布业务参数值变更事件
		domainEventPublisher.publishEvent(
			BusinessParamValueChangedEvent.ofSingleton(changeType, "", Pair.of(oldParamValue, newParamValue)));

		return true;
	}

	/**
	 * 校验版本号是否一致
	 *
	 * @param latestVersion 查询到的最新版本号
	 * @param version 传入的版本号
	 * @throws ServiceException 版本不一致时抛出业务异常
	 */
	private void verifyVersionShouldValid(Integer latestVersion, Integer version) {
		if (!Objects.equals(latestVersion, version)) {
			throw new ServiceException("参数值版本不一致，参数值可能已被其他操作人变更");
		}
	}

}
