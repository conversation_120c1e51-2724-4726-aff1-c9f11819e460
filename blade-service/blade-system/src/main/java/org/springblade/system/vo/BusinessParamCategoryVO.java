package org.springblade.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.system.entity.BusinessParamCategory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 业务参数分类视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("业务参数分类视图对象")
public class BusinessParamCategoryVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("分类编码")
    private String categoryCode;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("父分类ID")
    private Long parentId;

    @ApiModelProperty("父分类名称")
    private String parentName;

    @ApiModelProperty("祖先路径")
    private String ancestorPath;

    @ApiModelProperty("层级")
    private Integer level;

    @ApiModelProperty("排序")
    private Integer sortOrder;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("参数数量")
    private Integer paramCount;

    @ApiModelProperty("完整路径名称")
    private String fullPath;

    @ApiModelProperty("子分类列表")
    private List<BusinessParamCategoryVO> children;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 从实体转换为VO
     */
    public static BusinessParamCategoryVO fromEntity(BusinessParamCategory entity) {
        if (entity == null) {
            return null;
        }
        
        BusinessParamCategoryVO vo = new BusinessParamCategoryVO();
        vo.setId(entity.getId());
        vo.setCategoryCode(entity.getCategoryCode());
        vo.setCategoryName(entity.getCategoryName());
        vo.setParentId(entity.getParentId());
        vo.setParentName(entity.getParentName());
        vo.setAncestorPath(entity.getAncestorPath());
        vo.setLevel(entity.getLevel());
        vo.setSortOrder(entity.getSortOrder());
        vo.setIcon(entity.getIcon());
        vo.setDescription(entity.getDescription());
        vo.setStatus(entity.getStatus());
        vo.setParamCount(entity.getParamCount());
        vo.setFullPath(entity.getFullPath());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        
        // 转换子分类
        if (entity.getChildren() != null) {
            List<BusinessParamCategoryVO> children = new ArrayList<>();
            for (BusinessParamCategory child : entity.getChildren()) {
                children.add(BusinessParamCategoryVO.fromEntity(child));
            }
            vo.setChildren(children);
        }
        
        return vo;
    }
}
