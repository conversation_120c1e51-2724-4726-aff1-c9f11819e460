package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.enums.BusinessParamType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 业务参数元数据实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_business_param_meta")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessParamMeta对象", description = "业务参数元数据")
public class BusinessParamMeta extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

	/**
	 * 参数级别：1-平台级、2-租户级
	 *
	 * @see org.springblade.system.enums.BusinessParamLevel
	 */
	@ApiModelProperty(value = "参数级别", required = true, example = "1", allowableValues = "1,2")
	private Integer paramLevel;

    /**
     * 参数键（全局唯一）
     */
    @ApiModelProperty(value = "参数键（全局唯一）", required = true, example = "collection.timeout")
    @NotBlank(message = "参数键不能为空")
    @Size(max = 100, message = "参数键长度不能超过100个字符")
    private String paramKey;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称", required = true, example = "追缴超时时间")
    @NotBlank(message = "参数名称不能为空")
    @Size(max = 200, message = "参数名称长度不能超过200个字符")
    private String paramName;

    /**
     * 参数类型：STRING、JSON、NUMBER、BOOLEAN
     */
    @ApiModelProperty(value = "参数类型", required = true, example = "NUMBER", allowableValues = "STRING,JSON,NUMBER,BOOLEAN")
    @NotBlank(message = "参数类型不能为空")
    @Size(max = 20, message = "参数类型长度不能超过20个字符")
    private String paramType;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值", example = "30")
    private String defaultValue;

    /**
     * 验证规则（JSON格式）
     */
    @ApiModelProperty(value = "验证规则（JSON格式）", example = "{\"min\":1,\"max\":365}")
    private String validationRule;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "参数描述", example = "追缴业务超时时间（天）")
    @Size(max = 500, message = "参数描述长度不能超过500个字符")
    private String description;

    /**
     * 是否必填：0-否，1-是
     */
    @ApiModelProperty(value = "是否必填", required = true, example = "1", allowableValues = "0,1")
    @NotNull(message = "是否必填不能为空")
    private Integer isRequired;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序", example = "1")
    private Integer displayOrder;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", example = "1")
    @Version
    private Integer version;

    /**
     * 逻辑删除标记（不启用）
     */
    @TableField(exist = false)
    private Integer isDeleted;

    // ==================== 非数据库字段 ====================

    /**
     * 分类信息
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "分类信息")
    private BusinessParamCategory category;

    /**
     * 分类名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 完整分类路径
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "完整分类路径")
    private String fullCategoryPath;


	/**
	 * 校验参数值是否合法
	 * @param businessParamMeta 参数元数据
	 * @param paramValue 参数值
	 * @throws ServiceException 如果不合法，则抛出此异常
	 */
	public void verifyValueShouldValid(String paramValue) {
		BusinessParamType businessParamType = BusinessParamType.resolve(this.paramType);

		if (businessParamType == null) {
			throw new ServiceException("未知的参数类型：" + this.paramType);
		}

		boolean required = getIsRequired() == 1;
		boolean isValueBlank = Func.isBlank(paramValue);

		// 如果是必填项但值为空，则抛出异常
		if (required && isValueBlank) {
			throw new ServiceException("参数值必填，不能为空");
		}

		// 对于非空值，进行格式校验
		if (!isValueBlank && !businessParamType.isValidValue(paramValue)) {
			throw new ServiceException("参数值不合法, 错误参数值：" + paramValue);
		}

		// 根据元数据中的校验规则进行长度、大小等验证

	}

}
