package com.leliven.csc.domain.work.model;

import cn.hutool.core.lang.UUID;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 工单附件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderAttachment extends TenantDomain {

	/**
	 * 工单ID
	 */
	private Long workOrderId;
	/**
	 * 附件名称
	 */
	private String name;

	/**
	 * 附件类型
	 *
	 * <p>
	 * 标准附件：standard
	 * 音频附件：voice
	 * </p>
	 */
	private String type;
	/**
	 * 附件地址
	 */
	private String url;
	/**
	 * 附件大小
	 */
	private Long size;
	/**
	 * 附件时长  单位秒
	 * 当附件类型为流媒体时，需要传入时长
	 */
	private Integer duration;
	/**
	 * 附件描述
	 */
	private String description;

	public WorkOrderAttachment(String type, String url) {
		this.name = UUID.fastUUID().toString();
		this.type = type;
		this.url = url;
	}

	public static WorkOrderAttachment createStandard(String url) {
		return new WorkOrderAttachment("standard", url);
	}

	public WorkOrderAttachment belongTo(Long workOrderId) {
		this.workOrderId = workOrderId;
		return this;
	}

	public WorkOrderAttachment belongToTenant(String tenantId) {
		setTenantId(tenantId);
		return this;
	}



}
