package com.leliven.csc.domain.work.model;

import com.leliven.csc.domain.work.model.valueobject.Processor;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderOperationMode;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderProcessOperation;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.tool.utils.Func;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单流程领域对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderProcess extends TenantDomain {

	/**
	 * 工单ID
	 */
	private Long orderId;
	/**
	 * 流程描述
	 */
	private String flowDesc;
	/**
	 * 步骤
	 */
	private Integer step;
	/**
	 * 处理方式
	 */
	private String processMethod;
	/**
	 * 处理描述
	 */
	private String processDesc;
	/**
	 * 当前处理人
	 */
	private Set<Processor> currentProcessors;
	/**
	 * 上一个处理人
	 */
	private Processor lastProcessor;
	/**
	 * 下一个处理人列表
	 */
	private Set<Processor> nextProcessors;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 操作值
	 */
	private WorkOrderProcessOperation operation;
	/**
	 * 操作模式
	 */
	private WorkOrderOperationMode operationMode;
	/**
	 * 操作描述
	 */
	private String operationDesc;
	/**
	 * 附件地址
	 */
	private List<String> attachmentAddress;

	/**
	 * 催单次数
	 */
	private Integer reminderNum;
	/**
	 * 催单时间
	 */
	private LocalDateTime reminderTime;


	/**
	 * 构造函数
	 *
	 * @param orderId 工单ID
	 */
	public WorkOrderProcess(Long orderId) {
		this.orderId = orderId;
	}

	public WorkOrderProcess(WorkOrderProcessOperation operation) {
		this.operation = operation;
	}

	/**
	 * 创建工单流程
	 *
	 * @return 工单流程
	 */
	public static WorkOrderProcess create() {
		return new WorkOrderProcess();
	}

	/**
	 * 创建工单流程
	 *
	 * @param orderId 工单ID
	 * @return 工单流程
	 */
	public static WorkOrderProcess create(Long orderId) {
		return new WorkOrderProcess(orderId);
	}

	/**
	 * 创建工单流程
	 *
	 * @param operation 工单流程操作类型
	 * @return 工单流程
	 */
	public static WorkOrderProcess create(WorkOrderProcessOperation operation) {
		return new WorkOrderProcess(operation);
	}

	/**
	 * 创建开始流程
	 *
	 * @param currentProcessor 当前处理人
	 * @return 工单流程
	 */
	public static WorkOrderProcess createStartProcess(Processor currentProcessor) {
		return new WorkOrderProcess().startProcess(currentProcessor);
	}

	/**
	 * 设置工单ID
	 *
	 * @param orderId 工单ID
	 * @return 工单流程
	 */
	public WorkOrderProcess belongTo(Long orderId) {
		this.orderId = orderId;
		return this;
	}

	/**
	 * 设置租户
	 * <p>
	 * 如果当前租户为空，则设置租户
	 *
	 * @param tenantId 租户
	 * @return 工单流程
	 */
	public WorkOrderProcess belongToTenantIfBlank(String tenantId) {
		if (Func.isBlank(this.tenantId)) {
			this.setTenantId(tenantId);
		}
		return this;
	}

	/**
	 * 关闭流程
	 *
	 * @return 工单流程
	 */
	public WorkOrderProcess close() {
		this.operation(WorkOrderProcessOperation.CLOSE);
		this.generateFlowDesc(this.operation.getValue());
		return this;
	}

	/**
	 * 添加当前处理人
	 *
	 * @param processor 当前处理人
	 * @return 工单流程
	 */
	public WorkOrderProcess addCurrentProcessor(Processor processor) {
		addCurrentProcessors(Collections.singleton(processor));
		return this;
	}

	/**
	 * 添加当前处理人列表
	 *
	 * @param processors 当前处理人列表
	 * @return 工单流程
	 */
	public WorkOrderProcess addCurrentProcessors(Set<Processor> processors) {
		if (Func.isEmpty(this.currentProcessors)) {
			this.currentProcessors = new HashSet<>();
		}
		this.currentProcessors.addAll(processors);
		return this;
	}

	/**
	 * 下一个处理人列表
	 *
	 * @param processors 下一个处理人列表
	 * @return 工单流程
	 */
	public WorkOrderProcess addNextProcessors(Collection<Processor> processors) {
		if (Func.isEmpty(this.nextProcessors)) {
			this.nextProcessors = new HashSet<>();
		}
		this.nextProcessors.addAll(processors);
		return this;
	}

	/**
	 * 添加附件地址
	 *
	 * @param attachmentAddress 附件地址
	 * @return 工单流程
	 */
	public WorkOrderProcess addAttachmentAddress(List<String> attachmentAddress) {
		if (Func.isEmpty(attachmentAddress)) {
			return this;
		}
		if (Func.isEmpty(this.attachmentAddress)) {
			this.attachmentAddress = new ArrayList<>();
		}
		this.attachmentAddress.addAll(attachmentAddress);
		return this;
	}

	/**
	 * 设置流程操作
	 *
	 * @param operation 操作 {@link WorkOrderProcessOperation}
	 * @return 工单流程
	 */
	public WorkOrderProcess operation(WorkOrderProcessOperation operation) {
		this.operationDesc = operation.getValue();
		this.status = operation.getKey();
		this.operation = operation;
		return this;
	}

	/**
	 * 设置操作模式
	 *
	 * @param operationMode 操作模式 {@link WorkOrderOperationMode}
	 * @return 工单流程
	 */
	public WorkOrderProcess operationMode(WorkOrderOperationMode operationMode) {
		this.operationMode = operationMode;
		return this;
	}

	/**
	 * 设置流程描述
	 *
	 * @return 工单流程
	 */
	public WorkOrderProcess generateFlowDesc() {
		switch (this.operation) {
			case ASSIGN:
			case TRANSFER:
				this.flowDesc = String.format(this.operation.getFlowDesc(), Func.join(nextProcessorNames(), "、"));
				break;
			default:
				this.flowDesc = this.operation.getFlowDesc();
				break;
		}

		return this;
	}

	/**
	 * 设置流程描述
	 *
	 * @param flowDesc 流程描述
	 * @return 工单流程
	 */
	public WorkOrderProcess generateFlowDesc(String flowDesc) {
		this.flowDesc = flowDesc;
		return this;
	}

	/**
	 * 设置步骤
	 *
	 * @param step 步骤
	 * @return 工单流程
	 */
	public WorkOrderProcess step(Integer step) {
		this.step = step;
		return this;
	}

	/**
	 * 获取当前处理人Ids
	 *
	 * @return 当前处理人Ids
	 */
	public List<Long> currentProcessorIds() {
		return Optional.ofNullable(this.currentProcessors)
			.map(p -> p.stream().map(Processor::getId).collect(Collectors.toList()))
			.orElseGet(ArrayList::new);
	}

	/**
	 * 获取下一个处理人Ids
	 *
	 * @return 下一个处理人Ids
	 */
	public List<Long> nextProcessorIds() {
		return Optional.ofNullable(this.nextProcessors)
			.map(p -> p.stream().map(Processor::getId).collect(Collectors.toList()))
			.orElseGet(ArrayList::new);
	}

	/**
	 * 获取下一个处理人名称
	 *
	 * @return 下一个处理人Ids
	 */
	public List<String> nextProcessorNames() {
		return Optional.ofNullable(this.nextProcessors)
			.map(p -> p.stream().map(Processor::getName).collect(Collectors.toList()))
			.orElseGet(ArrayList::new);
	}

	/**
	 * 开始流程
	 *
	 * @param currentProcessor 当前处理人
	 * @return 工单流程
	 */
	private WorkOrderProcess startProcess(Processor currentProcessor) {
		this.step = 1;
		this.reminderNum = 0;
		this.addCurrentProcessor(currentProcessor);
		this.operation(WorkOrderProcessOperation.CREATE);
		this.generateFlowDesc(this.operation.getValue());
		return this;
	}

	public void verifyOperationModeShouldNotNull() {
		ObjectValidator.requireNonNull(this.operationMode, "操作模式不能为空");
	}

	public void verifyCurrentProcessorsShouldNotEmpty() {
		ObjectValidator.requireNotEmpty(this.currentProcessors, "获取当前处理人信息失败");
	}

	public void verifyNextProcessorsShouldNotEmpty() {
		ObjectValidator.requireNotEmpty(this.nextProcessors, "获取处理人信息失败");
	}

	public boolean isNextProcessorUnspecified() {
		return Func.isEmpty(this.nextProcessors);
	}
}
