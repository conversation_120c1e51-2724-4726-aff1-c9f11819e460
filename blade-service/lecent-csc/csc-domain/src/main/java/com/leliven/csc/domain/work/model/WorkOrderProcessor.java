package com.leliven.csc.domain.work.model;

import com.leliven.csc.domain.work.model.valueobject.WorkOrderStatus;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 工单处理人领域对象
 *
 * <p>表示工单处理人相关的领域信息</p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderProcessor extends TenantDomain {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单ID
	 */
	private Long orderId;
	/**
	 * 处理人ID
	 */
	private Long processorId;


	public WorkOrderProcessor(Long orderId, Long processorId, String tenantId) {
		this.orderId = orderId;
		this.tenantId = tenantId;
		this.processorId = processorId;
	}

	public WorkOrderProcessor changeStatus(WorkOrderStatus status) {
		this.status = status.getKey();
		return this;
	}

	public boolean isClosed() {
		return WorkOrderStatus.CLOSE.getKey().equals(this.status);
	}
}
