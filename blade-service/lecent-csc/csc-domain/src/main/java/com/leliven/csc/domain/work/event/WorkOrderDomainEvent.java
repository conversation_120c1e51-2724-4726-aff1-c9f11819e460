package com.leliven.csc.domain.work.event;

import com.leliven.csc.domain.work.model.WorkOrder;
import com.leliven.ddd.core.event.AbstractDomainEvent;

/**
 * 工单领域事件
 *
 * <AUTHOR>
 */
public class WorkOrderDomainEvent extends AbstractDomainEvent<WorkOrder, WorkOrderDomainEventType> {

	/**
	 * Create a new DomainEvent.
	 *
	 * @param source    the object on which the event initially occurred (never {@code null})
	 * @param eventType domain event type
	 * @param payload   the payload object (never {@code null})
	 */
	public WorkOrderDomainEvent(Object source, WorkOrderDomainEventType eventType, WorkOrder payload) {
		super(source, eventType, payload);
	}
}
