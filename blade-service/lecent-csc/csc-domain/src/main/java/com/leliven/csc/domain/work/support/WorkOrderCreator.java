package com.leliven.csc.domain.work.support;

import cn.hutool.core.lang.UUID;
import com.leliven.csc.domain.work.WorkOrderConfigRepositoryI;
import com.leliven.csc.domain.work.WorkOrderRepositoryI;
import com.leliven.csc.domain.work.model.WorkOrder;
import com.leliven.csc.domain.work.model.WorkOrderConfig;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderConstant;
import com.leliven.csc.domain.work.support.validator.WorkOrderConfigValidator;
import com.leliven.csc.domain.work.support.validator.WorkOrderValidator;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tool.utils.Func;

/**
 * 工单创建器
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class WorkOrderCreator {

	private final RedisLockClient redisLockClient;
	private final WorkOrderValidator orderValidator;
	private final WorkOrderRepositoryI orderRepository;
	private final WorkOrderConfigValidator configValidator;
	private final WorkOrderConfigRepositoryI configRepository;

	/**
	 * 创建工单
	 *
	 * @param order 工单
	 */
	public void create(WorkOrder order) {
		this.redisLockClient.lockFair(
			lockKey(order),
			3L,
			5L,
			() -> processCreate(order)
		);
	}

	/**
	 * 创建工单
	 *
	 * @param order 工单
	 * @return 是否创建成功
	 */
	private boolean processCreate(WorkOrder order) {
		WorkOrderConfig config = getConfig(order.getConfigId());
		// 校验新增工单领域规则
		this.orderValidator.verifyAddRule(order);
		// 新增工单领域逻辑
		order.create(this.orderRepository.getCode(), config);

		return true;
	}

	/**
	 * 获取工单配置
	 *
	 * @param configId 配置ID
	 * @return 配置
	 */
	private WorkOrderConfig getConfig(Long configId) {
		// 获取工单配置
		WorkOrderConfig config = this.configRepository.getById(configId);
		// 校验工单配置是否存在
		this.configValidator.verifyConfigShouldNotNull(config);

		return config;
	}

	/**
	 * 获取锁key
	 *
	 * @param order 工单
	 * @return key
	 */
	private String lockKey(WorkOrder order) {
		String uniqueKey = order.getBizCode();
		if (Func.isBlank(uniqueKey)) {
			uniqueKey = UUID.fastUUID().toString();
		}

		return WorkOrderConstant.Lock.CREATE + ':' + uniqueKey;
	}


}
