package com.leliven.csc.domain.shared.support;

import com.leliven.csc.domain.shared.ParkingDutyPersonnelRepositoryI;
import com.leliven.csc.domain.shared.model.ParkingDutyPersonnel;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 停车值班人员领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class ParkingDutyPersonnelDomainService {

	private final ParkingDutyPersonnelRepositoryI parkingDutyPersonnelRepository;

	/**
	 * 根据车场ID获取值班人员
	 *
	 * @param parklotId 车场ID
	 * @return 值班人员
	 */
	public List<ParkingDutyPersonnel> loadParkingDutyPersonnel(Long parklotId) {
		return parkingDutyPersonnelRepository.loadParkingDutyPersonnel(parklotId);
	}

	/**
	 * 根据车场ID,值班组获取值班人员
	 *
	 * @param parklotId 车场ID
	 * @return 值班人员
	 */
	public List<ParkingDutyPersonnel> loadParkingDutyPersonnel(Long parklotId, List<String> groups) {
		return parkingDutyPersonnelRepository.loadParkingDutyPersonnel(parklotId, groups);
	}
}
