package com.leliven.csc.domain.work.model.rule;

import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.tool.utils.Func;

/**
 * 工单规则
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderRule extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 规则名称
	 */
	protected String name;
	/**
	 * 规则描述
	 */
	protected String description;
	/**
	 * 规则类型
	 */
	protected WorkOrderRuleType type;
	/**
	 * 规则配置
	 */
	protected WorkOrderRuleConfigurable config;

	public void resolveConfig(String configJson) {
		verifyTypeShouldNotNull();
		ObjectValidator.requireNotBlank(configJson, "规则配置JSON不能为空");

		this.config = Func.readJson(configJson, type.getRuleConfigClass());
	}

	public void verify() {
		verifyConfigShouldNotNull();
		verifyNameShouldNotBlank();
		verifyTypeShouldNotNull();
	}

	public void verifyConfigShouldNotNull() {
		ObjectValidator.requireNonNull(this.config, "规则配置不能为空");
	}

	public void verifyNameShouldNotBlank() {
		ObjectValidator.requireNotBlank(this.name, "规则名称不能为空");
	}

	public void verifyTypeShouldNotNull() {
		ObjectValidator.requireNonNull(this.type, "规则类型不能为空");
	}

}
