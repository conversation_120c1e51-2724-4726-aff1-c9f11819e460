package com.leliven.csc.domain.work.support;

import com.leliven.csc.domain.work.WorkOrderRuleRepositoryI;
import com.leliven.csc.domain.work.model.rel.WorkOrderConfigRuleRel;
import com.leliven.csc.domain.work.model.rule.WorkOrderRule;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tenant.annotation.TenantIgnore;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 工单指派规则
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class WorkOrderRuleDomainService {

	private final WorkOrderRuleRepositoryI workOrderRuleRepository;

	public Optional<WorkOrderRule> getRule(Long ruleId) {
		return Optional.ofNullable(workOrderRuleRepository.getById(ruleId));
	}

	@TenantIgnore
	public List<WorkOrderRule> getRuleByConfigId(Long configId, String tenantId) {
		return workOrderRuleRepository.getByConfigId(configId, tenantId).stream()
			.map(WorkOrderConfigRuleRel::getAssignRuleId)
			.map(workOrderRuleRepository::getById)
			.collect(Collectors.toList());
	}

}
