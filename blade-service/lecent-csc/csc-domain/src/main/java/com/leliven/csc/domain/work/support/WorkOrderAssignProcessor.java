package com.leliven.csc.domain.work.support;

import com.leliven.csc.domain.work.WorkOrderConfigRepositoryI;
import com.leliven.csc.domain.work.WorkOrderRepositoryI;
import com.leliven.csc.domain.work.event.WorkOrderDomainEvent;
import com.leliven.csc.domain.work.model.WorkOrder;
import com.leliven.csc.domain.work.model.WorkOrderAssignContext;
import com.leliven.csc.domain.work.model.WorkOrderConfig;
import com.leliven.csc.domain.work.model.WorkOrderProcess;
import com.leliven.csc.domain.work.model.rule.assign.WorkOrderAssignRule;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderConstant;
import com.leliven.csc.domain.work.support.validator.WorkOrderValidator;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.redis.lock.RedisLock;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.List;

import static com.leliven.csc.domain.work.event.WorkOrderDomainEventType.ASSIGN;

/**
 * 工单指派处理器
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class WorkOrderAssignProcessor {

	private final WorkOrderValidator workOrderValidator;
	private final WorkOrderRepositoryI workOrderRepository;
	private final WorkOrderConfigRepositoryI workOrderConfigRepository;
	private final WorkOrderRuleDomainService ruleDomainService;

	/**
	 * 指派工单
	 *
	 * @param id 工单ID
	 */
	@RedisLock(value = WorkOrderConstant.Lock.MODIFY, param = "#id", waitTime = 3L, leaseTime = 5L)
	public void assign(Long id, WorkOrderProcess process) {
		WorkOrder workOrder = this.workOrderRepository.getById(id);
		this.assign(new WorkOrderAssignContext(workOrder, process));
	}

	/**
	 * 指派工单
	 *
	 * @param workOrder 工单
	 */
	@RedisLock(value = WorkOrderConstant.Lock.MODIFY, param = "#workOrder.id", waitTime = 3L, leaseTime = 5L)
	public void assign(WorkOrder workOrder, WorkOrderProcess process) {
		this.assign(new WorkOrderAssignContext(workOrder, process));
	}

	/**
	 * 指派工单
	 *
	 * @param context 工单指派上下文 {@link WorkOrderAssignContext}
	 */
	@RedisLock(value = WorkOrderConstant.Lock.MODIFY, param = "#workOrder.id", waitTime = 3L, leaseTime = 5L)
	public void assign(WorkOrderAssignContext context) {
		// 验证工单
		this.workOrderValidator.verifyWorkOrderShouldNotNull(context.getWorkOrder());
		// 处理工单指派
		handleAssign(context);
		// 更新工单
		this.workOrderRepository.update(context.getWorkOrder());
		// 发布工单指派事件
		SpringDomainEventPublisher.publish(new WorkOrderDomainEvent(this, ASSIGN, context.getWorkOrder()));
	}

	/**
	 * 处理工单指派
	 *
	 * @param context 工单指派上下文 {@link WorkOrderAssignContext}
	 */
	private void handleAssign(WorkOrderAssignContext context) {
		WorkOrderConfig config = workOrderConfigRepository.getById(context.getWorkOrder().getConfigId());

		// 查询指派规则
		List<WorkOrderAssignRule> rules = BeanUtil.copy(this.ruleDomainService.getRuleByConfigId(config.getId(), context.getWorkOrder().getTenantId()),
			WorkOrderAssignRule.class);
		LecentAssert.notEmpty(rules, "工单指派失败，原因：工单配置[id:"+ config.getId() +"]未指定人员指派规则，请联系车场管理员");

		// 处理指派规则逻辑
		rules.forEach(workOrderRule -> workOrderRule.assign(context));

		// 指派
		context.assign();
	}

}
