package com.leliven.csc.domain.work.support;

import com.leliven.csc.domain.shared.SystemUserRepositoryI;
import com.leliven.csc.domain.shared.model.ParkingDutyPersonnel;
import com.leliven.csc.domain.shared.support.ParkingDutyPersonnelDomainService;
import com.leliven.csc.domain.work.model.valueobject.Processor;
import com.leliven.csc.domain.work.support.interfaces.PersonnelDomainServiceI;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 人员领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class PersonnelDomainService implements PersonnelDomainServiceI {

	private final SystemUserRepositoryI systemUserRepository;
	private final ParkingDutyPersonnelDomainService parkingDutyPersonnelDomainService;

	/**
	 * 获取处理人
	 */
	@Override
	public Set<Processor> getProcessorsByDutyGroup(Long parklotId, List<String> dutyGroups) {
		List<ParkingDutyPersonnel> parkingDutyPersonnelList =
			this.parkingDutyPersonnelDomainService.loadParkingDutyPersonnel(parklotId, dutyGroups);
		return parkingDutyPersonnelList.stream()
			.map(parkingDutyPersonnel -> new Processor(parkingDutyPersonnel.getId(), parkingDutyPersonnel.getName()))
			.collect(Collectors.toSet());
	}

	/**
	 * 获取处理人
	 */
	@Override
	public Set<Processor> getProcessorsBySystemRoles(List<String> roles, String tenantId) {
		List<ParkingDutyPersonnel> parkingDutyPersonnelList = this.systemUserRepository.listUserByRoles(roles, tenantId);

		return parkingDutyPersonnelList.stream()
			.map(parkingDutyPersonnel -> new Processor(parkingDutyPersonnel.getId(), parkingDutyPersonnel.getName()))
			.collect(Collectors.toSet());
	}
}
