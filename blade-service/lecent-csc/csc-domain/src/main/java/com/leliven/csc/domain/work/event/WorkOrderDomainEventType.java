package com.leliven.csc.domain.work.event;

import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单领域事件类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WorkOrderDomainEventType implements DomainEventType {

	CREATED("新增工单"),
    ASSIGN("指派工单"),
	TRANSFERRED("转交工单"),
	COMMENTED("评论工单"),
	CLOSED("关闭工单"),
	;


	private final String description;
}
