package com.leliven.csc.domain.work.model;

import com.leliven.csc.domain.work.model.valueobject.WorkOrderCategory;
import com.leliven.ddd.core.model.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.DateUtil;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 工单配置表领域对象
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderConfig extends BaseDomain {

	/**
	 * 工单类别
	 * {@link WorkOrderCategory}
	 */
	private String category;
	/**
	 * 工单类型（由工单类别决定）
	 * {@link WorkOrderType}
	 */
	private String type;
	/**
	 * 紧急级别
	 * <p>
	 * 1-普通，2-重要，3-紧急，4-非常紧急
	 */
	private Integer level;
	/**
	 * 现象
	 */
	private String display;
	/**
	 * 消息模板id
	 */
	private Long templateId;
	/**
	 * 内容模板
	 */
	WorkOrderContentTemplate contentTemplate;
	/**
	 * 预响应时间（单位：分钟）
	 */
	private Integer preRespTime;
	/**
	 * 预完成时间（单位：分钟）
	 */
	private Integer preCompTime;
	/**
	 * 操作按钮类型
	 */
	private String action;
	/**
	 * 分发规则id
	 */
	private Long assignRuleId;

	/**
	 * 计算预响应时间
	 * <p>
	 * 预响应时间 = 开始时间 + 预响应分钟数
	 *
	 * @param startTime 开始时间
	 * @return 预响应时间
	 */
	public Date calculatePreResponseTime(Date startTime) {
		return calculatePreTime(startTime, this.preRespTime);
	}

	/**
	 * 计算预完成时间
	 * <p>
	 * 预完成时间 = 开始时间 + 预完成分钟数
	 *
	 * @param startTime 开始时间
	 * @return 预完成时间
	 */
	public Date calculatePreCompleteTime(Date startTime) {
		return calculatePreTime(startTime, this.preCompTime);
	}

	/**
	 * 计算预计时间
	 * <p>
	 * 预计时间 = 开始时间 + 预计分钟数
	 *
	 * @param startTime 开始时间
	 * @param preTime 预完成时间
	 * @return 预完成时间
	 */
	private Date calculatePreTime(Date startTime, Integer preTime) {
		if (Objects.nonNull(startTime) && Objects.nonNull(preTime)) {
			return DateUtil.plusMinutes(startTime, preTime);
		}

		return null;
	}

	/**
	 * 解析内容模板
	 *
	 * @param contextMap 上下文
	 * @return 解析后的内容
	 */
	public String parseContentTemplate(Map<String, Object> contextMap) {
		configDefaultTemplateParamIfNecessary(contextMap);
		return contentTemplate.parseContent(contextMap);
	}

	/**
	 * 配置默认的模板参数
	 *
	 * <p>
	 * 如果内容模版参数中包含 {@code display} 参数，且传入的参数上下文中不包含 {@code display} 参数，
	 * 则将 {@link WorkOrderConfig#display} 参数添加到上下文中
	 * </p>
	 * @param contextMap 默认参数
	 */
	private void configDefaultTemplateParamIfNecessary(Map<String, Object> contextMap) {
		if (contentTemplate.containsTemplateParam("display")) {
			contextMap.computeIfAbsent( "display", k -> display);
		}
	}



}
