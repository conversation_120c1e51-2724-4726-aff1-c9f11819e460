package com.leliven.csc.domain.work.model;

import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单类型领域对象
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderType extends TenantDomain {

	/**
	 * 父类型编号
	 */
	private String parentCode;
	/**
	 * 编号
	 */
	private String code;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 设备类型
	 */
	private Integer deviceType;
}
