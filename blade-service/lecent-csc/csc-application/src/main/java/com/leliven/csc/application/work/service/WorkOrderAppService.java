package com.leliven.csc.application.work.service;

import com.leliven.csc.application.work.assembler.WorkOrderAssembler;
import com.leliven.csc.application.work.assembler.WorkOrderProcessAssembler;
import com.leliven.csc.application.work.dto.cmd.WorkOrderAssignCmd;
import com.leliven.csc.application.work.dto.cmd.WorkOrderCommentCmd;
import com.leliven.csc.application.work.dto.cmd.WorkOrderFreeCmd;
import com.leliven.csc.application.work.dto.cmd.WorkOrderTransferCmd;
import com.leliven.csc.client.work.dto.WorkOrderProcessDTO;
import com.leliven.csc.client.work.dto.cmd.WorkOrderAddBatchCmd;
import com.leliven.csc.client.work.dto.cmd.WorkOrderAddCmd;
import com.leliven.csc.client.work.dto.cmd.WorkOrderClientCloseCmd;
import com.leliven.csc.client.work.dto.cmd.WorkOrderCloseCmd;
import com.leliven.csc.domain.work.WorkOrderRepositoryI;
import com.leliven.csc.domain.work.event.WorkOrderDomainEvent;
import com.leliven.csc.domain.work.event.WorkOrderDomainEventType;
import com.leliven.csc.domain.work.exception.WorkOrderNotFoundException;
import com.leliven.csc.domain.work.model.WorkOrder;
import com.leliven.csc.domain.work.model.WorkOrderProcess;
import com.leliven.csc.domain.work.model.valueobject.Processor;
import com.leliven.csc.domain.work.support.WorkOrderAssignProcessor;
import com.leliven.csc.domain.work.support.WorkOrderCreator;
import com.leliven.csc.domain.work.support.WorkOrderDomainService;
import com.leliven.csc.gateway.rpc.parking.ParkingRpcGateway;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;
import java.util.function.Function;

/**
 * 工单应用服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkOrderAppService implements WorkOrderAppServiceI {

	private final WorkOrderCreator workOrderCreator;
	private final ParkingRpcGateway parkingRpcGateway;
    private final WorkOrderRepositoryI workOrderRepository;
	private final WorkOrderDomainService workOrderDomainService;
	private final WorkOrderAssignProcessor workOrderAssignProcessor;

    @Override
	@Transactional(rollbackFor = Exception.class)
	public boolean add(WorkOrderAddCmd cmd) {
		// 转换为领域对象
		WorkOrder workOrder = WorkOrderAssembler.addCmd2DomainObject(cmd);
		// 新增工单领域逻辑
		this.workOrderCreator.create(workOrder);
		// 保存工单
		this.workOrderRepository.save(workOrder);
		cmd.setCode(workOrder.getCode());
		// 发布工单新增事件
		SpringDomainEventPublisher.publish(new WorkOrderDomainEvent(this, WorkOrderDomainEventType.CREATED, workOrder));
		return true;
	}

	/**
	 * 批量添加工单
	 *
	 * @param cmd 工单添加命令
	 * @return 添加结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addBatch(WorkOrderAddBatchCmd cmd) {
		// 转换为领域对象列表
		List<WorkOrder> workOrders = WorkOrderAssembler.fromDTOList(cmd.getWorkOrders());
		// 新增工单领域逻辑
		workOrders.forEach(this.workOrderCreator::create);
		// 保存工单
		this.workOrderRepository.saveBatch(workOrders);
		// 发布工单新增事件
		workOrders.forEach(workOrder ->
			SpringDomainEventPublisher.publish(new WorkOrderDomainEvent(this, WorkOrderDomainEventType.CREATED, workOrder)));
		return true;
	}

	@TransactionalEventListener(
		classes = WorkOrderDomainEvent.class,
		condition = "#event.eventType == T(com.leliven.csc.domain.work.event.WorkOrderDomainEventType).CREATED",
		phase = TransactionPhase.BEFORE_COMMIT
	)
	public void assign(WorkOrderDomainEvent event) {
		WorkOrder workOrder = event.getPayload();
		// 创建工单流程
		WorkOrderProcess process = WorkOrderProcess.create()
			.addCurrentProcessor(new Processor(workOrder.getSponsor()))
			.addNextProcessors(workOrder.getProcessors());
		// 指派工单
		this.workOrderAssignProcessor.assign(workOrder, process);
	}

	/**
	 * 指派工单
	 *
	 * @param cmd 指派工单命令
	 */
	@Override
	public boolean assign(WorkOrderAssignCmd cmd) {
		// 转换为领域对象
		WorkOrderProcess process = WorkOrderProcessAssembler.toDomainObject(cmd.getProcess());
		// 指派工单
		this.workOrderAssignProcessor.assign(cmd.getId(), process);

		return true;
	}

	/**
	 * 转交工单
	 *
	 * @param cmd 转交工单命令
	 * @return ture-转交成功 false-转交失败
	 */
	@Override
	public boolean transfer(WorkOrderTransferCmd cmd) {
		// 转换为领域对象
		WorkOrderProcess process = WorkOrderProcessAssembler.toDomainObject(cmd.getProcess());
		// 转派工单
		WorkOrder workOrder = this.workOrderDomainService.transfer(cmd.getId(), process);
		// 发布工单转派事件
		SpringDomainEventPublisher.publish(
			new WorkOrderDomainEvent(this, WorkOrderDomainEventType.TRANSFERRED, workOrder));
		return true;
	}

	@Override
	public boolean comment(WorkOrderCommentCmd cmd) {
		// 转换为领域对象
		WorkOrderProcess process = WorkOrderProcessAssembler.toDomainObject(cmd.getProcess());
		// 评论工单
		WorkOrder workOrder = this.workOrderDomainService.comment(cmd.getId(), process);
		// 发布工单评论事件
		SpringDomainEventPublisher.publish(
			new WorkOrderDomainEvent(this, WorkOrderDomainEventType.COMMENTED, workOrder));
		return false;
	}

	/**
	 * 通过业务编号关闭工单
	 *
	 * @param cmd 关闭工单命令
	 */
	@Override
	public void close(WorkOrderClientCloseCmd cmd) {
		// 通过业务编号获取工单ID
		Long id = this.workOrderRepository.getOrderIdByBizCode(cmd.getBizCode());
		// 校验工单ID是否存在
		ObjectValidator.requireNonNull(id, WorkOrderNotFoundException::new);
		// 设置工单ID
		cmd.setId(id);
		// 根据工单ID关闭工单
		close((WorkOrderCloseCmd) cmd);
	}

	/**
	 * 关闭工单
	 *
	 * @param cmd 关闭工单命令
	 * @return 关闭结果
	 */
	@Override
	public boolean close(WorkOrderCloseCmd cmd) {
		return actionIfClosed(cmd, workOrder -> true);
	}

	/**
	 * 释放工单
	 *
	 * @param cmd 释放工单命令
	 * @return 释放结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean free(WorkOrderFreeCmd cmd) {
		// 设置工单处理模式为手动
		cmd.getWorkOrderProcess().setMode(WorkOrderProcessDTO.MODE_MANUAL);
		// 关闭工单
		return this.actionIfClosed(cmd, workOrder -> {
			if (Boolean.TRUE.equals(cmd.getDeleteParkingOrder())) {
				String parkingOrderId = workOrder.getExtentParams().get("parkingOrderId");
				LecentAssert.notBlank(parkingOrderId, "停车记录id为空");
				String remark = StringUtil.format("释放工单[{}]清理停车记录", cmd.getId());
				boolean cleaned = this.parkingRpcGateway.cleanParkingOrder(Func.toLong(parkingOrderId), remark);
				if (!cleaned) {
					log.warn("关闭工单[{}]清理停车记录失败", workOrder.getId());
				}
			}
			return true;
		});
	}

	/**
	 * 关闭工单
	 * <p>
	 * 成功后会执行action中的操作，失败则抛出异常
	 *
	 * @param cmd 关闭工单命令
	 * @param action 关闭后执行的操作 {@link Function} T:工单 R:操作结果
	 * @return 关闭工单后执行的操作结果
	 */
	private <R> R actionIfClosed(WorkOrderCloseCmd cmd, Function<WorkOrder, R> action) {
		// 校验操作是否为空
		ObjectValidator.requireNonNull(action);
		// 校验工单ID
		ObjectValidator.requireNonNull(cmd.getId(), "工单ID不能为空");
		// 转换为流程领域对象
		WorkOrderProcess process = WorkOrderProcessAssembler.toDomainObject(cmd.getWorkOrderProcess());
		// 关闭工单
		WorkOrder workOrder = this.workOrderDomainService.close(cmd.getId(), process, cmd.getType());
		// 发布工单关闭事件
		SpringDomainEventPublisher.publish(new WorkOrderDomainEvent(this, WorkOrderDomainEventType.CLOSED, workOrder));

		return action.apply(workOrder);
	}
}
