package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.rel.WorkOrderConfigRuleRel;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderConfigRuleRelDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springframework.stereotype.Component;

/**
 * 工单配置关联规则
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Component
public class WorkOrderConfigRuleRelConverter extends AbstractDomainEntityConverter<WorkOrderConfigRuleRel, WorkOrderConfigRuleRelDO> {

    @Override
    protected WorkOrderConfigRuleRel doFromDO(WorkOrderConfigRuleRelDO dataObject) {
        WorkOrderConfigRuleRel domainObject = new WorkOrderConfigRuleRel();
        JavaUtils.INSTANCE.acceptIfNotNull(dataObject.getConfigId(), domainObject::setConfigId)
            .acceptIfNotNull(dataObject.getAssignRuleId(), domainObject::setAssignRuleId);
        return domainObject;
    }

    @Override
    protected WorkOrderConfigRuleRelDO doToDO(WorkOrderConfigRuleRel domainObject) {
        WorkOrderConfigRuleRelDO dataObject = new WorkOrderConfigRuleRelDO();
        JavaUtils.INSTANCE.acceptIfNotNull(domainObject.getConfigId(), dataObject::setConfigId)
            .acceptIfNotNull(domainObject.getAssignRuleId(), dataObject::setAssignRuleId);
        return dataObject;
    }
}
