package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrder;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderDO;
import com.leliven.csc.domain.work.model.valueobject.*;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;

/**
 * 工单领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Component
public class WorkOrderConverter extends AbstractDomainEntityConverter<WorkOrder, WorkOrderDO> {

	@Override
	protected WorkOrder doFromDO(WorkOrderDO dataObject) {
		WorkOrder domainObject = new WorkOrder(dataObject.getId());
		JavaUtils.INSTANCE.acceptIfHasText(dataObject.getCode(), domainObject::setCode)
			.acceptIfNotNull(dataObject.getType(), domainObject::setType)
			.acceptIfNotNull(dataObject.getCategory(), domainObject::setCategory)
			.acceptIfNotNull(dataObject.getLevel(), domainObject::setLevel)
			.acceptIfNotNull(dataObject.getParkLotId(), domainObject::setParkLotId)
			.acceptIfHasText(dataObject.getParkLotName(), domainObject::setParkLotName)
			.acceptIfNotNull(dataObject.getPlaceId(), domainObject::setPlaceId)
			.acceptIfHasText(dataObject.getPlaceCode(), domainObject::setPlaceCode)
			.acceptIfNotNull(dataObject.getDeviceType(), domainObject::setDeviceType)
			.acceptIfHasText(dataObject.getDeviceAddress(), domainObject::setDeviceAddress)
			.acceptIfHasText(dataObject.getTitle(), domainObject::setTitle)
			.acceptIfHasText(dataObject.getDescription(), domainObject::setDescription)
			.acceptIfHasText(dataObject.getRemarks(), domainObject::setRemarks)
			.acceptIfHasText(dataObject.getProcessMethod(), domainObject::setProcessMethod)
			.acceptIfNotNull(dataObject.getHandleEndTime(), domainObject::setHandleEndTime)
			.acceptIfNotNull(dataObject.getPreRespTime(), domainObject::setPreRespTime)
			.acceptIfNotNull(dataObject.getPreCompTime(), domainObject::setPreCompTime)
			.acceptIfNotNull(dataObject.getResult(), domainObject::setResult)
			.acceptIfNotNull(dataObject.getOpRead(), domainObject::setOpRead)
			.acceptIfNotNull(dataObject.getConfigId(), domainObject::setConfigId)
			.acceptIfNotNull(dataObject.getCompleteMode(), m -> domainObject.setCompleteMode(WorkOrderOperationMode.resolve(m)))
			.acceptIfNotNull(dataObject.getTotalProcessingTime(), domainObject::setTotalProcessingTime)
			.acceptIfNotNull(dataObject.getAlarmId(), domainObject::setAlarmId)
			.acceptIfNotNull(dataObject.getBizCode(), domainObject::setBizCode)
			.acceptIfHasText(
				dataObject.getExtentParams(),
				e -> domainObject.setExtentParams(JsonUtil.readMap(e, String.class, String.class)))
			.acceptIfHasText(
				dataObject.getAttachmentAddress(),
				t -> domainObject.setAttachmentAddresses(Func.toStrList(t)))
			.acceptIfNotNull(
				dataObject.getSponsorId(),
				s -> domainObject.setSponsor(dataobject2Sponsor(dataObject)))
			.acceptIfHasText(
				dataObject.getProcessorIds(),
				ids -> domainObject.setProcessors(dataobject2processors(dataObject)))
			.acceptIfHasText(
				dataObject.getReaderIds(),
				ids -> domainObject.setReaders(dataobject2readers(dataObject)))
				;
		domainObject.setSource(dataobject2WorkOrderSource(dataObject));
		return domainObject;
	}

	@Override
	protected WorkOrderDO doToDO(WorkOrder domainObject) {
		WorkOrderDO dataobject = new WorkOrderDO();
		JavaUtils.INSTANCE.acceptIfHasText(domainObject.getCode(), dataobject::setCode)
			.acceptIfNotNull(domainObject.getType(), dataobject::setType)
			.acceptIfNotNull(domainObject.getCategory(), dataobject::setCategory)
			.acceptIfNotNull(domainObject.getLevel(), dataobject::setLevel)
			.acceptIfNotNull(domainObject.getParkLotId(), dataobject::setParkLotId)
			.acceptIfHasText(domainObject.getParkLotName(), dataobject::setParkLotName)
			.acceptIfNotNull(domainObject.getPlaceId(), dataobject::setPlaceId)
			.acceptIfHasText(domainObject.getPlaceCode(), dataobject::setPlaceCode)
			.acceptIfNotNull(domainObject.getDeviceType(), dataobject::setDeviceType)
			.acceptIfHasText(domainObject.getDeviceAddress(), dataobject::setDeviceAddress)
			.acceptIfNotNull(domainObject.getSource(), s -> workOrderSource2Dataobject(s, dataobject))
			.acceptIfHasText(domainObject.getTitle(), dataobject::setTitle)
			.acceptIfHasText(domainObject.getDescription(), dataobject::setDescription)
			.acceptIfHasText(domainObject.getRemarks(), dataobject::setRemarks)
			.acceptIfHasText(domainObject.getProcessMethod(), dataobject::setProcessMethod)
			.acceptIfNotNull(domainObject.getHandleEndTime(), dataobject::setHandleEndTime)
			.acceptIfNotNull(domainObject.getPreRespTime(), dataobject::setPreRespTime)
			.acceptIfNotNull(domainObject.getPreCompTime(), dataobject::setPreCompTime)
			.acceptIfNotNull(domainObject.getResult(), dataobject::setResult)
			.acceptIfNotNull(domainObject.getOpRead(), dataobject::setOpRead)
			.acceptIfNotNull(domainObject.getConfigId(), dataobject::setConfigId)
			.acceptIfNotNull(domainObject.getCompleteMode(), m -> dataobject.setCompleteMode(m.getValue()))
			.acceptIfNotNull(domainObject.getTotalProcessingTime(), dataobject::setTotalProcessingTime)
			.acceptIfNotNull(domainObject.getAlarmId(), dataobject::setAlarmId)
			.acceptIfNotNull(domainObject.getBizCode(), dataobject::setBizCode)
			.acceptIfNotNull(domainObject.getExtentParams(),
				e -> dataobject.setExtentParams(Func.toJson(e)))
			.acceptIfNotEmpty(
				domainObject.getAttachmentAddresses(),
				t -> dataobject.setAttachmentAddress(Func.join(t)))
			.acceptIfNotNull(domainObject.getSponsor(), s -> sponsor2DO(s, dataobject))
			.acceptIfCondition(
				Func.isNotEmpty(domainObject.getProcessors()),
				domainObject.getProcessors(),
				dataobject,
				this::processors2DO)
			.acceptIfCondition(
				Func.isNotEmpty(domainObject.getReaders()),
				domainObject.getReaders(),
				dataobject,
				this::readers2DO);

		return dataobject;
	}

	WorkOrderSource dataobject2WorkOrderSource(WorkOrderDO dataobject) {
		return new WorkOrderSource(dataobject.getSourceId(), WorkOrderOperationMode.resolve(dataobject.getSourceMode()));

	}

	Sponsor dataobject2Sponsor(WorkOrderDO workOrderDO) {
		return new Sponsor(workOrderDO.getSponsorId(), workOrderDO.getSponsorName());
	}

	Set<Reader> dataobject2readers(WorkOrderDO workOrderDO) {
		List<Long> ids = Func.toLongList(workOrderDO.getReaderIds());
		List<String> names = Func.toStrList(workOrderDO.getReaderNames());
		Set<Reader> readers = new HashSet<>(ids.size());
		for (int i = 0; i < ids.size(); i++) {
			readers.add(new Reader(ids.get(i), names.get(i)));
		}
		return readers;
	}

	Set<Processor> dataobject2processors(WorkOrderDO workOrderDO) {
		List<Long> ids = Func.toLongList(workOrderDO.getProcessorIds());
		List<String> names = Func.toStrList(workOrderDO.getProcessorIds());
		Set<Processor> processors = new HashSet<>(ids.size());
		for (int i = 0; i < ids.size(); i++) {
			processors.add(new Processor(ids.get(i), names.get(i)));
		}
		return processors;
	}

	void workOrderSource2Dataobject(WorkOrderSource source, WorkOrderDO dataobject) {
		dataobject.setSourceId(source.getChannel());
		dataobject.setSourceMode(source.getMode().getValue());
	}

	void sponsor2DO(Sponsor s, WorkOrderDO dataobject) {
		dataobject.setSponsorId(s.getId());
		dataobject.setSponsorName(s.getName());
	}

	void processors2DO(Set<Processor> processors, WorkOrderDO workOrderDO) {
		person2DO(processors, (personIds, personNames) -> {
			workOrderDO.setProcessorIds(Func.join(personIds));
			workOrderDO.setProcessorNames(Func.join(personNames));
		});
	}

	void readers2DO(Set<Reader> readers, WorkOrderDO workOrderDO) {
		person2DO(readers, (personIds, personNames) -> {
			workOrderDO.setReaderIds(Func.join(personIds));
			workOrderDO.setReaderNames(Func.join(personNames));
		});
	}

	void person2DO(Collection<? extends Person> persons, BiConsumer<List<Long>, List<String>> consumer) {
		List<Long> personIds = new ArrayList<>(persons.size());
		List<String> personNames = new ArrayList<>(persons.size());
		persons.forEach(person -> {
			personIds.add(person.getId());
			personNames.add(person.getName());
		});
		consumer.accept(personIds, personNames);
	}
}
