package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrderProcessor;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderProcessorDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springframework.stereotype.Component;

/**
 * 工单处理人转换器
 *
 * <p>负责将工单处理人的领域对象与数据对象之间进行转换</p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Component
public class WorkOrderProcessorConverter extends AbstractDomainEntityConverter<WorkOrderProcessor, WorkOrderProcessorDO> {

	@Override
	protected WorkOrderProcessor doFromDO(WorkOrderProcessorDO dataObject) {
		return new WorkOrderProcessor(dataObject.getOrderId(), dataObject.getProcessorId(), dataObject.getTenantId());
	}

	@Override
	protected WorkOrderProcessorDO doToDO(WorkOrderProcessor domainObject) {
		WorkOrderProcessorDO dataObject = new WorkOrderProcessorDO();
		JavaUtils.INSTANCE
			.acceptIfNotNull(domainObject.getOrderId(), dataObject::setOrderId)
			.acceptIfNotNull(domainObject.getProcessorId(), dataObject::setProcessorId);
		return dataObject;
	}
}


