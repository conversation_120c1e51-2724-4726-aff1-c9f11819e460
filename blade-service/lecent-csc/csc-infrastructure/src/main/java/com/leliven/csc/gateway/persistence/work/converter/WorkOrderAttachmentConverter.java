package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrderAttachment;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderAttachmentDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 工单附件转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderAttachmentConverter extends DomainEntityConverter<WorkOrderAttachment, WorkOrderAttachmentDO> {

	WorkOrderAttachmentConverter INSTANCE = Mappers.getMapper(WorkOrderAttachmentConverter.class);

	@Override
	@Mapping(target = "belongTo", ignore = true)
	WorkOrderAttachment fromDO(WorkOrderAttachmentDO dataObject);

	@Override
	WorkOrderAttachmentDO toDO(WorkOrderAttachment domainObject);

}
