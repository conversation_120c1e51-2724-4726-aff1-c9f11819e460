package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.rule.WorkOrderRule;
import com.leliven.csc.domain.work.model.rule.WorkOrderRuleConfigurable;
import com.leliven.csc.domain.work.model.rule.WorkOrderRuleType;
import com.leliven.csc.domain.work.support.factory.WorkOrderRuleFactory;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderRuleDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 工单规则转换器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class WorkOrderRuleConverter extends AbstractDomainEntityConverter<WorkOrderRule, WorkOrderRuleDO> {

	private final WorkOrderRuleFactory workOrderRuleFactory;

	@Override
	protected WorkOrderRule doFromDO(WorkOrderRuleDO dataObject) {
		WorkOrderRuleType ruleType = WorkOrderRuleType.resolve(dataObject.getType());
        return this.workOrderRuleFactory.getRule(ruleType, dataObject.getConfig(), rule -> {
			rule.setName(dataObject.getName());
			rule.setDescription(dataObject.getDescription());
		});
	}

	@Override
	protected WorkOrderRuleDO doToDO(WorkOrderRule domainObject) {
		WorkOrderRuleDO workOrderRuleDO = new WorkOrderRuleDO();
		workOrderRuleDO.setName(domainObject.getName());
		workOrderRuleDO.setDescription(domainObject.getDescription());
		workOrderRuleDO.setType(domainObject.getType().getValue());
		config2DataObject(domainObject.getConfig(), workOrderRuleDO);
		return workOrderRuleDO;
	}

	void config2DataObject(WorkOrderRuleConfigurable config, WorkOrderRuleDO dataObject) {
		dataObject.setConfig(Objects.isNull(config) ? "" : Func.toJson(config));
	}
}
