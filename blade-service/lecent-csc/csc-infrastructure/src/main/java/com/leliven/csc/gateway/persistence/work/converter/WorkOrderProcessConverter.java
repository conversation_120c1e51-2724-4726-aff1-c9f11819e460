package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrderProcess;
import com.leliven.csc.domain.work.model.valueobject.Processor;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderOperationMode;
import com.leliven.csc.domain.work.model.valueobject.WorkOrderProcessOperation;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderProcessDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 工单流程转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Component
public class WorkOrderProcessConverter extends AbstractDomainEntityConverter<WorkOrderProcess, WorkOrderProcessDO> {

	@Override
	protected WorkOrderProcess doFromDO(WorkOrderProcessDO dataObject) {
		WorkOrderProcess domainObject = new WorkOrderProcess(dataObject.getOrderId());
		JavaUtils.INSTANCE
			.acceptIfHasText(dataObject.getFlowDesc(), domainObject::setFlowDesc)
			.acceptIfNotNull(dataObject.getStep(), domainObject::setStep)
			.acceptIfHasText(dataObject.getProcessMethod(), domainObject::setProcessMethod)
			.acceptIfHasText(dataObject.getProcessDesc(), domainObject::setProcessDesc)
			.acceptIfHasText(dataObject.getCurrentUserNameId(),
				p -> domainObject.setCurrentProcessors(dataobject2CurrentProcessors(dataObject)))
			.acceptIfNotNull(dataObject.getLastUserNameId(),
				p -> domainObject.setLastProcessor(dataobject2lastProcessor2DO(dataObject)))
			.acceptIfHasText(dataObject.getRemark(), domainObject::setRemark)
			.acceptIfHasText(dataObject.getOpRemark(), domainObject::setOperationDesc)
			.acceptIfNotNull(dataObject.getOperationMode(), m ->
				domainObject.setOperationMode(WorkOrderOperationMode.resolve(m)))
			.acceptIfNotNull(dataObject.getStatus(), s -> domainObject.setOperation(WorkOrderProcessOperation.resolve(s)))
			.acceptIfHasText(dataObject.getAttachmentAddress(), t -> domainObject.setAttachmentAddress(Func.toStrList(t)))
			.acceptIfNotNull(dataObject.getReminderNum(), domainObject::setReminderNum)
			.acceptIfNotNull(dataObject.getReminderTime(), domainObject::setReminderTime);
		return domainObject;
	}

	@Override
	protected WorkOrderProcessDO doToDO(WorkOrderProcess domainObject) {
		WorkOrderProcessDO dataobject = new WorkOrderProcessDO();
		JavaUtils.INSTANCE.acceptIfNotNull(domainObject.getOrderId(), dataobject::setOrderId)
			.acceptIfHasText(domainObject.getFlowDesc(), dataobject::setFlowDesc)
			.acceptIfNotNull(domainObject.getStep(), dataobject::setStep)
			.acceptIfHasText(domainObject.getProcessMethod(), dataobject::setProcessMethod)
			.acceptIfHasText(domainObject.getProcessDesc(), dataobject::setProcessDesc)
			.acceptIfCondition(
				Func.isNotEmpty(domainObject.getCurrentProcessors()),
				domainObject.getCurrentProcessors(),
				p -> currentProcessors2DO(p, dataobject))
			.acceptIfNotNull(domainObject.getLastProcessor(), p -> lastProcessor2DO(p, dataobject))
			.acceptIfHasText(domainObject.getRemark(), dataobject::setRemark)
			.acceptIfHasText(domainObject.getOperationDesc(), dataobject::setOpRemark)
			.acceptIfNotNull(domainObject.getOperation(), o -> operation2DO(o, dataobject))
			.acceptIfNotNull(domainObject.getOperationMode(), m -> dataobject.setOperationMode(m.getValue()))
			.acceptIfNotEmpty(domainObject.getAttachmentAddress(), t -> dataobject.setAttachmentAddress(Func.join(t)))
			.acceptIfNotNull(domainObject.getReminderNum(), dataobject::setReminderNum)
			.acceptIfNotNull(domainObject.getReminderTime(), dataobject::setReminderTime);
		return dataobject;
	}

	void operation2DO(WorkOrderProcessOperation operation, WorkOrderProcessDO dataobject) {
		dataobject.setStatus(operation.getKey());
		dataobject.setOperation(operation.getKey());
		dataobject.setOpRemark(operation.getValue());
	}

	void currentProcessors2DO(Set<Processor> currentProcessors, WorkOrderProcessDO dataobject) {
		List<Long> ids = new ArrayList<>();
		List<String> names = new ArrayList<>();
		currentProcessors.forEach(p -> {
			ids.add(p.getId());
			names.add(p.getName());
		});
		dataobject.setCurrentUserNameId(Func.join(ids));
		dataobject.setCurrentUserName(Func.join(names));
	}

	void lastProcessor2DO(Processor lastProcessor, WorkOrderProcessDO dataobject) {
		dataobject.setLastUserNameId(lastProcessor.getId());
		dataobject.setLastUserName(lastProcessor.getName());
	}

	Set<Processor> dataobject2CurrentProcessors(WorkOrderProcessDO dataobject) {
		List<Long> ids = Func.toLongList(dataobject.getCurrentUserNameId());
		List<String> names = Func.toStrList(dataobject.getCurrentUserName());

		Set<Processor> list = new HashSet<>(ids.size());
		for (int i = 0; i < ids.size(); i++) {
			list.add(new Processor(ids.get(i), names.get(i)));
		}
		return list;
	}

	Processor dataobject2lastProcessor2DO(WorkOrderProcessDO dataobject) {
		return new Processor(dataobject.getLastUserNameId(), dataobject.getLastUserName());
	}
}
