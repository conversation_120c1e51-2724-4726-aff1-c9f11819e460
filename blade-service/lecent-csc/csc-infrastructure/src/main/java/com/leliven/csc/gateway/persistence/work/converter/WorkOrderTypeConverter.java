package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrderType;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderTypeDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springframework.stereotype.Component;

/**
 * 工单类型转换器
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Component
public class WorkOrderTypeConverter extends AbstractDomainEntityConverter<WorkOrderType, WorkOrderTypeDO> {

	@Override
	protected WorkOrderType doFromDO(WorkOrderTypeDO dataObject) {
		WorkOrderType domainObject = new WorkOrderType();
		JavaUtils.INSTANCE
			.acceptIfHasText(dataObject.getParentCode(), domainObject::setParentCode)
			.acceptIfHasText(dataObject.getCode(), domainObject::setCode)
			.acceptIfHasText(dataObject.getName(), domainObject::setName)
			.acceptIfNotNull(dataObject.getDeviceType(), domainObject::setDeviceType);
		return domainObject;
	}

	@Override
	protected WorkOrderTypeDO doToDO(WorkOrderType domainObject) {
		WorkOrderTypeDO dataObject = new WorkOrderTypeDO();
		JavaUtils.INSTANCE
			.acceptIfHasText(domainObject.getParentCode(), dataObject::setParentCode)
			.acceptIfHasText(domainObject.getCode(), dataObject::setCode)
			.acceptIfHasText(domainObject.getName(), dataObject::setName)
			.acceptIfNotNull(domainObject.getDeviceType(), dataObject::setDeviceType);
		return dataObject;
	}
}

