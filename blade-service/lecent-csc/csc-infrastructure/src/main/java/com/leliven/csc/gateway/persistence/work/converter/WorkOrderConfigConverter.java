package com.leliven.csc.gateway.persistence.work.converter;

import com.leliven.csc.domain.work.model.WorkOrderConfig;
import com.leliven.csc.domain.work.model.WorkOrderContentTemplate;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderConfigDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

/**
 * 工单配置表转换器
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Component
@SuppressWarnings("DuplicatedCode")
public class WorkOrderConfigConverter extends AbstractDomainEntityConverter<WorkOrderConfig, WorkOrderConfigDO> {

	@Override
	protected WorkOrderConfig doFromDO(WorkOrderConfigDO dataObject) {
		WorkOrderConfig domainObject = new WorkOrderConfig();
		JavaUtils.INSTANCE
			.acceptIfHasText(dataObject.getCategory(), domainObject::setCategory)
			.acceptIfHasText(dataObject.getType(), domainObject::setType)
			.acceptIfNotNull(dataObject.getLevel(), domainObject::setLevel)
			.acceptIfHasText(dataObject.getDisplay(), domainObject::setDisplay)
			.acceptIfNotNull(dataObject.getTemplateId(), domainObject::setTemplateId)
			.acceptIfNotNull(dataObject.getPreRespTime(), domainObject::setPreRespTime)
			.acceptIfNotNull(dataObject.getPreCompTime(), domainObject::setPreCompTime)
			.acceptIfNotNull(dataObject.getAssignRuleId(), domainObject::setAssignRuleId)
			.acceptIfHasText(dataObject.getAction(), domainObject::setAction);
		domainObject.setContentTemplate(dataObject2ContentTemplate(dataObject));
		return domainObject;
	}

	@Override
	protected WorkOrderConfigDO doToDO(WorkOrderConfig domainObject) {
		WorkOrderConfigDO dataObject = new WorkOrderConfigDO();
		JavaUtils.INSTANCE
			.acceptIfHasText(domainObject.getCategory(), dataObject::setCategory)
			.acceptIfHasText(domainObject.getType(), dataObject::setType)
			.acceptIfNotNull(domainObject.getLevel(), dataObject::setLevel)
			.acceptIfHasText(domainObject.getDisplay(), dataObject::setDisplay)
			.acceptIfNotNull(domainObject.getTemplateId(), dataObject::setTemplateId)
			.acceptIfNotNull(domainObject.getPreRespTime(), dataObject::setPreRespTime)
			.acceptIfNotNull(domainObject.getPreCompTime(), dataObject::setPreCompTime)
			.acceptIfNotNull(domainObject.getAssignRuleId(), dataObject::setAssignRuleId)
			.acceptIfHasText(domainObject.getAction(), dataObject::setAction)
			.acceptIfNotNull(domainObject.getContentTemplate(),
				contentTemplate -> contentTemplate2DataObject(domainObject, dataObject));
		return dataObject;
	}

	private WorkOrderContentTemplate dataObject2ContentTemplate(WorkOrderConfigDO dataObject) {
		return new WorkOrderContentTemplate(dataObject.getContentTemplate(), Func.toStrList(dataObject.getContentTemplateParam()));
	}

	private void contentTemplate2DataObject(WorkOrderConfig domainObject, WorkOrderConfigDO dataObject) {
		WorkOrderContentTemplate contentTemplate = domainObject.getContentTemplate();
		dataObject.setContentTemplate(contentTemplate.getContentTemplate());
		dataObject.setContentTemplateParam(Func.join(contentTemplate.getTemplateParams()));
	}
}
