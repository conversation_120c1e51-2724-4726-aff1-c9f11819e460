package com.lecent.device.dto.event;

import com.lecent.device.entity.Device;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

import java.util.List;

/**
 * 旧设备同步领域事件
 *
 * <AUTHOR>
 */
public class DeviceSyncDeletedDomainEvent extends AbstractDomainEvent<List<Device>, DomainEventType> {

    protected DeviceSyncDeletedDomainEvent(Object source, DomainEventType eventType, List<Device> payload) {
        super(source, eventType, payload);
    }

    public static DeviceSyncDeletedDomainEvent of(Object source, List<Device> payload) {
        return new DeviceSyncDeletedDomainEvent(source, EventType.DELETED, payload);
    }

    public enum EventType implements DomainEventType {
        DELETED
    }
}
