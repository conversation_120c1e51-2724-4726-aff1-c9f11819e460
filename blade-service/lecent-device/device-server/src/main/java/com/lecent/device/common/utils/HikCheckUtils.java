package com.lecent.device.common.utils;

import com.lecent.device.common.enums.OssCode;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.image.MultiTypeImageUrlsWrapper;
import com.lecent.device.dto.TCLRcvDTO;
import com.lecent.device.enums.EnterExitType;
import com.leliven.vehicle.enums.PlateColor;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateValidator;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.resource.feign.IOssClient;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海康业务处理工具类
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HikCheckUtils {

	private static final Duration MAX_ALLOWED_DURATION = Duration.ofMinutes(10);

	/**
	 * 校验TriggerTime是否有效，无效则订正为当前时间
	 *
	 * @param tclRcvDTO TCLRcvDTO
	 */
	public static void correctTriggerTimeIfInvalid(TCLRcvDTO tclRcvDTO) {
		Date dateTime = tclRcvDTO.getDateTime();

		// 有效性检查（包含判空逻辑）
		Date now = new Date();
		if (!DateValidator.isValidTimeRange(dateTime, now, MAX_ALLOWED_DURATION)) {
			tclRcvDTO.setDateTime(now);
			log.warn("correct trigger time: device[{}], invalid time [{}], corrected time [{}]",
				tclRcvDTO.getChannelName(), dateTime, now);
		}
	}

	/**
	 * MultipartFile 转 Base64
	 *
	 * @param file MultipartFile
	 * @return String
	 */
	public static String generateBase64(MultipartFile file) {
		if (file == null || file.isEmpty()) {
			throw new RuntimeException("图片不能为空！");
		}
		String contentType = file.getContentType();
		byte[] imageBytes;
		String base64EncoderImg = "";
		try {
			imageBytes = file.getBytes();
			BASE64Encoder base64Encoder = new BASE64Encoder();
			base64EncoderImg = "data:" + contentType + ";base64," + base64Encoder.encode(imageBytes);
			base64EncoderImg = base64EncoderImg.replaceAll("[\\s*\t\n\r]", "");
		} catch (IOException e) {
			log.error("图片转换base64异常：{}", e.getMessage());
		}
		return base64EncoderImg;
	}

	/**
	 * 检查解析参数
	 *
	 * @param tclRcvDTO TCLRcvDTO
	 */
	public static void checkTclRcvDTO(TCLRcvDTO tclRcvDTO) {
		try {
			if (null == tclRcvDTO) {
				throw new ServiceException("tclRcvDTO must not null");
			}
			if (null == tclRcvDTO.getTfs()) {
				throw new ServiceException("tfs must not null");
			}

			if (null == tclRcvDTO.getTfs().getPlateInfo()) {
				throw new ServiceException("plate info must not null");
			}

			if (null == tclRcvDTO.getTfs().getVehicleInfo()) {
				throw new ServiceException("vehicle info must not null");
			}
		} catch (ServiceException e) {
			throw new ServiceException("业务处理异常, 解析TclRcvDTO失败, 原因" + e.getMessage());
		}
	}
	/**
	 * 检查解析参数
	 *
	 * @param tclRcvDTO TCLRcvDTO
	 */
	public static void checkTclRcvDTOV2(TCLRcvDTO tclRcvDTO) {
		try {
			if (null == tclRcvDTO) {
				throw new ServiceException("tclRcvDTO must not null");
			}
			if(Func.isEmpty(tclRcvDTO.getPackingSpaceRecognition())){
				throw new ServiceException("packingSpaceRecognition not null");
			}

		} catch (ServiceException e) {
			throw new ServiceException("业务处理异常, 解析TclRcvDTO失败, 原因" + e.getMessage());
		}
	}

	/**
	 * 通过报文的设备序列号、通道Id
	 * 生成系统唯一序列号
	 * 格式：string.format("%s@%s", channelName, channelId)
	 *
	 * @param channelName 设备序列号
	 * @param channelId   通道Id
	 * @return String
	 */
	public static String deviceSn(String channelName, String channelId) {
		LecentAssert.isTrue(Func.isNoneBlank(channelName), "channelName is blank");
		LecentAssert.isTrue(Objects.nonNull(channelId), "channelId is null");
		return String.format("%s@%s", channelName, channelId);
	}





	/**
	 * 检查通道ID
	 *
	 * @param channelId 通道ID
	 * @return Integer
	 */
	public static Integer channelId(String channelId) {
		if (Func.isNoneBlank(channelId) && Func.isNumeric(channelId)) {
			return Integer.valueOf(channelId);
		}

		return 1;
	}

	/**
	 * 转换车牌格式
	 * before： 蓝贵EEF958  after:  贵EEF958
	 *
	 * @param plate 车牌号
	 * @return String
	 */
	public static String transformPlate(String plate) {
		if (PlateValidator.isNoPlate(plate)) {
			return "无牌车";
		}
		return plate.substring(1).trim();
	}

	public static PlateColor transformPlateColor(String plate){
		//默认蓝牌
		if (PlateValidator.isNoPlate(plate)) {
			return null;
		}

		// 从车牌号开头截取颜色标识（如"绿"、"蓝"等）
		if (Func.isNotBlank(plate) && plate.length() > 0) {
			String colorPrefix = plate.substring(0, 1);

			// 使用 PlateColor.getByChineseName() 方法进行颜色匹配
			PlateColor matchedColor = PlateColor.getByChineseName(colorPrefix);
			if (!matchedColor.isUnknown()) {
				return matchedColor;
			}

			// 如果 getByChineseName 没有匹配到，尝试使用 tencent 字段匹配
			for (PlateColor plateColor : PlateColor.values()) {
				if (colorPrefix.equals(plateColor.tencent())) {
					return plateColor;
				}
			}
		}

		// 使用 PlateColor.isGreen() 方法判断是否为绿色车牌
		if (PlateColor.isGreen(plate)) {
			return PlateColor.GREEN;
		}

		// 根据车牌长度判断颜色（备用逻辑）
		if (plate.length() == 8) {
			// 8位车牌通常是新能源车牌，返回绿色
			return PlateColor.GREEN;
		} else if (plate.length() == 7) {
			// 7位车牌通常是普通车牌，返回蓝色
			return PlateColor.BLUE;
		}

		// 其他情况默认返回蓝色
		return PlateColor.BLUE;
	}


	public static Integer typeOf(String vehicleEnterState) {
		switch (vehicleEnterState) {
			case "vehicleEnter":
				return EnterExitType.ENTER.getValue();
			case "vehicleExit":
				return EnterExitType.EXIT.getValue();
			default:
				throw new ServiceException("unknown vehicleEnterState： " + vehicleEnterState);
		}
	}

	public static MultiTypeImageUrlsWrapper uploadImages(HikMultipartFileWrapper multipartFileWrapper) {
		List<String> plateImageUrls = uploadImages(multipartFileWrapper.getPlateImageFiles());
		List<String> sceneImageUrls = uploadImages(multipartFileWrapper.getSceneImageFiles());
		MultiTypeImageUrlsWrapper wrapper = new MultiTypeImageUrlsWrapper();
		wrapper.addPlateImageUrls(plateImageUrls);
		wrapper.addSceneImageUrls(sceneImageUrls);
		return wrapper;
	}

	public static List<String> uploadImages(List<MultipartFile> imageFiles) {
		try {
			return Optional.ofNullable(imageFiles)
				.map(files -> files.stream().map(HikCheckUtils::uploadImage).filter(Func::isNotBlank).collect(Collectors.toList()))
				.orElse(Collections.emptyList());
		} catch (Exception e) {
			log.error("uploadPic failed", e);
			return Collections.emptyList();
		}
	}

	public static String uploadImage(MultipartFile imageFile) {
		return Optional.ofNullable(imageFile)
			.map(file -> {
				IOssClient ossClient = SpringUtil.getBean(IOssClient.class);
				final R<BladeFile> ossFile =
					ossClient.putFileInputStreamByOssCode(file, OssCode.ROADSIDE_PARKING_CODE.getCode());
				if (!ossFile.isSuccess()) {
					log.warn("upload file[filename={}] failed, ossFile = {}", imageFile.getOriginalFilename(), ossFile);
					return StringPool.EMPTY;
				}
				return ossFile.getData().getLink();
			}).orElse(StringPool.EMPTY);
	}
}
