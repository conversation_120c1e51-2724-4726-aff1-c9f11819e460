package com.lecent.device.listener.spring.hik;

import com.lecent.device.cache.DeviceCache;
import com.lecent.device.common.enums.OssCode;
import com.lecent.device.common.utils.HikCheckUtils;
import com.lecent.device.common.utils.converter.HikPlateColorConverter;
import com.lecent.device.constant.DeviceConstant;
import com.lecent.device.domain.hik.HikEventXmlFileResolver;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.hik.HikXStream;
import com.lecent.device.domain.hik.event.HikEvent;
import com.lecent.device.dto.TCLRcvDTO;
import com.lecent.device.entity.Device;
import com.lecent.device.enums.CameraCaptureType;
import com.lecent.device.event.DeviceCameraCaptureEvent;
import com.lecent.device.event.payload.CameraCaptureEventPayload;
import com.lecent.device.publisher.DeviceEventPublisher;
import com.lecent.device.service.IDeviceHeartbeatService;
import com.lecent.device.service.IDeviceRuntimeStatusService;
import com.lecent.device.service.IDeviceService;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.csc.alarm.enums.AlarmRestoreClassEnum;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.vehicle.enums.PlateColor;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.CacheNames;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.resource.feign.IOssClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>  海康自动抓拍事件监听器
 * @Description TODO
 * @createTime 2025/8/8 15:40
 */
@Slf4j
@Component
public class PackingSpaceRecognitionListener {

	@Resource
	private IOssClient ossClient;
	@Resource
	private IDeviceService deviceService;
	@Resource
	private DeviceEventPublisher deviceEventPublisher;
	@Resource
	private IDeviceHeartbeatService deviceHeartbeatService;
	@Resource
	private IDeviceRuntimeStatusService deviceRuntimeStatusService;
	@Resource
	private BladeRedis bladeRedis;

	@Resource
	private MqSender mqSender;

	/**
	 * 海康摄像头抓拍事件
	 */
	@EventListener(
		classes = HikEvent.class,
		condition = "#event.eventType == T(com.lecent.device.domain.hik.event.HikEventType).PACKING_SPACE_RECOGNITION"
	)
	public void onEvent(HikEvent<HikMultipartFileWrapper> event) {
		HikMultipartFileWrapper fileWrapper = event.getPayload();
		// 解析TCLRcvDTO
		TCLRcvDTO tclRcvDTO = HikEventXmlFileResolver.build(HikXStream.of(event.getEventType()))
			.resolveJson(fileWrapper.getJSONFile());
		log.info("AIDEventListener tclRcvDTO: {}", Func.toJson(tclRcvDTO));
		if (log.isDebugEnabled()) {
			log.debug("AIDEventListener tclRcvDTO: {}", Func.toJson(tclRcvDTO));
		}
		// 校验TCLRcvDTO
		HikCheckUtils.checkTclRcvDTOV2(tclRcvDTO);
		// 校验TriggerTime是否有效，无效则订正为当前时间
		HikCheckUtils.correctTriggerTimeIfInvalid(tclRcvDTO);
		// 场景图片集合
		List<MultipartFile> sceneImageFiles = fileWrapper.getSceneImageFiles();
		// 上传图片到oss
		String imagesUrl = uploadImages(sceneImageFiles);
		// 获取设备, 注：高位摄像头只会发送一个抓拍事件，通道号为1
		resolveDevice(tclRcvDTO.getChannelName(), tclRcvDTO.getChannelID())
			.forEach(t -> {
				// 推送到设备心跳业务处理
				deviceHeartbeatService.heartbeat(t);
				// 推送设备运行状态
				deviceRuntimeStatusService.runtimeStatus(t, Func.toJson(tclRcvDTO));
				// 构建事件
				DeviceCameraCaptureEvent captureEvent = buildCameraCaptureEvent(t, tclRcvDTO, imagesUrl);
				// 发布设备定时抓拍事件
				deviceEventPublisher.publishCameraCaptureTimedEvent(captureEvent);
				// 恢复告警
				recoverShelterAlarm(captureEvent);
				// 检查设备IP地址是否变更，如果变更则更新设备信息
				deviceService.updateIfIpAddrDiff(t, tclRcvDTO.getIpAddress());
			});
	}

	/**
	 * 构建设备定时抓拍事件
	 *
	 * @param device    设备
	 * @param tclRcvDTO TCLRcvDTO
	 * @param imagesUrl 图片地址
	 * @return DeviceCameraCaptureEvent
	 */
	private DeviceCameraCaptureEvent buildCameraCaptureEvent(Device device, TCLRcvDTO tclRcvDTO, String imagesUrl) {
//		TCLRcvDTO.TFS tfs = tclRcvDTO.getTfs();
//		TCLRcvDTO.PlateInfo plateInfo = tfs.getPlateInfo();

		List<TCLRcvDTO.PackingSpaceRecognition> packingSpaceRecognitions = tclRcvDTO.getPackingSpaceRecognition();
		TCLRcvDTO.PackingSpaceRecognition packingSpaceRecognition = packingSpaceRecognitions.get(0);


		return new DeviceCameraCaptureEvent(
			CameraCaptureEventPayload.builder()
				.captureTime(tclRcvDTO.getDateTime())
				.type(CameraCaptureType.TIMED)
				.imageUrl(imagesUrl)
				.plate(HikCheckUtils.transformPlate(packingSpaceRecognition.getPlateNo()))
				.plateColor(HikCheckUtils.transformPlateColor(packingSpaceRecognition.getPlateNo()))
				.parkingState(packingSpaceRecognition.getIsParked().equals("yes")?0:1)
				.build()
			, device
		);
	}
	/**
	 * 上传图片到oss
	 *
	 * @param sceneImageFiles 场景图片集合
	 * @return 图片地址
	 */
	private String uploadImages(List<MultipartFile> sceneImageFiles) {
		List<String> imagesUrls = new ArrayList<>();
		try {
			for (MultipartFile file : sceneImageFiles) {
				R<BladeFile> bladeFileR = ossClient.putFileInputStreamByOssCode(file, OssCode.SNAP_CODE.getCode());
				if (bladeFileR != null && bladeFileR.getData() != null) {
					imagesUrls.add(bladeFileR.getData().getLink());
				}
			}
		} catch (Exception e) {
			log.error("AIDEventListener uploadImages error", e);
		}
		return Func.join(imagesUrls);
	}

	/**
	 * 根据设备类型解析设备
	 *
	 * @param channelName 通道名称
	 * @param channelId   通道ID
	 * @return List<Device>
	 */
	private List<Device> resolveDevice(String channelName, String channelId) {
		Device device = DeviceCache.existBySn(HikCheckUtils.deviceSn(channelName, channelId));
		if (DeviceType.HIGH_VIDEO_CAMERA.isSameValue(device.getType())) {
			// 如果是高位摄像头，则根据sn前缀获取所有设备
			return deviceService.listBySnLikeRight(channelName + StringPool.AT);
		}
		return Collections.singletonList(device);
	}

	private void recoverShelterAlarm(DeviceCameraCaptureEvent captureEvent) {
		Device device = captureEvent.getSource();
		try {
			//有车牌才处理
			if (!PlateValidator.isPlate(captureEvent.getPayload().getPlate())) {
				return;
			}
			//判断是否有告警
			Boolean existed = bladeRedis.getRedisTemplate().opsForSet().isMember(CacheNames.SHELTER_ALARM_DEVICE, device.getId());
			if (Boolean.TRUE.equals(existed)) {
				//删除缓存
				bladeRedis.getRedisTemplate().opsForSet().remove(CacheNames.SHELTER_ALARM_DEVICE, device.getId());
				//推送恢复告警事件
				AlarmEvent alarmEvent = new AlarmEvent();
				alarmEvent.setAlarmType(1);
				alarmEvent.setAlarmClass(AlarmRestoreClassEnum.DEVICE_VIDEO_SHELTER.getKey());
				alarmEvent.setAlarmTime(captureEvent.getPayload().getCaptureTime());
				alarmEvent.setDeviceId(device.getId());
				alarmEvent.setDeviceSn(device.getSn());
				alarmEvent.setPlate(captureEvent.getPayload().getPlate());
				String routingKey = DeviceConstant.DEVICE_ALARM_UP_PREFIX + AlarmRestoreClassEnum.DEVICE_VIDEO_SHELTER.getKey();
				mqSender.sendMessage(Func.toJson(alarmEvent), routingKey, Exchanges.AMQ_TOPIC);
			}
		} catch (Exception e) {
			log.error("recoverShelterAlarm", e);
		}
	}
}
