package com.lecent.device.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lecent.device.cache.DeviceCache;
import com.lecent.device.cache.DeviceCacheNames;
import com.lecent.device.constant.DeviceConstant;
import com.lecent.device.dto.BindingContext;
import com.lecent.device.dto.DeviceBindDTO;
import com.lecent.device.dto.DeviceDTO;
import com.lecent.device.dto.DeviceTreeDTO;
import com.lecent.device.dto.event.DeviceSyncBindingDomainEvent;
import com.lecent.device.dto.event.DeviceSyncDeletedDomainEvent;
import com.lecent.device.dto.event.DeviceSyncDomainEvent;
import com.lecent.device.entity.*;
import com.lecent.device.mapper.DeviceMapper;
import com.lecent.device.service.*;
import com.lecent.device.vo.DeviceStatisticsVO;
import com.lecent.device.vo.DeviceVO;
import com.lecent.park.dto.BatchBindDeviceDTO;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.feign.IParkClient;
import com.lecent.park.feign.IParkingPlaceClient;
import com.lecent.park.feign.IParklotDeviceRetClient;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.enums.BindStatus;
import org.springblade.common.enums.NetworkStatus;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 设备管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-09
 */
@Slf4j
@Service
@TenantIgnore(tenants = TenantConstant.ADMIN_CODE)
public class DeviceServiceImpl extends BaseServiceImpl<DeviceMapper, Device> implements IDeviceService {

    @Resource
    private IDeviceParamService deviceParamService;
    @Lazy
    @Resource
    private IGroupService groupService;
    @Resource
    private IProductService productService;
    @Resource
    private RedisLockClient redisLockClient;
    @Resource
    private IProductParamService productParamService;
    @Resource
    private BladeRedis redis;
    @Lazy
    @Resource
    private IParkClient parkClient;
    @Lazy
    @Resource
    private IParkingPlaceClient parkingPlaceClient;
    @Lazy
    @Resource
    private IParklotDeviceRetClient parklotDeviceRetClient;

    @Slave
    @Override
    public IPage<DeviceVO> selectDevicePage(IPage<DeviceVO> page, DeviceVO device) {
        return page.setRecords(baseMapper.selectDevicePage(page, device, getChildGroupIds(device.getGroupId())));
    }

    /**
     * 设备数量统计
     *
     * @return DeviceStatisticsVO
     */
    @Slave
    @Override
    public DeviceStatisticsVO statistics(DeviceDTO deviceDTO) {
        final DeviceStatisticsVO statisticsVO = this.baseMapper.statistics(deviceDTO, getChildGroupIds(deviceDTO.getGroupId()));
        statisticsVO.setOfflineNum(statisticsVO.getTotalNum() - statisticsVO.getOnlineNum());
        return statisticsVO;
    }

    private List<Long> getChildGroupIds(Long groupId) {
        return Optional.ofNullable(groupId)
            .map(t ->
                groupService.list(Wrappers.<Group>lambdaQuery()
                        .like(Group::getAncestors, t)
                        .or().eq(Group::getId, t))
                    .stream()
                    .map(BaseEntity::getId)
                    .collect(Collectors.toList()))
            .orElse(Collections.emptyList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Device saveIfAbsent(DeviceDTO deviceDTO) {
        LecentAssert.notNull(deviceDTO.getType(), "参数[type]必须传值");

        if (DeviceType.PARKING_LOCK.getValue().equals(deviceDTO.getType())) {
            LecentAssert.notBlank(deviceDTO.getGatewaySn(), "参数[gatewaySn]必须传值");

            if (deviceDTO.getGatewaySn().startsWith("000")) {
                throw new ServiceException("网关设备序列号[" + deviceDTO.getGatewaySn() + "]异常，请检测网关设备与车位锁是否连接正常");
            }

            return redisLockClient.lockFair(DeviceCacheNames.LOCK_DEVICE_LOCK_EDIT + ":" + deviceDTO.getGatewaySn(),
                5L,
                6L,
                () -> saveSpaceLockDevice(deviceDTO)
            );
        }

        LecentAssert.notBlank(deviceDTO.getSn(), "参数[sn]必须传值");
        return saveIfAbsent(deviceDTO.getSn(), () -> deviceDTO);
    }

    private Device saveIfAbsentGatewayDevice(DeviceDTO deviceDTO) {
        return saveIfAbsent(deviceDTO.getGatewaySn(), () -> {
            Product product = this.productService.existByCode(String.valueOf(DeviceType.PARKING_LOCK_GATEWAY.getValue()));
            DeviceDTO gatewayDeviceDTO = new DeviceDTO();
            gatewayDeviceDTO.setSn(deviceDTO.getGatewaySn());
            gatewayDeviceDTO.setType(Integer.valueOf(product.getNodeType()));
            gatewayDeviceDTO.setProductId(product.getId());
            gatewayDeviceDTO.setName(deviceDTO.getGatewayName());
            gatewayDeviceDTO.setLat(deviceDTO.getLat());
            gatewayDeviceDTO.setLng(deviceDTO.getLng());
            gatewayDeviceDTO.setAddress(deviceDTO.getAddress());
            return gatewayDeviceDTO;
        });
    }

    /**
     * 新增车位锁设备
     * 单独处理
     *
     * @param deviceDTO 设备DTO
     * @return Device
     */
    public Device saveSpaceLockDevice(DeviceDTO deviceDTO) {
        LecentAssert.isTrue(Func.isNoneBlank(deviceDTO.getSn()) || Func.isNoneBlank(deviceDTO.getGatewaySn()),
            "参数[sn]或[gatewaySn]其中一个必须传值");
        // 网关设备
        Device gatewayDevice = saveIfAbsentGatewayDevice(deviceDTO);

        // 网关子设备
        return saveIfAbsent(oneByMac(deviceDTO.getMac()), () -> {
            if (Func.isBlank(deviceDTO.getSn())) {
                // 设备通道号自增逻辑
                List<Device> childDevices = listGatewayChild(gatewayDevice.getId());
                int channelId = generateLockChannelId(gatewayDevice, childDevices);
                deviceDTO.setSn(String.format("%s@%d", gatewayDevice.getSn(), channelId));
            }

            deviceDTO.setParentId(gatewayDevice.getId());
            return deviceDTO;
        });
    }

    /**
     * 如果存在设备，则返回设备信息
     * 如果不存在设备，则保存设备，并返回设备信息
     *
     * @param sn       设备号
     * @param supplier Supplier<DeviceDTO> supplier
     * @return Device
     */
    private Device saveIfAbsent(String sn, Supplier<DeviceDTO> supplier) {
        return saveIfAbsent(DeviceCache.getBySn(sn), supplier);
    }

    /**
     * 如果存在设备，则返回设备信息
     * 如果不存在设备，则保存设备，并返回设备信息
     *
     * @param device   设备信息
     * @param supplier Supplier<DeviceDTO> supplier
     * @return Device
     */
    private Device saveIfAbsent(Device device, Supplier<DeviceDTO> supplier) {
        return Optional.ofNullable(device).orElseGet(() -> {
            DeviceDTO deviceDTO = supplier.get();
            customSaveOrUpdate(deviceDTO);
            if (Func.isNotEmpty(deviceDTO.getParams())) {
                this.deviceParamService.saveOrUpdateParams(
                    deviceDTO.getParams().stream()
                        .peek(t -> t.setDeviceId(deviceDTO.getId()))
                        .collect(Collectors.toList())
                );
            }
            return deviceDTO;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean customSaveOrUpdate(DeviceDTO device) {
        LecentAssert.isTrue(Objects.nonNull(device.getProductId()) || Objects.nonNull(device.getType()),
            "参数[productId]或[type]不能同时为空");
        if (!checkSnRepeat(device.getSn(), device.getId())) {
            throw new ServiceException("序列号已存在");
        }

        Product product = Objects.isNull(device.getProductId()) ? this.productService.existByCode(String.valueOf(device.getType()))
            : this.productService.existById(device.getProductId());
        if ("3".equals(product.getNodeType())) {
            LecentAssert.notNull(device.getParentId(), "新增网关子设备，父级设备id不能为空");
            Device parentDevice = this.getById(device.getParentId());
            LecentAssert.notNull(parentDevice, "未查询到父级设备信息");
        }

        device.setProductId(product.getId());
        device.setType(Integer.valueOf(product.getCode()));
        final boolean success = saveOrUpdate(device);
        LecentAssert.isTrue(success, "保存设备失败");
        //新增或修改时从产品参数初始化
        final List<ProductParam> productParams = this.productParamService.selectParams(device.getProductId());
        final List<DeviceParam> deviceParams = this.deviceParamService.listByDeviceId(device.getId());

        List<DeviceParam> addList = Lists.newArrayList();
        productParams.forEach(productParam -> {
            final List<DeviceParam> findList = deviceParams.stream().filter(deviceParam -> deviceParam.getK().equals(productParam.getK())).collect(Collectors.toList());
            if (Func.isEmpty(findList)) {
                DeviceParam deviceParam = new DeviceParam();
                deviceParam.setK(productParam.getK());
                deviceParam.setName(productParam.getName());
                deviceParam.setRemark(productParam.getRemark());
                deviceParam.setVType(productParam.getV());
                deviceParam.setDeviceId(device.getId());
                addList.add(deviceParam);
            }
        });

        if (Func.isNotEmpty(addList)) {
            deviceParamService.saveBatch(addList);
        }

        DeviceCache.delKey(device.getSn());

        SpringDomainEventPublisher.publish(DeviceSyncDomainEvent.ofCreated(this, device));

        return true;
    }

    @Override
    public Boolean bindDeviceToParking(DeviceBindDTO deviceBindDTO) {
        // 获取产品信息
        Product product = productService.existById(deviceBindDTO.getProductId());
        LecentAssert.notNull(product, "产品信息不存在");

        // 使用分布式锁确保并发安全
        String lockKey = DeviceCacheNames.LOCK_DEVICE_BATCH_BIND + deviceBindDTO.getDeviceSn();
        return redisLockClient.lockFair(lockKey, 30L, 35L,
            () -> executeDeviceBinding(deviceBindDTO, product));
    }

    /**
     * 执行设备绑定操作
     */
    private Boolean executeDeviceBinding(DeviceBindDTO deviceBindDTO, Product product) {
        // 1. 批量获取基础数据
        BindingContext context = prepareBindingContext(deviceBindDTO, product);

        // 2. 验证数据一致性
        validateBindingContext(context);

        // 3. 检查设备序列号唯一性
        validateDeviceSerialNumbers(context);

        List<Device> createdDevices = new ArrayList<>();
		try {
			// 4. 批量创建设备
			createdDevices = batchCreateDevices(context);

			// 5. 批量绑定设备到车位
			batchBindDevicesToPlaces(createdDevices, context.getPlaces());

			log.info("成功绑定{}个设备到车位", createdDevices.size());
			return true;
		} catch (Exception e) {
			// 6. 异常时回滚已创建的设备
			rollbackCreatedDevices(createdDevices, e);
			throw e;
		}
    }

    /**
     * 准备绑定上下文数据
     */
    private BindingContext prepareBindingContext(DeviceBindDTO deviceBindDTO, Product product) {
        List<Long> parkLotIds = deviceBindDTO.getParklotIds();
        List<Long> placeIds = deviceBindDTO.getPlaceIds();

        // 并行获取车场和车位信息
        R<List<Parklot>> parkLotResult = parkClient.queryParklotByIds(parkLotIds);
        LecentAssert.isTrue(parkLotResult.isSuccess() && Func.isNotEmpty(parkLotResult.getData()),
            "获取车场信息失败：{}", parkLotResult.getMsg());

        R<List<ParkingPlace>> placeResult = parkingPlaceClient.getByIdsExcludeTenantId(placeIds);
        LecentAssert.isTrue(placeResult.isSuccess() && Func.isNotEmpty(placeResult.getData()),
            "获取车位信息失败：{}", placeResult.getMsg());

        List<Parklot> parkLots = parkLotResult.getData();
        List<ParkingPlace> places = placeResult.getData();

        // 构建映射关系
        Map<Long, Parklot> parkLotMap = parkLots.stream()
            .collect(Collectors.toMap(Parklot::getId, Function.identity()));

        // 预先获取或创建所有需要的设备分组
        Map<Long, Long> parklotGroupMap = batchGetOrCreateGroups(parkLots);

        return BindingContext.builder()
            .deviceBindDTO(deviceBindDTO)
            .product(product)
            .parkLots(parkLots)
            .places(places)
            .parkLotMap(parkLotMap)
            .parklotGroupMap(parklotGroupMap)
            .build();
    }

    /**
     * 验证绑定上下文数据一致性
     */
    private void validateBindingContext(BindingContext context) {
        List<Long> parkLotIds = context.getDeviceBindDTO().getParklotIds();
        List<Long> placeIds = context.getDeviceBindDTO().getPlaceIds();

        // 验证车场数据完整性
        Set<Long> foundParkLotIds = context.getParkLots().stream()
            .map(Parklot::getId)
            .collect(Collectors.toSet());
        List<Long> missingParkLotIds = parkLotIds.stream()
            .filter(id -> !foundParkLotIds.contains(id))
            .collect(Collectors.toList());
        LecentAssert.isTrue(missingParkLotIds.isEmpty(),
            "以下车场不存在：{}", missingParkLotIds);

        // 验证车位数据完整性
        Set<Long> foundPlaceIds = context.getPlaces().stream()
            .map(ParkingPlace::getId)
            .collect(Collectors.toSet());
        List<Long> missingPlaceIds = placeIds.stream()
            .filter(id -> !foundPlaceIds.contains(id))
            .collect(Collectors.toList());
        LecentAssert.isTrue(missingPlaceIds.isEmpty(),
            "以下车位不存在：{}", missingPlaceIds);

        // 验证车位与车场的对应关系
        Set<Long> placeParklotIds = context.getPlaces().stream()
            .map(ParkingPlace::getParklotId)
            .collect(Collectors.toSet());
        boolean isParklotMatched = parkLotIds.containsAll(placeParklotIds);
        LecentAssert.isTrue(isParklotMatched, "部分车位不属于指定的车场");
    }

    /**
     * 验证设备序列号唯一性
     */
    private void validateDeviceSerialNumbers(BindingContext context) {
        String baseSn = context.getDeviceBindDTO().getDeviceSn();
        Set<String> deviceSnSet = new HashSet<>();
        List<String> duplicateSnList = new ArrayList<>();

        for (ParkingPlace place : context.getPlaces()) {
            String deviceSn = generateDeviceSn(baseSn, place, context.getProduct());
            if (!deviceSnSet.add(deviceSn)) {
                duplicateSnList.add(deviceSn);
            }

            // 检查数据库中是否已存在
            if (checkSnRepeat(deviceSn, null)) {
                Device existingDevice = getBySn(deviceSn);
                if (existingDevice != null) {
                    duplicateSnList.add(deviceSn + "(数据库中已存在)");
                }
            } else {
                duplicateSnList.add(deviceSn + "(数据库中已存在)");
            }
        }

        LecentAssert.isTrue(duplicateSnList.isEmpty(),
            "以下设备序列号重复或已存在：{}", String.join("、", duplicateSnList));
    }

    /**
     * 批量获取或创建设备分组
     */
    private Map<Long, Long> batchGetOrCreateGroups(List<Parklot> parkLots) {
        Map<Long, Long> parklotGroupMap = new HashMap<>();

        // 批量查询现有分组
        Set<String> groupNames = parkLots.stream()
            .map(Parklot::getName)
            .collect(Collectors.toSet());

        List<Group> existingGroups = groupService.lambdaQuery()
            .in(Group::getName, groupNames)
            .list();

        Map<String, Long> existingGroupMap = existingGroups.stream()
            .collect(Collectors.toMap(Group::getName, Group::getId));

        // 批量创建缺失的分组
        List<Group> groupsToCreate = new ArrayList<>();
        for (Parklot parklot : parkLots) {
            Long groupId = existingGroupMap.get(parklot.getName());
            if (groupId != null) {
                parklotGroupMap.put(parklot.getId(), groupId);
            } else {
                Group newGroup = new Group();
                newGroup.setName(parklot.getName());
                newGroup.setParentId(0L);
                newGroup.setSort(1);
                newGroup.setRemark("车场设备分组");
                groupsToCreate.add(newGroup);
            }
        }

        if (!groupsToCreate.isEmpty()) {
            boolean saved = groupService.saveBatch(groupsToCreate);
            LecentAssert.isTrue(saved, "批量创建设备分组失败");

            // 重新查询获取ID
            for (Group group : groupsToCreate) {
                Group savedGroup = groupService.lambdaQuery()
                    .eq(Group::getName, group.getName())
                    .one();
                LecentAssert.notNull(savedGroup, "分组创建后查询失败：{}", group.getName());

                // 找到对应的车场并建立映射
                parkLots.stream()
                    .filter(parklot -> parklot.getName().equals(group.getName()))
                    .forEach(parklot -> parklotGroupMap.put(parklot.getId(), savedGroup.getId()));
            }
        }

        return parklotGroupMap;
    }

    /**
     * 批量创建设备
     */
    private List<Device> batchCreateDevices(BindingContext context) {
        List<Device> createdDevices = new ArrayList<>();
        String baseSn = context.getDeviceBindDTO().getDeviceSn();
        Product product = context.getProduct();

        // 处理车位锁网关设备（如果需要）
        Device gatewayDevice = null;
        if (DeviceType.PARKING_LOCK.isSameValue(Integer.valueOf(product.getCode())) && baseSn.contains("@")) {
            gatewayDevice = createGatewayDeviceIfNeeded(context);
            if (gatewayDevice != null) {
                createdDevices.add(gatewayDevice);
            }
        }

        // 批量创建设备
        for (ParkingPlace place : context.getPlaces()) {
            Device device = createSingleDevice(place, context, gatewayDevice);
            createdDevices.add(device);
        }

        log.info("批量创建设备完成，共创建{}个设备", createdDevices.size());
        return createdDevices;
    }

    /**
     * 创建网关设备（如果需要）
     */
    private Device createGatewayDeviceIfNeeded(BindingContext context) {
        String baseSn = context.getDeviceBindDTO().getDeviceSn();
        String gatewaySn = baseSn.substring(0, baseSn.indexOf("@"));

        // 检查网关设备是否已存在
        Device existingGateway = getBySn(gatewaySn);
        if (existingGateway != null) {
            return existingGateway;
        }

        // 创建网关设备
        Product gatewayProduct = productService.existByCode(String.valueOf(DeviceType.PARKING_LOCK_GATEWAY.getValue()));

        // 使用第一个车场的信息创建网关设备
        Parklot firstParklot = context.getParkLots().get(0);
        Long groupId = context.getParklotGroupMap().get(firstParklot.getId());

        DeviceDTO gatewayDeviceDTO = new DeviceDTO();
        gatewayDeviceDTO.setSn(gatewaySn);
        gatewayDeviceDTO.setName(firstParklot.getName() + "-" + DeviceType.PARKING_LOCK_GATEWAY.getName());
        gatewayDeviceDTO.setProductId(gatewayProduct.getId());
        gatewayDeviceDTO.setType(Integer.valueOf(gatewayProduct.getCode()));
        gatewayDeviceDTO.setGroupId(groupId);
        gatewayDeviceDTO.setRemark(context.getDeviceBindDTO().getRemark());

        Boolean success = customSaveOrUpdate(gatewayDeviceDTO);
        LecentAssert.isTrue(success, "创建网关设备失败");

        return getBySn(gatewaySn);
    }

    /**
     * 创建单个设备
     */
    private Device createSingleDevice(ParkingPlace place, BindingContext context, Device gatewayDevice) {
        String deviceSn = generateDeviceSn(context.getDeviceBindDTO().getDeviceSn(), place, context.getProduct());
        Long groupId = context.getParklotGroupMap().get(place.getParklotId());

        // 生成设备名称
        String deviceName = place.getPayCode() + "-" +
            DeviceType.resolve(Integer.valueOf(context.getProduct().getCode())).getName();

        DeviceDTO deviceDTO = new DeviceDTO();
        deviceDTO.setSn(deviceSn);
        deviceDTO.setName(deviceName);
        deviceDTO.setProductId(context.getProduct().getId());
        deviceDTO.setType(Integer.valueOf(context.getProduct().getCode()));
        deviceDTO.setGroupId(groupId);
        deviceDTO.setRemark(context.getDeviceBindDTO().getRemark());

        if (gatewayDevice != null) {
            deviceDTO.setParentId(gatewayDevice.getId());
            deviceDTO.setGatewayName(gatewayDevice.getName());
            deviceDTO.setGatewaySn(gatewayDevice.getSn());
        }

        // 车位锁设备特殊处理
        if (DeviceType.PARKING_LOCK.isSameValue(deviceDTO.getType()) &&
            context.getDeviceBindDTO().getDeviceSn().contains("@")) {

            Device device = saveSpaceLockDevice(deviceDTO);
            LecentAssert.notNull(device, "创建车位锁设备失败");
            return device;
        }

        Boolean success = customSaveOrUpdate(deviceDTO);
        LecentAssert.isTrue(success, "创建设备失败");

        Device device = getBySn(deviceSn);
        LecentAssert.notNull(device, "设备创建后查询失败");

        return device;
    }

    /**
     * 生成设备序列号
     */
    private String generateDeviceSn(String baseSn, ParkingPlace place, Product product) {
        if ((DeviceType.PARKING_LOCK.isSameValue(Integer.valueOf(product.getCode())) && baseSn.contains("@"))
            || DeviceType.PARKING_LOCK_GATEWAY.isSameValue(Integer.valueOf(product.getCode()))) {
            return baseSn;
        } else {
            return baseSn + "@" + place.getPayCode();
        }
    }

	/**
	 * 回滚已创建的设备
	 */
	private void rollbackCreatedDevices(List<Device> createdDevices, Exception originalException) {
		if (createdDevices.isEmpty()) {
			return;
		}

		try {
			List<Long> deviceIds = createdDevices.stream()
					.map(Device::getId)
					.collect(Collectors.toList());

			log.warn("设备绑定失败，开始回滚删除已创建的设备，设备ID列表：{}", deviceIds);
			batchDeleteWithGroupRemoval(deviceIds);
			log.info("设备回滚删除完成");
		} catch (Exception rollbackException) {
			log.error("设备回滚删除失败", rollbackException);
			// 将回滚异常添加到原始异常中
			originalException.addSuppressed(rollbackException);
		}
	}

    /**
     * 批量绑定设备到车位
     */
    private void batchBindDevicesToPlaces(List<Device> devices, List<ParkingPlace> places) {
        // 创建设备与车位的映射关系
        Map<String, Device> deviceMap = devices.stream()
            .filter(device -> !DeviceType.PARKING_LOCK_GATEWAY.isSameValue(device.getType()))
            .collect(Collectors.toMap(device -> {
                // 从设备名称中提取支付码
                String name = device.getName();
                return name.substring(0, name.indexOf("-"));
            }, Function.identity()));

        // 构建批量绑定DTO
        List<BatchBindDeviceDTO.DeviceBindInfo> bindInfos = places.stream()
            .map(place -> {
                Device device = deviceMap.get(place.getPayCode());
                if (device == null) {
                    throw new ServiceException("未找到车位[" + place.getPayCode() + "]对应的设备");
                }

                BatchBindDeviceDTO.DeviceBindInfo bindInfo = new BatchBindDeviceDTO.DeviceBindInfo();
                bindInfo.setDeviceId(device.getId());
                bindInfo.setParkPlaceId(place.getId());
                bindInfo.setDeviceType(device.getType());
                bindInfo.setProductId(device.getProductId());
                return bindInfo;
            })
            .collect(Collectors.toList());

        BatchBindDeviceDTO batchBindDTO = new BatchBindDeviceDTO();
        batchBindDTO.setDeviceBindInfos(bindInfos);

        // 调用批量绑定接口
        R<Boolean> bindResult = parklotDeviceRetClient.batchBindSpaceDevice(batchBindDTO);
        LecentAssert.isTrue(bindResult.isSuccess() && bindResult.getData(),
            "批量绑定设备到车位失败：{}", bindResult.getMsg());

        log.info("成功批量绑定{}个设备到车位", bindInfos.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindUpdateDevice(DeviceDTO deviceDTO) {
        this.updateBindStatus(deviceDTO.getId(), deviceDTO.getBindStatus());

        final List<DeviceParam> params = deviceDTO.getParams();
        if (Func.isNotEmpty(params)) {
            params.forEach(p -> p.setDeviceId(deviceDTO.getId()));
            deviceParamService.saveOrUpdateParams(params);
        }

        if (Objects.nonNull(deviceDTO.getDeviceBind())) {
            DeviceSyncBindingDomainEvent event = DeviceSyncBindingDomainEvent.of(this, deviceDTO);
            SpringDomainEventPublisher.publish(event);
        }

        return true;
    }

    /**
     * 不限制租户，保证全局唯一
     *
     * @param sn 设备序列号
     * @param id id
     * @return boolean
     */
    @TenantIgnore
    private boolean checkSnRepeat(String sn, Long id) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", BladeConstant.DB_NOT_DELETED).eq("sn", sn);
        if (Func.isNotEmpty(id)) {
            queryWrapper.ne("id", id);
        }
        List<Device> devices = this.baseMapper.selectList(queryWrapper);
        return Func.isEmpty(devices);
    }

    @Override
    public Boolean addDeviceToGroup(Long groupId, List<Long> deviceIdList) {
        return this.lambdaUpdate().set(Device::getGroupId, groupId).set(Device::getHasGroup, 1).in(Device::getId, deviceIdList).update();
    }

    @Override
    public boolean removeGroup(List<Long> deviceIdList) {
        return this.lambdaUpdate().set(Device::getGroupId, null).set(Device::getHasGroup, 0).in(Device::getId, deviceIdList).update();
    }

    @Override
    public Device existBySn(String sn) {
        Device device = getBySn(sn);
        if (null == device || null == device.getId()) {
            LecentAssert.alertException("设备信息不存在，sn={}", sn);
        }
        return device;
    }

    @Override
    public Device getBySn(String sn) {
        return lambdaQuery().eq(Device::getSn, sn).one();
    }

    @Override
    public List<Device> getByOutDeviceId(String outDeviceId) {
        return Optional.ofNullable(outDeviceId)
            .map(t -> lambdaQuery().eq(Device::getOutDeviceId, outDeviceId).list())
            .orElse(null);
    }

    @Override
    public List<Device> onlineList() {
        return this.lambdaQuery().eq(Device::getNetworkStatus, NetworkStatus.ONLINE.getCode()).list();
    }

    @Override
    public boolean updateBindStatus(Long id, Integer bindStatus) {
        LecentAssert.notNull(id, "参数id不能为空");
        LecentAssert.notNull(bindStatus, "参数bindStatus不能为空");
        this.lambdaUpdate().set(Device::getBindStatus, bindStatus).eq(Device::getId, id).update();

        return true;
    }

    @Override
    public boolean updateNetworkStatus(Device device) {
        LecentAssert.notNull(device.getId(), "参数id不能为空");
        LecentAssert.notNull(device.getNetworkStatus(), "参数bindStatus不能为空");
        return this.lambdaUpdate().set(Device::getNetworkStatus, device.getNetworkStatus())
            .set(Device::getLastConnectTime, device.getLastConnectTime())
            .eq(Device::getId, device.getId())
            .update();
    }

    @Override
    public boolean updateOutDeviceId(Device device) {
        LecentAssert.notNull(device.getOutDeviceId(), "参数outDeviceId不能为空");
        return this.lambdaUpdate().set(Device::getOutDeviceId, device.getOutDeviceId())
            .eq(Device::getId, device.getId())
            .update();
    }

    @Override
    public void updateIfFirmwareVersionDiff(Device device, String latestFirmwareVersion) {
        if (Func.isBlank(latestFirmwareVersion) || latestFirmwareVersion.equals(device.getFirmwareVersion())) {
            return;
        }

        boolean updated = this.lambdaUpdate().set(Device::getFirmwareVersion, latestFirmwareVersion)
            .eq(Device::getId, device.getId())
            .update();

        if (updated) {
            DeviceCache.delKey(device.getSn());
        }

    }

    @Override
    public void updateIfIpAddrDiff(Device device, String latestIpAddr) {
        try {
            if (Func.isBlank(latestIpAddr) || latestIpAddr.equals(device.getIp())) {
                return;
            }

            boolean updated = this.lambdaUpdate().set(Device::getIp, latestIpAddr)
                .eq(Device::getId, device.getId())
                .update();

            if (updated) {
                DeviceCache.delKey(device.getSn());
            }
        } catch (Exception e) {
            log.error("更新设备IP地址失败", e);
        }
    }

    @Override
    public DeviceVO detail(Device device) {
        Device detail = getOne(Condition.getQueryWrapper(device));
        DeviceVO deviceVO = BeanUtil.copy(detail, DeviceVO.class);
        LecentAssert.notNull(deviceVO, "未查询到设备信息");
        return deviceVO;
    }

    @Override
    public List<Device> listByNodeType(String nodeType) {
        List<Long> products = this.productService.idsByNodeType(nodeType);
        if (products.isEmpty()) {
            return Collections.emptyList();
        }

        return this.lambdaQuery().in(Device::getProductId, products).list();
    }

    @Override
    public List<Device> listBySnLikeRight(String prefix) {
        if (Func.isBlank(prefix)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().likeRight(Device::getSn, prefix).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogic(@NotNull Long id) {
        LecentAssert.notNull(id, "参数[id]不能为空");
        return deleteLogic(ListUtil.of(id));
    }

    @Override
    public boolean deleteLogic(List<Long> ids) {
        LecentAssert.notNull(ids, "参数[ids]不能为空");
        LecentAssert.isTrue(!ids.isEmpty(), "参数[ids]长度必须大于0");

        // 1. 查询设备信息并检查绑定状态
        List<Device> devices = this.listByIds(ids);

        // 2. 如果有已绑定的设备，抛出异常
        List<String> snList = devices.stream().map(t -> {
            LecentAssert.isTrue(BindStatus.UN_BIND.getCode().equals(t.getBindStatus()), "绑定状态为[已绑定]状态的设备，需先解绑，再进行删除");
            return t.getSn();
        }).collect(Collectors.toList());

        // 3. 执行逻辑删除
        boolean success = super.deleteLogic(ids);
        LecentAssert.isTrue(success, "删除设备失败");

        // 4. 清理缓存
        snList.stream().filter(Func::isNoneBlank).forEach(DeviceCache::delKey);

        // 5. 发布领域事件
        SpringDomainEventPublisher.publish(DeviceSyncDeletedDomainEvent.of(this, devices));

        log.info("批量删除设备完成，共删除{}个设备", devices.size());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteWithGroupRemoval(List<Long> ids) {
        List<Long> groupIds = this.lambdaQuery()
            .in(Device::getId, ids)
            .list()
            .stream()
            .map(Device::getGroupId)
            .distinct()
            .collect(Collectors.toList());

        deleteLogic(ids);

        List<Long> removeGroupIds = groupIds.stream()
            .filter(groupId -> this.lambdaQuery().eq(Device::getGroupId, groupId).count() == 0)
            .collect(Collectors.toList());

        // 删除设备数为空的分组
        if (CollectionUtils.isNotEmpty(removeGroupIds)) {
            groupService.deleteLogic(removeGroupIds);
        }
        return true;
    }

    /**
     * 通过mac地址
     * 查询设备信息
     *
     * @param mac mac地址
     * @return Device
     */
    private Device oneByMac(String mac) {
        if (Func.isBlank(mac)) {
            return null;
        }
        return this.lambdaQuery().eq(Device::getMac, mac).one();
    }

    @Override
    public Map<String, Object> getRuntimeStatus(String sn) {
        Device device = getBySn(sn);
        LecentAssert.notNull(device, "未查询到设备信息");
        Map<String, Object> map = Optional.ofNullable(DeviceCache.getRuntimeStatusMap(sn)).orElse(new HashMap<>());
        map.put("network", NetworkStatus.OFFLINE.getCode() == device.getNetworkStatus() ? "0" : "1");

        return map;
    }

    private List<Device> listGatewayChild(Long parentId) {
        return Optional.ofNullable(parentId)
            .map(t -> this.lambdaQuery().eq(Device::getParentId, t).list())
            .orElse(Lists.newArrayList());
    }

    private int generateLockChannelId(Device gatewayDevice, List<Device> gatewayChildDevices) {
        if (gatewayChildDevices.isEmpty()) {
            return 1;
        }

        LecentAssert.isFalse(gatewayChildDevices.size() == 16, "超出网关设备通道上限数量限制15");
        int[] map = new int[15];
        for (Device gatewayChildDevice : gatewayChildDevices) {
            String[] split = gatewayChildDevice.getSn().split(StringPool.AT);
            boolean isValid = split.length == 2 && split[0].equals(gatewayDevice.getSn()) && Func.isNumeric(split[1]);
            LecentAssert.isTrue(isValid, "通道号自增业务异常，sn格式有误, sn={}", gatewayChildDevice.getSn());
            int channelId = Integer.parseInt(split[1]);
            if (channelId >= 1 && channelId <= 15) {
                map[channelId - 1] = 1;
            }
        }

        // 先分配奇数通道号（1、3、5...15）
        for (int i = 0; i < 15; i += 2) {
            if (map[i] == 0) {
                return i + 1;
            }
        }
        // 再分配偶数通道号（2、4、6...14）
        for (int i = 1; i < 15; i += 2) {
            if (map[i] == 0) {
                return i + 1;
            }
        }

        throw new ServiceException("通道号自增业务异常");
    }

    @Override
    public Device cacheDeviceGetBySn(String sn) {
        return DeviceCache.getBySn(sn);
    }

    @Override
    public Device getStatus(String id) {
        return this.baseMapper.getStatus(id);
    }

    @Override
    public List<Tree<Long>> listByPreviewTree() {
        List<Group> list = groupService.list();
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setChildrenKey("children");
        List<Tree<Long>> treeList = TreeUtil.build(list, 0L, treeNodeConfig, ((object, treeNode) -> {
            treeNode.setId(object.getId());
            treeNode.setParentId(object.getParentId());
            treeNode.setName(object.getName());
            List<Device> deviceList = baseMapper.selectList(new LambdaQueryWrapper<Device>()
                .eq(Device::getType, DeviceType.VIDEO_PILE.getValue())
                .eq(Device::getGroupId, object.getId()));
            List<DeviceTreeDTO> dtoList = new ArrayList<>();
            deviceList.forEach(e -> dtoList.add(BeanUtil.copy(e, DeviceTreeDTO.class)));
            treeNode.putExtra("deviceList", deviceList);
			/*
			// 腾讯云时时查询设备在线状态
			deviceList.forEach(e -> {
				DeviceTreeDTO dto = BeanUtil.copy(e, DeviceTreeDTO.class);
				if (StrUtil.isNotEmpty(e.getOutDeviceId())) {
					ResultDTO resultDTO = videoStreamContext.getDeviceStrategy(VideoStreamTypeEnum.TENCENT_IOT_VIDEO_INDUSTRY).getInfoByDeviceId(e.getOutDeviceId());
					if (resultDTO.getIsSuccess()) {
						JSONObject data = JSONUtil.parseObj(resultDTO);
						AllDeviceInfo allDeviceInfo = JSONUtil.parseObj(data.get("data")).get("Device", AllDeviceInfo.class);
						// 0：设备不在线；1：设备在线；2：设备隔离中；3：设备未注册
						dto.setDeviceStatus(allDeviceInfo.getStatus());
					} else {
						dto.setDeviceStatus(Long.valueOf(-1));
					}
				}
				dtoList.add(dto);
			});*/
        }));
        return treeList;
    }

    @Override
    public Map<String, Object> getDeviceStatus(String sn) {
        Device device = this.baseMapper.getDeviceStatus(sn);
        if (null == device) {
            return null;
        }
        Map<String, Object> map = Optional.ofNullable(DeviceCache.getRuntimeStatusMap(sn)).orElse(new HashMap<>());
        map.put("network", NetworkStatus.OFFLINE.getCode() == device.getNetworkStatus() ? "0" : "1");
        return map;
    }

    @Override
    public Device getDeviceInfoById(String id) {
        return this.baseMapper.getDeviceInfoById(id);
    }

    @Override
    public boolean uploadCoord(String sn, Double lng, Double lat) {
        Map<String, Object> map = new HashMap<>();
        map.put("sn", sn);
        map.put("lng", lng);
        map.put("lat", lat);
        map.put("time", DateUtil.formatDateTime(DateUtil.now()));
        map.put("user", AuthUtil.getUserName());
        String key = DeviceConstant.CACHE_DEVICE_COORD + AuthUtil.getTenantId();
        redis.hSet(key, sn, map);
        redis.expire(key, 60 * 60 * 24);
        return true;
    }

    @Override
    public List<Object> getCoordList() {
        return redis.hVals(DeviceConstant.CACHE_DEVICE_COORD + AuthUtil.getTenantId());
    }
}
