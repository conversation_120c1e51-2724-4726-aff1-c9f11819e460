package com.lecent.device.service.impl;

import com.lecent.device.domain.hik.HikEventXmlFileResolver;
import com.lecent.device.domain.hik.HikMultipartFileWrapper;
import com.lecent.device.domain.hik.HikXStream;
import com.lecent.device.domain.hik.event.HikEvent;
import com.lecent.device.domain.hik.event.HikEventType;
import com.lecent.device.dto.TCLRcvDTO;
import com.lecent.device.service.IHikVideoPileService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.request.BladeHttpServletRequestWrapper;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;

/**
 * Hik Video Pile
 * 海康视频桩
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class HikVideoPileServiceImpl implements IHikVideoPileService {

	@Override
	public void process(HttpServletRequest httpServletRequest) throws IOException {
		if (httpServletRequest instanceof BladeHttpServletRequestWrapper) {
			String requestBody = WebUtil.getRequestBody(httpServletRequest.getInputStream());
			log.info("HikVideoPileServiceImpl.process requestBody: {}", requestBody);
			return;
		}

		if (!(httpServletRequest instanceof MultipartHttpServletRequest)) {
			log.warn("HikVideoPileServiceImpl.process httpServletRequest not instanceof MultipartHttpServletRequest");
			return;
		}

		MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) httpServletRequest;
		HikMultipartFileWrapper multipartFileWrapper = HikMultipartFileWrapper.of(multiRequest.getMultiFileMap());
		TCLRcvDTO tclRcvDTO = null;
		// 按未知的事件类型先解析报文，获取到报文头中的事件类型(EventType)，再发布事件
		if(multipartFileWrapper.getXmlFile() !=null){
			tclRcvDTO = HikEventXmlFileResolver.build(HikXStream.of(HikEventType.UNKNOWN))
				.resolve(multipartFileWrapper.getXmlFile());
		}else if(multipartFileWrapper.getJSONFile()!=null){

			tclRcvDTO = HikEventXmlFileResolver.build(HikXStream.of(HikEventType.UNKNOWN))
				.resolveJson(multipartFileWrapper.getJSONFile());
		}
		checkTclRcvDTO(tclRcvDTO);
		HikEventType hikEventType = HikEventType.resolve(tclRcvDTO.getEventType());
		SpringUtil.publishEvent(new HikEvent<>(this, hikEventType, multipartFileWrapper));
	}

	private void checkTclRcvDTO(TCLRcvDTO tclRcvDTO) {
		if (Objects.isNull(tclRcvDTO)) {
			throw new ServiceException("解析 xml file 失败, tclRcvDTO is null");
		}
	}

}
