package com.lecent.device.controller.open;

import com.lecent.device.dto.upark.*;
import com.lecent.device.service.IUParkDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 宇视摄像头事件上报 Controller
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = {"/upark", "/api/upark"})
@Api(value = "宇视摄像头事件上报", tags = "宇视摄像头事件上报")
public class UParkDeviceController {

    private final IUParkDeviceService uParkDeviceService;

    @ApiOperation(value = "基础数据上报")
    @PostMapping("/basicinfo")
    public UParkResponse<Boolean> basicInfo(HttpServletRequest request) {
        String requestId = generateRequestId();
        try {
            String body = WebUtil.getRequestBody(request.getInputStream());
            log.info("[宇视摄像头事件][{}] 基础数据上报开始, 数据: {}", requestId, body);
            UParkBasicInfoDTO dto = JsonUtil.parse(body, UParkBasicInfoDTO.class);
            uParkDeviceService.handleBasicInfo(dto);
            log.info("[宇视摄像头事件][{}] 基础数据上报成功", requestId);
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] 基础数据上报失败", requestId, e);
        }
        return UParkResponse.success();
    }

    @ApiOperation(value = "心跳保活")
    @PostMapping("/keepalive")
    public UParkResponse<UParkKeepAliveVO> keepalive(HttpServletRequest request) {
        String requestId = generateRequestId();
        try {
            String body = WebUtil.getRequestBody(request.getInputStream());
            log.debug("[宇视摄像头事件][{}] 心跳保活开始, 数据: {}", requestId, body);
            UParkKeepAliveDTO dto = JsonUtil.parse(body, UParkKeepAliveDTO.class);
            uParkDeviceService.handleKeepAlive(dto);
            log.debug("[宇视摄像头事件][{}] 心跳保活成功", requestId);
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] 心跳保活失败", requestId, e);
        }
        return UParkResponse.success();
    }

    @ApiOperation(value = "车位告警")
    @PostMapping("/parkalarm")
    public UParkResponse<Boolean> parkAlarm(HttpServletRequest request) {
        String requestId = generateRequestId();
        try {
            String body = WebUtil.getRequestBody(request.getInputStream());
            // 清理多余的逗号
            body = body.replaceAll(",\\s*([}\\]])", "$1");
            UParkAlarmDTO dto = JsonUtil.parse(body, UParkAlarmDTO.class);

            List<UParkAlarmDTO.UParkPicInfoDTO> picInfo = dto.getParams().getPicInfo();
            logPictureInfo(requestId, "车位告警", picInfo);
            
            // 临时移除图片信息进行日志打印
            dto.getParams().setPicInfo(null);
            log.info("[宇视摄像头事件][{}] 车位告警处理开始, 数据: {}", requestId, JsonUtil.toJson(dto));
            dto.getParams().setPicInfo(picInfo);

            uParkDeviceService.handleParkAlarm(dto);
            log.info("[宇视摄像头事件][{}] 车位告警处理成功", requestId);
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] 车位告警处理失败", requestId, e);
        }
        return UParkResponse.success();
    }

    @ApiOperation(value = "车位状态")
    @PostMapping("/parkstatus")
    public UParkResponse<Boolean> parkStatus(HttpServletRequest request) {
        String requestId = generateRequestId();
        try {
            String body = WebUtil.getRequestBody(request.getInputStream());
            UParkStatusDTO dto = JsonUtil.parse(body, UParkStatusDTO.class);

            List<UParkStatusDTO.UParkPicInfoDTO> picInfo = dto.getParams().getPicInfo();
            logPictureInfo(requestId, "车位状态", picInfo);
            
            // 临时移除图片信息进行日志打印
            dto.getParams().setPicInfo(null);
            log.info("[宇视摄像头事件][{}] 车位状态处理开始, 数据: {}", requestId, JsonUtil.toJson(dto));
            dto.getParams().setPicInfo(picInfo);

            uParkDeviceService.handleParkStatus(dto);
            log.info("[宇视摄像头事件][{}] 车位状态处理成功", requestId);
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] 车位状态处理失败", requestId, e);
        }
        return UParkResponse.success();
    }

    @ApiOperation(value = "抓拍结果上报")
    @PostMapping("/capture")
    public UParkResponse<Boolean> capture(HttpServletRequest request) {
        String requestId = generateRequestId();
        try {
            String body = WebUtil.getRequestBody(request.getInputStream());
            UParkCaptureDTO dto = JsonUtil.parse(body, UParkCaptureDTO.class);

            List<UParkCaptureDTO.UParkPicInfoDTO> picInfo = dto.getData().getPicInfo();
            logPictureInfo(requestId, "抓拍结果上报", picInfo);
            
            // 临时移除图片信息进行日志打印
            dto.getData().setPicInfo(null);
            log.info("[宇视摄像头事件][{}] 抓拍结果上报处理开始, 数据: {}", requestId, JsonUtil.toJson(dto));
            dto.getData().setPicInfo(picInfo);

            uParkDeviceService.handleCapture(dto);
            log.info("[宇视摄像头事件][{}] 抓拍结果上报处理成功", requestId);
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] 抓拍结果上报处理失败", requestId, e);
        }
        return UParkResponse.success();
    }

    /**
     * 生成请求ID用于日志追踪
     */
    private String generateRequestId() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 记录图片信息日志
     * 
     * @param requestId 请求ID
     * @param eventType 事件类型
     * @param picInfoList 图片信息列表
     */
    private void logPictureInfo(String requestId, String eventType, List<?> picInfoList) {
        try {
            if (Func.isNotEmpty(picInfoList)) {
                int picCount = picInfoList.size();
                // 获取第一张图片的大小信息
                Integer totalSize = null;
                if (picInfoList.get(0) instanceof UParkAlarmDTO.UParkPicInfoDTO) {
                    totalSize = ((UParkAlarmDTO.UParkPicInfoDTO) picInfoList.get(0)).getSize();
                } else if (picInfoList.get(0) instanceof UParkStatusDTO.UParkPicInfoDTO) {
                    totalSize = ((UParkStatusDTO.UParkPicInfoDTO) picInfoList.get(0)).getSize();
                } else if (picInfoList.get(0) instanceof UParkCaptureDTO.UParkPicInfoDTO) {
                    totalSize = ((UParkCaptureDTO.UParkPicInfoDTO) picInfoList.get(0)).getSize();
                }
                
                if (totalSize != null) {
                    log.info("[宇视摄像头事件][{}] {} - 图片信息: 数量={}, 首张图片大小={}B",
                        requestId, eventType, picCount, totalSize);
                } else {
                    log.info("[宇视摄像头事件][{}] {} - 图片信息: 数量={}", 
                        requestId, eventType, picCount);
                }
            } else {
                log.warn("[宇视摄像头事件][{}] {} - 图片信息为空", requestId, eventType);
            }
        } catch (Exception e) {
            log.error("[宇视摄像头事件][{}] {} - 记录图片信息日志失败: {}", requestId, eventType, e.getMessage());
        }
    }
}