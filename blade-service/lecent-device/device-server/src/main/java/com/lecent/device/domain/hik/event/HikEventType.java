package com.lecent.device.domain.hik.event;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.xml.resolver.apps.resolver;

/**
 * 海康摄像头事件类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum HikEventType {

	/**
	 * 未知
	 */
	UNKNOWN("unknown"),
	/**
	 * 识别
	 */
	TFS("tfs"),
	/**
	 * 自动抓拍
	 */
	AID("aid"),
	/**
	 * 遮挡告警
	 */
	SHELTER_ALARM("shelterAlarm"),

	/**
	 * 定时抓拍
	 */
	PACKING_SPACE_RECOGNITION("PackingSpaceRecognition"),

	;

	private final String type;

	public static boolean contains(String name) {
		for (HikEventType eventType : HikEventType.values()) {
			if (eventType.name().equalsIgnoreCase(name)) {
				return true;
			}
		}

		return false;
	}

	public static HikEventType resolve(String type) {
		for (HikEventType eventType : HikEventType.values()) {
			if (eventType.type.equalsIgnoreCase(type)) {
				return eventType;
			}
		}

		return HikEventType.UNKNOWN;
	}

	public static HikEventType valueOfName(String name) {
		for (HikEventType eventType : HikEventType.values()) {
			if (eventType.name().equalsIgnoreCase(name)) {
				return eventType;
			}
		}

		return HikEventType.UNKNOWN;
	}

	public boolean isUnknown() {
		return this.equals(HikEventType.UNKNOWN);
	}

}
