package com.lecent.device.dto.event;

import com.lecent.device.dto.DeviceDTO;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

/**
 * 旧设备同步领域事件
 *
 * <AUTHOR>
 */
public class DeviceSyncDomainEvent extends AbstractDomainEvent<DeviceDTO, DomainEventType> {

    protected DeviceSyncDomainEvent(Object source, DomainEventType eventType, DeviceDTO payload) {
        super(source, eventType, payload);
    }

    public static DeviceSyncDomainEvent ofCreated(Object source, DeviceDTO payload) {
        return new DeviceSyncDomainEvent(source, EventType.CREATED, payload);
    }

    public boolean isCreated() {
        return EventType.CREATED.equals(getEventType());
    }

    public boolean isUpdated() {
        return EventType.UPDATED.equals(getEventType());
    }

    public enum EventType implements DomainEventType {
        CREATED,
        UPDATED,
    }
}
