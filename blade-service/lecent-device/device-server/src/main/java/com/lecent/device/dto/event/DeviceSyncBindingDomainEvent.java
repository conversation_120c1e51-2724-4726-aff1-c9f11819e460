package com.lecent.device.dto.event;

import com.lecent.device.dto.DeviceDTO;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

/**
 * 旧设备同步绑定领域事件
 *
 * <AUTHOR>
 */
public class DeviceSyncBindingDomainEvent extends AbstractDomainEvent<DeviceDTO, DomainEventType> {

    protected DeviceSyncBindingDomainEvent(Object source, DomainEventType eventType, DeviceDTO payload) {
        super(source, eventType, payload);
    }

    public static DeviceSyncBindingDomainEvent of(Object source, DeviceDTO payload) {
        return new DeviceSyncBindingDomainEvent(source, EventType.BINDING, payload);
    }

    public enum EventType implements DomainEventType {
        BINDING,
    }
}
