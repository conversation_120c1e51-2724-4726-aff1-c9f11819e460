package com.lecent.device.domain.hik;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springblade.common.enums.FileExtension;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.FileUtil;
import org.springframework.lang.Nullable;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HikMultipartFileWrapper {

	public static final String XML_FILENAME = "event.xml";

	public static final String JSON_FILENAME="PackingSpaceRecognition.json";
	public static final String PLATE_IMAGE_FILENAME = "licensePlatePicture.jpg";
	public static final String SCENE_IMAGE_FILENAME = "detectionPicture.jpg";

	private MultiValueMap<String, MultipartFile> multipartFileMap;

	public HikMultipartFileWrapper(MultiValueMap<String, MultipartFile> multipartFileMap) {
		this.multipartFileMap = multipartFileMap;
	}

	public static HikMultipartFileWrapper of(MultiValueMap<String, MultipartFile> multiFileMap) {
		if (Objects.isNull(multiFileMap)) {
			throw new ServiceException("multiFileMap must not null");
		}

		MultiValueMap<String, MultipartFile> fileMap = new LinkedMultiValueMap<>();
		multiFileMap.forEach((k, v) ->
			v.stream().filter(t -> Objects.nonNull(t.getOriginalFilename()))
				.forEach(file -> {
					String fileExtension = FileUtil.getFileExtension(file.getOriginalFilename());
					String filename = FileExtension.XML.equalsIgnoreCase(fileExtension) ? XML_FILENAME : file.getOriginalFilename();
					fileMap.add(filename, file);
				})
		);
		return new HikMultipartFileWrapper(fileMap);
	}

	@Nullable
	public MultipartFile getXmlFile() {
		return getMultipartFileMapOpt().map(t -> t.getFirst(XML_FILENAME)).orElse(null);
	}
	@Nullable
	public MultipartFile getJSONFile() {
		return getMultipartFileMapOpt().map(t -> t.getFirst(JSON_FILENAME)).orElse(null);
	}

	public List<MultipartFile> getPlateImageFiles() {
		return getMultipartFileMapOpt().map(t -> t.get(PLATE_IMAGE_FILENAME)).orElseGet(Lists::newArrayList);
	}

	public List<MultipartFile> getSceneImageFiles() {
		return getMultipartFileMapOpt().map(t -> t.get(SCENE_IMAGE_FILENAME)).orElseGet(Lists::newArrayList);
	}

	public List<MultipartFile> getImageFiles() {
		List<MultipartFile> imageFiles = Lists.newArrayList();
		getMultipartFileMapOpt().ifPresent(map ->
			map.forEach((k, v) ->
				v.stream().filter(t -> Objects.nonNull(t.getOriginalFilename()))
					.forEach(file -> {
						String fileExtension = FileUtil.getFileExtension(file.getOriginalFilename());
						if (FileExtension.isImageFile(fileExtension)) {
							imageFiles.add(file);
						}
					})
			));
		return imageFiles;
	}

	public Optional<MultiValueMap<String, MultipartFile>> getMultipartFileMapOpt() {
		return Optional.ofNullable(multipartFileMap);
	}

}
