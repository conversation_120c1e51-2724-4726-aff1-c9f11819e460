package com.lecent.device.domain.hik;

import com.lecent.device.dto.TCLRcvDTO;
import com.thoughtworks.xstream.XStream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.IoUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 海康事件xml文件解析器
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HikEventXmlFileResolver {

	private XStream xStream;

	public HikEventXmlFileResolver(XStream xStream) {
		this.xStream = xStream;
	}

	public static HikEventXmlFileResolver build(XStream xStream) {
		return new HikEventXmlFileResolver(xStream);
	}

	public TCLRcvDTO resolve(MultipartFile xmlFile) {
		String xml = read(xmlFile);
		return resolve(xml);
	}
	public TCLRcvDTO resolveJson(MultipartFile xmlFile) {
		String json = read(xmlFile);
		return Func.readJson(json,TCLRcvDTO.class);
	}

	public TCLRcvDTO resolve(String xml) {
		return (TCLRcvDTO) xStream.fromXML(xml);
	}


	public String read(MultipartFile xmlFile) {
		LecentAssert.notNull(xmlFile, "xml file must not null");
		try {
			return IoUtil.readToString(xmlFile.getInputStream());
		} catch (IOException e) {
			log.error("read xml multipartFile failed ", e);
			throw new ServiceException("read xml multipartFile failed, " + e.getMessage());
		}
	}

}
