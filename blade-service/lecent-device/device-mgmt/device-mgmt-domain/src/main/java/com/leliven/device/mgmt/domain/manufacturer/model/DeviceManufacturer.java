package com.leliven.device.mgmt.domain.manufacturer.model;

import com.leliven.ddd.core.model.BaseDomain;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 设备厂商 领域对象
 *
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class DeviceManufacturer extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 厂商名称
     */
    private String name;

    /**
     * 厂商编码
     */
    private String code;


	public DeviceManufacturer(String name, String code) {
		this(null, name, code);
	}

    public DeviceManufacturer(Long id, String name, String code) {
		this.id = id;
        this.name = name;
        this.code = code;
    }
}
