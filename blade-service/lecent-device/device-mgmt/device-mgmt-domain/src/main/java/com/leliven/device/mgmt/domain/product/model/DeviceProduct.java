package com.leliven.device.mgmt.domain.product.model;

import com.leliven.device.mgmt.domain.product.model.valueobject.DeviceNodeType;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.ddd.core.model.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备产品
 *
 * <p>暂时命名，避免和旧的Product类冲突<p/>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceProduct extends BaseDomain {

    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品图片链接
     */
    private String imageUrl;
    /**
     * 产品型号
     */
    private String model;
    /**
     * 设备类型
     */
    private DeviceType deviceType;
    /**
	 * 节点类型
	 */
	private DeviceNodeType nodeType;
    /**
     * 厂商ID
     */
    private Long manufacturerId;
    /**
	 * 产品描述
	 */
	private String desc;
}
