package com.leliven.device.mgmt.domain.info;

import com.leliven.device.mgmt.domain.info.model.valueobject.DeviceBindingType;
import com.leliven.ddd.core.model.BaseDomain;
import lombok.Getter;

/**
 * 停车设备绑定
 *
 * <AUTHOR>
 * @since 2024/11/14
 */
@Getter
public class ParkingDeviceBinding extends BaseDomain {

    /**
     * 所属停车场ID
     */
    private Long parklotId;
    /**
     * 绑定类型
     */
    private DeviceBindingType bindingType;
}
