package com.leliven.device.mgmt.domain.product.support;

import com.leliven.device.mgmt.domain.product.DeviceProductGateway;
import com.leliven.device.mgmt.domain.product.model.DeviceProduct;
import com.leliven.device.mgmt.domain.product.support.validator.DeviceProductValidator;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;

/**
 * 设备厂商 领域服务
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class DeviceProductDomainService {

    private final DeviceProductValidator productValidator;
    private final DeviceProductGateway productRepository;

    public DeviceProduct get(Long id) {
        return this.productRepository.getOne(id);
    }

    public void add(DeviceProduct product) {
        this.productValidator.verifyAddRule(product);
        this.productRepository.save(product);
    }

    public void edit(DeviceProduct product) {
        this.productRepository.update(product);
    }
}
