package com.leliven.device.mgmt.infra.gateway.persistence.converter;

import com.leliven.device.mgmt.domain.product.model.DeviceProduct;
import com.leliven.device.mgmt.infra.gateway.persistence.mysql.dataobject.DeviceProductDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;

/**
 * 产品信息 领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S6548"})
public class DeviceProductConverter extends AbstractDomainEntityConverter<DeviceProduct, DeviceProductDO> {

    public static final DeviceProductConverter INSTANCE = new DeviceProductConverter();

    @Override
    protected DeviceProduct doFromDO(DeviceProductDO dataObject) {
       return null;
    }

    @Override
    protected DeviceProductDO doToDO(DeviceProduct domainObject) {
       return null;
    }
}
