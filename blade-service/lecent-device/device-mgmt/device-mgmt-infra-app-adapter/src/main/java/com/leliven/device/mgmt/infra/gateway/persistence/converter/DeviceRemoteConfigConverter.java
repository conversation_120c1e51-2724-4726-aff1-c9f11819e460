package com.leliven.device.mgmt.infra.gateway.persistence.converter;

import com.leliven.device.mgmt.domain.remote.model.DeviceRemoteConfig;
import com.leliven.device.mgmt.infra.gateway.persistence.mysql.dataobject.DeviceRemoteConfigDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 设备远程配置转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceRemoteConfigConverter extends DomainEntityConverter<DeviceRemoteConfig, DeviceRemoteConfigDO> {

    DeviceRemoteConfigConverter INSTANCE = Mappers.getMapper(DeviceRemoteConfigConverter.class);

    /**
     * 从数据对象转换为领域对象
     *
     * @param dataObject 数据对象
     * @return 领域对象
     */
    @Override
    DeviceRemoteConfig fromDO(DeviceRemoteConfigDO dataObject);

    /**
     * 从领域对象转换为数据对象
     *
     * @param domainObject 领域对象
     * @return 数据对象
     */
    @Override
    DeviceRemoteConfigDO toDO(DeviceRemoteConfig domainObject);
}
