package com.leliven.device.mgmt.infra.gateway.persistence.converter;

import com.leliven.device.mgmt.domain.manufacturer.model.DeviceManufacturer;
import com.leliven.device.mgmt.infra.gateway.persistence.mysql.dataobject.DeviceManufacturerDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;

/**
 * 设备厂商 领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S6548"})
public class DeviceManufacturerConverter extends AbstractDomainEntityConverter<DeviceManufacturer, DeviceManufacturerDO> {

    public static final DeviceManufacturerConverter INSTANCE = new DeviceManufacturerConverter();

    @Override
    protected DeviceManufacturer doFromDO(DeviceManufacturerDO dataObject) {
       return new DeviceManufacturer(dataObject.getName(), dataObject.getCode());
    }

    @Override
    protected DeviceManufacturerDO doToDO(DeviceManufacturer domainObject) {
        DeviceManufacturerDO dataobject = new DeviceManufacturerDO();
        dataobject.setName(domainObject.getName());
        dataobject.setCode(domainObject.getCode());
        return dataobject;
    }
}
