package com.leliven.park.domain.parking.core.support.specialexit;

import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingCompensateExitInfo;
import com.leliven.ddd.core.event.DomainEventPublisher;
import com.leliven.vehicle.model.Vehicle;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 基于触发时间优先级的复合特殊出场处理器测试
 *
 * <AUTHOR>
 */
public class CompositeTimePriorityCompensateExitHandlerTest {

    @Mock
    private ParkingCompensateExitInfoProvider provider1;
    @Mock
    private ParkingCompensateExitInfoProvider provider2;
    @Mock
    private ParkingCompensateExitInfoProvider provider3;
    @Mock
    private DomainEventPublisher eventPublisher;

    @Mock
    private ParkingOrderAgg parkingOrderAgg;

    private CompositeTimePriorityCompensateExitHandler handler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        List<ParkingCompensateExitInfoProvider> providers = Arrays.asList(provider1, provider2, provider3);
        handler = new CompositeTimePriorityCompensateExitHandler(providers);
        // 使用 setter 注入 mock 的事件发布器
        handler.setDomainEventPublisher(eventPublisher);
    }

    @Test
    public void constructor_shouldCreateCopyOfProvidersList() {
        // 准备测试数据
        List<ParkingCompensateExitInfoProvider> originalProviders = Arrays.asList(provider1, provider2);

        // 执行测试
        CompositeTimePriorityCompensateExitHandler testHandler = new CompositeTimePriorityCompensateExitHandler(originalProviders);

        // 验证结果 - 通过反射获取私有字段
        List<ParkingCompensateExitInfoProvider> actualProviders = testHandler.getProviders();

        // 验证列表内容相同但不是同一个对象
        assertEquals(originalProviders.size(), actualProviders.size());
        assertTrue(actualProviders.containsAll(originalProviders));
        assertNotSame(originalProviders, actualProviders);
    }

    @Test
    public void supports_whenOrderIsOnRoad_shouldReturnTrue() {
        // 准备测试数据
        when(parkingOrderAgg.isOnRoadOrder()).thenReturn(true);

        // 执行测试
        boolean result = handler.supports(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
        verify(parkingOrderAgg).isOnRoadOrder();
    }

    @Test
    public void supports_whenOrderIsNotOnRoad_shouldReturnFalse() {
        // 准备测试数据
        when(parkingOrderAgg.isOnRoadOrder()).thenReturn(false);

        // 执行测试
        boolean result = handler.supports(parkingOrderAgg);

        // 验证结果
        assertFalse(result);
        verify(parkingOrderAgg).isOnRoadOrder();
    }

    @Test
    public void doHandle_whenExitInfoExists_shouldReturnTrue() {
        // 准备测试数据
        ParkingCompensateExitInfo exitInfo = createExitInfo(new Date());

        when(provider1.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.of(exitInfo));
        when(provider2.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());
        when(provider3.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());

        // 执行测试
        boolean result = handler.doHandle(parkingOrderAgg);

        // 验证结果 - 我们只能验证返回值，不验证静态方法调用
        assertTrue(result);
        verify(provider1).getExitInfoIfSupported(parkingOrderAgg);
    }

    @Test
    public void doHandle_whenNoExitInfo_shouldReturnFalse() {
        // 准备测试数据
        when(provider1.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());
        when(provider2.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());
        when(provider3.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());

        // 执行测试
        boolean result = handler.doHandle(parkingOrderAgg);

        // 验证结果
        assertFalse(result);
        verify(provider1).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider2).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider3).getExitInfoIfSupported(parkingOrderAgg);
    }

    @Test
    public void doHandle_whenProvidersListIsEmpty_shouldReturnFalse() {
        // 准备测试数据
        handler = new CompositeTimePriorityCompensateExitHandler(Collections.emptyList());

        // 执行测试
        boolean result = handler.doHandle(parkingOrderAgg);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void getLatestExitInfo_shouldSelectLatestExitInfo() {
        // 准备测试数据 - 创建三个不同时间的出场信息
        Date oldestDate = new Date(System.currentTimeMillis() - 3000);
        Date middleDate = new Date(System.currentTimeMillis() - 2000);
        Date latestDate = new Date(System.currentTimeMillis() - 1000);

        ParkingCompensateExitInfo oldestInfo = createExitInfo(oldestDate);
        ParkingCompensateExitInfo middleInfo = createExitInfo(middleDate);
        ParkingCompensateExitInfo latestInfo = createExitInfo(latestDate);

        when(provider1.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.of(oldestInfo));
        when(provider2.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.of(middleInfo));
        when(provider3.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.of(latestInfo));

        // 执行测试
        Optional<ParkingCompensateExitInfo> result = handler.getLatestExitInfo(parkingOrderAgg);

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(latestDate, result.get().getExitTime());
        verify(provider1).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider2).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider3).getExitInfoIfSupported(parkingOrderAgg);
    }

    @Test
    public void getLatestExitInfo_whenNoProviderReturnsExitInfo_shouldReturnEmpty() {
        // 准备测试数据
        when(provider1.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());
        when(provider2.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());
        when(provider3.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.empty());

        // 执行测试
        Optional<ParkingCompensateExitInfo> result = handler.getLatestExitInfo(parkingOrderAgg);

        // 验证结果
        assertFalse(result.isPresent());
        verify(provider1).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider2).getExitInfoIfSupported(parkingOrderAgg);
        verify(provider3).getExitInfoIfSupported(parkingOrderAgg);
    }

    @Test
    public void handle_whenSupportsOrder_shouldCallDoHandle() {
        // 准备测试数据
        when(parkingOrderAgg.isOnRoadOrder()).thenReturn(true);
        ParkingCompensateExitInfo exitInfo = createExitInfo(new Date());
        when(provider1.getExitInfoIfSupported(parkingOrderAgg)).thenReturn(Optional.of(exitInfo));

        // 执行测试
        boolean result = handler.handle(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
        verify(parkingOrderAgg).isOnRoadOrder();
        verify(provider1).getExitInfoIfSupported(parkingOrderAgg);
    }

    @Test
    public void handle_whenDoesNotSupportOrder_shouldReturnFalse() {
        // 准备测试数据
        when(parkingOrderAgg.isOnRoadOrder()).thenReturn(false);

        // 执行测试
        boolean result = handler.handle(parkingOrderAgg);

        // 验证结果
        assertFalse(result);
        verify(parkingOrderAgg).isOnRoadOrder();
        verifyNoMoreInteractions(provider1, provider2, provider3);
    }

    /**
     * 创建测试用的出场信息
     */
    private ParkingCompensateExitInfo createExitInfo(Date exitTime) {
        return ParkingCompensateExitInfo.builder()
                .parkingOrderId(1L)
                .placeId(2L)
                .vehicle(Vehicle.of("粤B12345"))
                .exitTime(exitTime)
                .imageUrl("https://example.com/image.jpg")
                .triggerType(ParkingTriggerWay.SYSTEM_COMPENSATORY_REGISTER_BY_SNAP)
                .build();
    }
}
