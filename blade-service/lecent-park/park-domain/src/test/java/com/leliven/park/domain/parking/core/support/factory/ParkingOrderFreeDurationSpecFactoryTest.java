package com.leliven.park.domain.parking.core.support.factory;

import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.common.PayStatus;
import com.leliven.park.domain.order.parking.entity.ParkingEventInfo;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.temp.TempParkingOrderRepositoryI;
import com.leliven.park.domain.order.temp.model.TempParkingOrderCollection;
import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.ddd.core.specification.Specification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@DisplayName("停车免费时长规约工厂测试")
class ParkingOrderFreeDurationSpecFactoryTest {

    private TempParkingOrderRepositoryI tempParkingOrderRepository;
    private ParkingOrderFreeDurationSpecFactory factory;
    private TempParkingBillingRuleCfg chargeRegulation;

    @BeforeEach
    void setUp() {
        tempParkingOrderRepository = mock(TempParkingOrderRepositoryI.class);
        factory = new ParkingOrderFreeDurationSpecFactory(
            tempParkingOrderRepository, new ParkingFreeDurationSpecFactory());
        chargeRegulation = mock(TempParkingBillingRuleCfg.class);
    }

    @Test
    @DisplayName("在免费时长内满足 EntryFreeDurationSpec")
    void entryFreeDurationSpecWithinFreeDuration() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createEntryFreeDurationSpec(chargeRegulation);

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("超过免费时长不满足 EntryFreeDurationSpec")
    void entryFreeDurationSpecOutsideFreeDuration() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createEntryFreeDurationSpec(chargeRegulation);

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("在支付后免费离场时长内满足 PaymentFreeLeaveDurationSpec")
    void paymentFreeLeaveDurationSpecWithinFreeLeaveDuration() {
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createPaidFreeLeaveDurationSpec(chargeRegulation);

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("超过支付后免费离场时长不满足 PaymentFreeLeaveDurationSpec")
    void paymentFreeLeaveDurationSpecOutsideFreeLeaveDuration() {
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createPaidFreeLeaveDurationSpec(chargeRegulation);

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(any()))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("如果一个规约满足，DisjunctionSpecification 满足")
    void disjunctionSpecificationOneSpecSatisfied() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation);

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("在免费时长内和支付后免费离场时长内都满足")
    void bothSpecsSatisfied() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation);

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("在免费时长内，但不在支付后免费离场时长内")
    void entryFreeDurationSpecSatisfiedPaidFreeLeaveDurationSpecNotSatisfied() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation);

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(any()))
            .thenReturn(new TempParkingOrderCollection());

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("不在免费时长内，但在支付后免费离场时长内")
    void entryFreeDurationSpecNotSatisfiedPaidFreeLeaveDurationSpecSatisfied() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation);

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("如果没有规约满足，DisjunctionSpecification 不满足")
    void disjunctionSpecificationNoSpecSatisfied() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation);

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(any()))
            .thenReturn(new TempParkingOrderCollection());

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("在免费时长内和支付后免费离场时长内都满足，DisjunctionSpecification.not() 不满足")
    void bothSpecsSatisfiedNot() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation).not();

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("在免费时长内但不在支付后免费离场时长内，DisjunctionSpecification.not() 不满足")
    void entryFreeDurationSpecSatisfiedPaidFreeLeaveDurationSpecNotSatisfiedNot() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation).not();

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(any()))
            .thenReturn(new TempParkingOrderCollection());

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("不在免费时长内，但在支付后免费离场时长内，DisjunctionSpecification.not() 不满足")
    void entryFreeDurationSpecNotSatisfiedPaidFreeLeaveDurationSpecSatisfiedNot() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation).not();

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(10).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        assertFalse(spec.isSatisfiedBy(order));
    }

    @Test
    @DisplayName("不处于免费时长内，订单未支付过，DisjunctionSpecification.not() 满足")
    void disjunctionSpecificationNoSpecSatisfiedNot() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation).not();

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(any()))
            .thenReturn(new TempParkingOrderCollection());

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }

     @Test
    @DisplayName("不处于免费时长内，也不处于支付后免费离场时长内，DisjunctionSpecification.not() 满足")
    void disjunctionSpecificationNo_whenNotWithinFreeDuration_returnTrue() {
        when(chargeRegulation.getFreeDuration()).thenReturn(Duration.ofMinutes(15));
        when(chargeRegulation.getFreeLeaveDurationAfterPaid()).thenReturn(Duration.ofMinutes(15));
        Specification<ParkingOrderAgg> spec = factory.createParkingFreeDurationSpec(chargeRegulation).not();

        TempParkingOrderDomain paidOrder = new TempParkingOrderDomain();
        paidOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        paidOrder.setPayTime(new Date(System.currentTimeMillis() - Duration.ofMinutes(20).toMillis()));

        when(tempParkingOrderRepository.getCollectionByParkingOrderId(1L))
            .thenReturn(new TempParkingOrderCollection(Collections.singletonList(paidOrder)));

        ParkingOrderAgg order = new ParkingOrderAgg();
        order.setId(1L);
        order.setEnterInfo(new ParkingEventInfo());
        order.getEnterInfo().setDatetime(new Date(System.currentTimeMillis() - Duration.ofMinutes(40).toMillis()));

        assertTrue(spec.isSatisfiedBy(order));
    }
}
