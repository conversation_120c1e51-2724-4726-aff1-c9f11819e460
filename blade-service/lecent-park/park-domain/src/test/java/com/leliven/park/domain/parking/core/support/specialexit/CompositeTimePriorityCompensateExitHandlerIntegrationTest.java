package com.leliven.park.domain.parking.core.support.specialexit;

import cn.hutool.core.math.Money;
import com.google.common.collect.Lists;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCaptureRecord;
import com.leliven.park.domain.common.PayStatus;
import com.leliven.park.domain.order.parking.entity.ParkingEventInfo;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.temp.TempParkingOrderRepositoryI;
import com.leliven.park.domain.order.temp.model.TempParkingOrderCollection;
import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.park.domain.parking.core.event.ParkingCompensateExitSelectedEvent;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingCompensateExitInfo;
import com.leliven.ddd.core.event.DomainEventPublisher;
import com.leliven.vehicle.model.Vehicle;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import static com.lecent.park.en.parklot.ParkingStatusEnum.PARK_IN;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 使用模拟处理器的集成测试
 *
 * <AUTHOR>
 */
public class CompositeTimePriorityCompensateExitHandlerIntegrationTest {

    @Mock
    private ParkingPlaceGateway parkingPlaceGateway;
    @Mock
    private TempParkingOrderRepositoryI tempParkingOrderRepository;
    @Mock
    private DomainEventPublisher eventPublisher;

    private ParkingOrderAgg parkingOrderAgg;
    private CompositeTimePriorityCompensateExitHandler compositeHandler;

    /**
     * 2025-05-21 12:00:00
     */
    private static final long FIXED_TIME = 1747800000000L;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 创建测试用的停车订单
        parkingOrderAgg = new ParkingOrderAgg();
        parkingOrderAgg.setId(1001L);
        parkingOrderAgg.setPlaceId(2001L);
        parkingOrderAgg.setParkingStatus(PARK_IN);
        parkingOrderAgg.getParkingCost().addActualReceiveAmount(new Money(1));
        parkingOrderAgg.setOtherFlag(1);
        // 设置入场信息
        ParkingEventInfo enterInfo = new ParkingEventInfo();
        enterInfo.setDatetime(new Date(FIXED_TIME - 3600000)); // 1小时前
        enterInfo.setVehicle(Vehicle.of("粤B12345"));
        parkingOrderAgg.setEnterInfo(enterInfo);

        // 创建实际的处理器
        ParkingAutoCaptureExitHandler autoCaptureHandler = new ParkingAutoCaptureExitHandler(parkingPlaceGateway);
        ParkingPaidCompensateExitHandler paidExitHandler = new ParkingPaidCompensateExitHandler(tempParkingOrderRepository);

        // 注入事件发布器
        autoCaptureHandler.setDomainEventPublisher(eventPublisher);
        paidExitHandler.setDomainEventPublisher(eventPublisher);

        // 创建复合处理器
        compositeHandler = new CompositeTimePriorityCompensateExitHandler(
                Arrays.asList(autoCaptureHandler, paidExitHandler));
        compositeHandler.setDomainEventPublisher(eventPublisher);
    }

    @Test
    public void shouldSelectPaidExitInfo_whenBothProvidersSupported_payTimeIsLaterThanCaptureTime() {
        // 准备测试数据 - 模拟抓拍记录
        Date captureTime = new Date(FIXED_TIME - 10 * 60 * 1000L); // 10分钟前
        ParkingPlaceCameraCaptureRecord captureRecord = new ParkingPlaceCameraCaptureRecord();
        captureRecord.setCaptureTime(captureTime);
        captureRecord.setVehicle(parkingOrderAgg.getEnterInfo().getVehicle());
        captureRecord.setImageUrl("https://example.com/capture.jpg");
        captureRecord.setDeviceType(DeviceType.VIDEO_PILE);

        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
            eq(parkingOrderAgg.getPlaceId()),
            eq(parkingOrderAgg.getEnterInfo().getDatetime()),
            any(Date.class),
            eq(parkingOrderAgg.getEnterInfo().getVehicle().getPlate()))
        ).thenReturn(Optional.of(captureRecord));

        // 准备测试数据 - 模拟支付记录
        Date payTime = new Date(FIXED_TIME - 5 * 60 *1000); // 5分钟前（比抓拍时间更晚）
        TempParkingOrderDomain tempParkingOrder = TempParkingOrderDomain.create();
        tempParkingOrder.setPayTime(payTime);
        tempParkingOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection(Lists.newArrayList(tempParkingOrder));
        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
                .thenReturn(tempParkingOrderCollection);

        // 执行测试
        boolean result = compositeHandler.handle(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
         // 捕获事件参数
        ArgumentCaptor<ParkingCompensateExitSelectedEvent> eventCaptor = ArgumentCaptor.forClass(ParkingCompensateExitSelectedEvent.class);
        verify(eventPublisher, times(1)).publishEvent(eventCaptor.capture());
        ParkingCompensateExitSelectedEvent event = eventCaptor.getValue();
        ParkingCompensateExitInfo exitInfo = event.getPayload();
        assertNotNull(exitInfo);
        assertEquals(payTime, exitInfo.getExitTime());
        assertEquals(ParkingTriggerWay.SYSTEM_COMPENSATORY_REGISTER_BY_PAID, exitInfo.getTriggerType());
    }

    @Test
    public void shouldSelectCaptureExitInfo_whenBothProvidersSupported_captureTimeIsLaterThanPayTime() {
        // 准备测试数据 - 模拟抓拍记录
        ParkingPlaceCameraCaptureRecord captureRecord = new ParkingPlaceCameraCaptureRecord();
        // 10分钟前
        captureRecord.setCaptureTime(new Date(FIXED_TIME - 10 * 60 * 1000L));
        captureRecord.setVehicle(parkingOrderAgg.getEnterInfo().getVehicle());
        captureRecord.setImageUrl("https://example.com/capture.jpg");
        captureRecord.setDeviceType(DeviceType.VIDEO_PILE);

        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
            eq(parkingOrderAgg.getPlaceId()),
            eq(parkingOrderAgg.getEnterInfo().getDatetime()),
            any(Date.class),
            eq(parkingOrderAgg.getEnterInfo().getVehicle().getPlate()))
        ).thenReturn(Optional.of(captureRecord));

        // 准备测试数据 - 模拟支付记录
        // 15分钟前（比抓拍时间早）
        Date payTime = new Date(FIXED_TIME - 15 * 60 *1000);
        TempParkingOrderDomain tempParkingOrder = TempParkingOrderDomain.create();
        tempParkingOrder.setPayTime(payTime);
        tempParkingOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection(Lists.newArrayList(tempParkingOrder));
        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
                .thenReturn(tempParkingOrderCollection);

        // 执行测试
        boolean result = compositeHandler.handle(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
         // 捕获事件参数
        ArgumentCaptor<ParkingCompensateExitSelectedEvent> eventCaptor = ArgumentCaptor.forClass(ParkingCompensateExitSelectedEvent.class);
        verify(eventPublisher, times(1)).publishEvent(eventCaptor.capture());
        ParkingCompensateExitSelectedEvent event = eventCaptor.getValue();
        ParkingCompensateExitInfo exitInfo = event.getPayload();
        assertNotNull(exitInfo);
        assertEquals(captureRecord.getCaptureTime(), exitInfo.getExitTime());
        assertEquals(ParkingTriggerWay.SYSTEM_COMPENSATORY_REGISTER_BY_SNAP, exitInfo.getTriggerType());
    }

    @Test
    public void shouldSelectCaptureExitInfo_whenBothProvidersSupported_captureTimeAndPayTimeAreSame() {
        // 准备测试数据 - 模拟抓拍记录
        Date sameTime = new Date(FIXED_TIME - 10 * 60 * 1000L); // 10分钟前
        ParkingPlaceCameraCaptureRecord captureRecord = new ParkingPlaceCameraCaptureRecord();
        captureRecord.setCaptureTime(sameTime);
        captureRecord.setVehicle(parkingOrderAgg.getEnterInfo().getVehicle());
        captureRecord.setImageUrl("https://example.com/capture.jpg");
        captureRecord.setDeviceType(DeviceType.VIDEO_PILE);

        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
            eq(parkingOrderAgg.getPlaceId()),
            eq(parkingOrderAgg.getEnterInfo().getDatetime()),
            any(Date.class),
            eq(parkingOrderAgg.getEnterInfo().getVehicle().getPlate()))
        ).thenReturn(Optional.of(captureRecord));

        // 准备测试数据 - 模拟支付记录
        TempParkingOrderDomain tempParkingOrder = TempParkingOrderDomain.create();
        tempParkingOrder.setPayTime(sameTime);
        tempParkingOrder.setPayStatus(PayStatus.PAY_SUCCESS);
        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection(Lists.newArrayList(tempParkingOrder));
        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
                .thenReturn(tempParkingOrderCollection);

        // 执行测试
        boolean result = compositeHandler.handle(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
         // 捕获事件参数
        ArgumentCaptor<ParkingCompensateExitSelectedEvent> eventCaptor = ArgumentCaptor.forClass(ParkingCompensateExitSelectedEvent.class);
        verify(eventPublisher, times(1)).publishEvent(eventCaptor.capture());
        ParkingCompensateExitSelectedEvent event = eventCaptor.getValue();
        ParkingCompensateExitInfo exitInfo = event.getPayload();
        assertNotNull(exitInfo);
        assertEquals(sameTime, exitInfo.getExitTime());
        assertEquals(ParkingTriggerWay.SYSTEM_COMPENSATORY_REGISTER_BY_SNAP, exitInfo.getTriggerType());
    }

    @Test
    public void shouldSelectCaptureExitInfo_whenPaidNotSupported() {
        // 准备测试数据 - 模拟抓拍记录
        ParkingPlaceCameraCaptureRecord captureRecord = new ParkingPlaceCameraCaptureRecord();
        captureRecord.setCaptureTime(new Date(FIXED_TIME - 10 * 60 * 1000L));
        captureRecord.setVehicle(parkingOrderAgg.getEnterInfo().getVehicle());
        captureRecord.setImageUrl("https://example.com/capture.jpg");
        captureRecord.setDeviceType(DeviceType.VIDEO_PILE);

        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
            eq(parkingOrderAgg.getPlaceId()),
            eq(parkingOrderAgg.getEnterInfo().getDatetime()),
            any(Date.class),
            eq(parkingOrderAgg.getEnterInfo().getVehicle().getPlate()))
        ).thenReturn(Optional.of(captureRecord));

        // 模拟支付记录不存在
        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection();
        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
                .thenReturn(tempParkingOrderCollection);

        // 执行测试
        boolean result = compositeHandler.handle(parkingOrderAgg);

        // 验证结果
        assertTrue(result);
        // 捕获事件参数
        ArgumentCaptor<ParkingCompensateExitSelectedEvent> eventCaptor = ArgumentCaptor.forClass(ParkingCompensateExitSelectedEvent.class);
        verify(eventPublisher, times(1)).publishEvent(eventCaptor.capture());
        ParkingCompensateExitSelectedEvent event = eventCaptor.getValue();
        ParkingCompensateExitInfo exitInfo = event.getPayload();
        assertNotNull(exitInfo);
        assertEquals(captureRecord.getCaptureTime(), exitInfo.getExitTime());
        assertEquals(ParkingTriggerWay.SYSTEM_COMPENSATORY_REGISTER_BY_SNAP, exitInfo.getTriggerType());
    }

    @Test
    public void shouldReturnFalse_whenNoProviderSupported() {
        // 模拟抓拍记录不存在
        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
                anyLong(), any(Date.class), any(Date.class), anyString()))
                .thenReturn(Optional.empty());

        // 模拟支付记录不存在
        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection();
        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
                .thenReturn(tempParkingOrderCollection);

        // 执行测试
        boolean result = compositeHandler.handle(parkingOrderAgg);

        // 验证结果
        assertFalse(result);
    }

    //    @Test
//    public void testAutoCaptureHandlerDirectly() {
//        // 准备测试数据 - 模拟抓拍记录
//        Date captureTime = new Date(System.currentTimeMillis() - 600000); // 10分钟前
//        ParkingPlaceCameraCaptureRecord captureRecord = new ParkingPlaceCameraCaptureRecord();
//        captureRecord.setCaptureTime(captureTime);
//        captureRecord.setVehicle(parkingOrderAgg.getEnterInfo().getVehicle());
//        captureRecord.setImageUrl("https://example.com/capture.jpg");
//        captureRecord.setDeviceType(DeviceType.VIDEO_PILE);
//
//        when(parkingPlaceGateway.getLastCaptureWithDateRangeClosed(
//                anyLong(), any(Date.class), any(Date.class), anyString()))
//                .thenReturn(Optional.of(captureRecord));
//
//        // 执行测试
//        boolean result = autoCaptureHandler.handle(parkingOrderAgg);
//
//        // 验证结果
//        assertTrue(result);
//    }

//    @Test
//    public void testPaidExitHandlerDirectly() {
//        // 准备测试数据 - 模拟支付记录
//        TempParkingOrderCollection tempParkingOrderCollection = new TempParkingOrderCollection();
//        Date payTime = new Date(System.currentTimeMillis() - 300000); // 5分钟前
//        when(tempParkingOrderRepository.getCollectionByParkingOrderId(anyLong()))
//                .thenReturn(tempParkingOrderCollection);
//        when(tempParkingOrderCollection.getLastPaidOrder()).thenReturn(Optional.of(tempParkingOrder));
//        when(tempParkingOrder.getPayTime()).thenReturn(payTime);
//        when(tempParkingOrder.isPaySuccess()).thenReturn(true);
//        when(parkingOrderAgg.hasBeenPaid()).thenReturn(true);
//
//        // 执行测试
//        boolean result = paidExitHandler.handle(parkingOrderAgg);
//
//        // 验证结果
//        assertTrue(result);
//    }
}
