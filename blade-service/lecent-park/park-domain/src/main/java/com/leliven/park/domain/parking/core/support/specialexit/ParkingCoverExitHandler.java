package com.leliven.park.domain.parking.core.support.specialexit;

import com.leliven.park.domain.order.parking.ParkingOrderRepositoryI;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import com.leliven.park.domain.order.parking.event.ParkingOrderEventType;
import com.leliven.park.domain.parking.core.interfaces.ParkingCombinedSupport;
import com.leliven.park.domain.parking.core.support.publisher.ParkingOrderAbnormalPublisherI;
import com.leliven.park.domain.parking.core.support.publisher.ParkingSuccessfulPassagePublisherI;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 覆盖出场处理
 *
 * <AUTHOR>
 */
@Order
@Component
@RequiredArgsConstructor
public class ParkingCoverExitHandler extends AbstractParkingCompensateExitHandler
	implements ParkingCompensateExitHandler, ParkingCombinedSupport<ParkingOrderAgg> {

	private final ParkingOrderRepositoryI parkingOrderRepository;
	private final ParkingOrderAbnormalPublisherI parkingOrderAbnormalPublisher;
	private final ParkingSuccessfulPassagePublisherI parkingSuccessfulPassagePublisher;

	@Override
	public boolean supports(ParkingOrderAgg parkingOrderAgg) {
		return ParkingCombinedSupport.super.supports(parkingOrderAgg)
			&& Objects.nonNull(parkingOrderAgg.getTargetParkingOrder());
	}

	@Override
	public boolean doHandle(ParkingOrderAgg parkingOrder) {
		return exit(parkingOrder);
	}

	/**
	 * 覆盖出场
	 *
	 * @param presentOrder 在场订单聚合列表
	 */
	private boolean exit(ParkingOrderAgg presentOrder) {
		// 通过当前入场订单进行覆盖出场
		presentOrder.coverExit(presentOrder.getTargetParkingOrder());
		parkingOrderRepository.update(presentOrder);
		parkingSuccessfulPassagePublisher.publishExitMessage(presentOrder);
		// 发布覆盖出场事件
		SpringDomainEventPublisher.publish(
			new ParkingOrderDomainEvent(this, ParkingOrderEventType.COVERED, presentOrder));
		// 发送异常订单消息
		parkingOrderAbnormalPublisher.publish(presentOrder);
		return true;
	}
}
