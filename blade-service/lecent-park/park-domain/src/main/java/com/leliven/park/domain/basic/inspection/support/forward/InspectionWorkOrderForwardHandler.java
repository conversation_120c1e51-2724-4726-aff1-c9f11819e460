package com.leliven.park.domain.basic.inspection.support.forward;

import com.leliven.park.domain.basic.inspection.model.InspectionForwardContext;
import com.leliven.park.domain.basic.inspection.model.InspectionRecord;
import com.leliven.park.domain.basic.inspection.model.valueobject.*;
import com.leliven.park.domain.basic.parklot.ParklotCacheRepositoryI;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.common.workorder.LocalWorkOrderGateway;
import com.leliven.park.domain.common.workorder.model.LocalWorkOrder;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单转发处理器
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class InspectionWorkOrderForwardHandler implements InspectionForwardHandler {

    /**
     * 巡检转工单来源
     */
    public static final LocalWorkOrder.Source SOURCE = new LocalWorkOrder.Source(4, 1);

    private final ParkingPlaceGateway parkingPlaceGateway;
    private final LocalWorkOrderGateway workOrderGateway;
    private final ParklotCacheRepositoryI parklotCacheRepository;


    @Override
    public void handle(List<InspectionForwardContext> forwardContexts) {
        List<LocalWorkOrder> workOrders = createWorkOrders(forwardContexts);

        if (!workOrders.isEmpty()) {
            // 创建工单
            workOrderGateway.addBatch(workOrders);
        }
    }

    @Override
    public InspectionPhenomenonForwardRule.TargetType getTargetType() {
        return InspectionPhenomenonForwardRule.TargetType.WORK_ORDER;
    }


    /**
     * 将巡检记录转换为工单
     *
     * @param forwardContexts 巡检转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     * @return 工单列表 {@link LocalWorkOrder}
     */
    private List<LocalWorkOrder> createWorkOrders(List<InspectionForwardContext> forwardContexts) {
        // 筛选需要创建工单的记录
        List<InspectionForwardContext> need2WorkOrderForwardContexts = forwardContexts.stream()
            .filter(ctx -> supports(ctx.getTargetType()) && ctx.isNeedForward())
            .collect(Collectors.toList());

         List<LocalWorkOrder> workOrders = new ArrayList<>();

        if (need2WorkOrderForwardContexts.isEmpty()) {
            return workOrders;
        }

        Map<Long, ParklotDomain> parklots = new HashMap<>();
        Map<InspectionObject, ParkingSpace> parkingSpaces = new HashMap<>();

        for (InspectionForwardContext context : need2WorkOrderForwardContexts) {
            // 创建工单工厂方法
            createWorkOrder(
                context,
                parklots.computeIfAbsent(context.getParklotId(), parklotCacheRepository::getById),
                parkingSpaces.computeIfAbsent(context.getInspectionObject(), this::getParkingSpace)
            ).ifPresent(workOrders::add);
        }

        return workOrders;
    }

    /**
	 * 创建单个工单对象的工厂方法
	 *
	 * @param parklot              停车场 {@link ParklotDomain}
	 * @param space                泊位 {@link ParkingSpace}
	 * @return 创建的工单对象 {@link LocalWorkOrder}
	 */
    private Optional<LocalWorkOrder> createWorkOrder(InspectionForwardContext context,
                                                     ParklotDomain parklot,
                                                     ParkingSpace space) {

        InspectionRecord inspectionRecord = context.getInspectionRecord();
        InspectionPhenomenonForwardRule forwardRule = context.getForwardRule();
        if (inspectionRecord.getAbnormalInfo() == null || inspectionRecord.getAbnormalInfo().getWorkOrderId() == null) {
			log.warn("记录(任务项ID:{})需要转工单，但异常信息或工单ID为空", inspectionRecord.getTaskItemId());
			return Optional.empty();
		}

		LocalWorkOrder.Builder builder = LocalWorkOrder.builder();

        if (Objects.nonNull(space)) {
            builder.placeId(space.getId())
                .placeCode(space.getPlaceCode());
        }

		builder.id(inspectionRecord.getAbnormalInfo().getWorkOrderId())
            .bizCode(null)
            .source(SOURCE)
            .tenantId(inspectionRecord.getTenantId())
            .parkLotId(parklot.getId())
            .parkLotName(parklot.getName())
            .configId(Func.toLong(forwardRule.getTargetId()))
			.extentParams(createExtParams(inspectionRecord))
            .attachments(convert2WorkOrderAttachments(inspectionRecord.getAbnormalInfo().getAbnormalImages()))
            .sponsor(new LocalWorkOrder.Sponsor(inspectionRecord.getInspectorId(), inspectionRecord.getInspectorName()));

        return Optional.ofNullable(builder.build());
    }

	/**
	 * 创建工单扩展参数
	 *
	 * @param inspectionRecord 巡检记录
	 * @return 工单扩展参数
	 */
	private static Map<String, String> createExtParams(InspectionRecord inspectionRecord) {
		Map<String, String> extParams = new HashMap<>();
        Optional.ofNullable(inspectionRecord.getAbnormalInfo())
            .ifPresent(abnormalInfo -> extParams.put("abnormalReason", abnormalInfo.getAbnormalReasonName()));
		return extParams;
	}


	/**
     * 处理工单附件
     *
     * @param abnormalImages 异常图片附件
     * @return 工单附件 {@link LocalWorkOrder.Attachments}
     */
    private LocalWorkOrder.Attachments convert2WorkOrderAttachments(List<String> abnormalImages) {

        if (Func.isEmpty(abnormalImages)) {
            return LocalWorkOrder.Attachments.EMPTY;
        }

        List<LocalWorkOrder.Attachment> imageAttachments = abnormalImages.stream()
            .filter(Func::isNotBlank)
            .map(url -> LocalWorkOrder.Attachment.createStandard("巡检异常图片附件", url))
            .collect(Collectors.toList());

        if (Func.isNotEmpty(imageAttachments)) {
            Map<String, List<LocalWorkOrder.Attachment>> attachmentMap = new HashMap<>();
            attachmentMap.put("standard", imageAttachments);
            return new LocalWorkOrder.Attachments(attachmentMap);
        }

        return LocalWorkOrder.Attachments.EMPTY;
    }

    /**
     * 根据巡检对象解析车位ID
     * @param object 巡检对象
     * @return 车位ID
     */
    public Long resolveParkingPlaceId(InspectionObject object) {
        if (object.isParkingPlaceType()) {
            return object.getId();
        }

        return null;
    }

    /**
     * 获取车位信息
     *
     * @param object 巡检对象 {@link InspectionObject}
     * @return 车位信息 {@link ParkingSpace}
     */
    private ParkingSpace getParkingSpace(InspectionObject object) {
        if (object.isParkingPlaceType()) {
            return parkingPlaceGateway.asCacheSupplier().getOptional(object.getId())
                .orElseThrow(() -> new ServiceException("未查询到车位信息，id:"+ object.getId()));
        }

        return null;
    }


}
