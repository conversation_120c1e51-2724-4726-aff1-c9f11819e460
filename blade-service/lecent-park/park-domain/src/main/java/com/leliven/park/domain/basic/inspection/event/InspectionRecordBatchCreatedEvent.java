package com.leliven.park.domain.basic.inspection.event;


import com.leliven.park.domain.basic.inspection.model.InspectionRecord;
import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;

import javax.annotation.Nonnull;
import java.util.List;


/**
 * 巡检记录批量创建事件
 *
 * <AUTHOR>
 */
public class InspectionRecordBatchCreatedEvent extends AbstractPayloadDomainEvent<List<InspectionRecord>> {


    /**
     * Create a new DomainEvent.
     *
     * @param payload the payload object (never {@code null})
     */
    protected InspectionRecordBatchCreatedEvent(@Nonnull List<InspectionRecord> payload) {
        super(payload);
    }

    /**
     * Create a new DomainEvent.
     *
     * @param payload the payload object (never {@code null})
     */
    public static InspectionRecordBatchCreatedEvent of(@Nonnull List<InspectionRecord> payload) {
        return new InspectionRecordBatchCreatedEvent(payload);
    }
}
