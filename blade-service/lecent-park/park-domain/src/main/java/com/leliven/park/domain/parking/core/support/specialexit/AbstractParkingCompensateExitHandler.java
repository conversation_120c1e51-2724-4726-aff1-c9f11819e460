package com.leliven.park.domain.parking.core.support.specialexit;

import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.parking.core.event.ParkingCompensateExitSelectedEvent;
import com.leliven.park.domain.parking.core.interfaces.ParkingSupport;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingCompensateExitInfo;
import com.leliven.ddd.core.event.DomainEventPublisher;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;

import javax.annotation.Resource;

/**
 * 补偿出场处理器抽象类
 *
 * <AUTHOR>
 */
public abstract class AbstractParkingCompensateExitHandler implements ParkingSupport<ParkingOrderAgg>, ParkingCompensateExitHandler {

	private DomainEventPublisher domainEventPublisher;

	/**
	 * 设置领域事件发布者(自动注入)
	 *
	 * @param domainEventPublisher 领域事件发布者
	 */
	@Resource(type = SpringDomainEventPublisher.class)
	public void setDomainEventPublisher(DomainEventPublisher domainEventPublisher) {
		this.domainEventPublisher = domainEventPublisher;
	}

	@Override
	public boolean handle(ParkingOrderAgg parkingOrder) {
		if (supports(parkingOrder)) {
			return doHandle(parkingOrder);
		}
		return false;
	}

	protected abstract boolean doHandle(ParkingOrderAgg parkingOrder);

	/**
	 * 发布出场信息已选择事件
	 *
	 * @param exitInfo 出场信息
	 * @return 发布结果
	 */
	protected boolean publishExitInfoSelectedEvent(ParkingCompensateExitInfo exitInfo) {
		domainEventPublisher.publishEvent(ParkingCompensateExitSelectedEvent.create(exitInfo));
		return true;
	}
}
