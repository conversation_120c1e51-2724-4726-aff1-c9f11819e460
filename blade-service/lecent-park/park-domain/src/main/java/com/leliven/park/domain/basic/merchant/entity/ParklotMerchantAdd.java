package com.leliven.park.domain.basic.merchant.entity;

import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;

/**
 * 车场商户新增聚合
 *
 * <AUTHOR>
 */
@Data
public class ParklotMerchantAdd extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 商户名称
	 */
	private String name;
	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态：0无效、1有效
	 */
	private Integer status;
}
