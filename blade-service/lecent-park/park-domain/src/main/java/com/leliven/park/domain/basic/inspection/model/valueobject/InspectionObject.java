package com.leliven.park.domain.basic.inspection.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.ObjectValidator;

import java.util.Objects;

/**
 * 巡检对象
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionObject {

    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     *
     * <ul>
     * <li>车场名称</li>
     * <li>通道名称</li>
     * <li>车位支付码</li>
     * </ul>
     */
    private String name;

    /**
     * 类型
     */
    private InspectionObjectType type;

    /**
     * 私有构造函数，用于内部使用
     *
     * @param id    ID {@link Long}
     * @param name  名称 {@link String}
     * @param type  类型 {@link InspectionObjectType}
     */
    private InspectionObject(Long id, String name, InspectionObjectType type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }

    /**
     * 创建巡检对象
     *
     * @param id    ID {@link Long}
     * @param name  名称 {@link String}
     * @param type  类型 {@link InspectionObjectType}
     * @return 巡检对象 {@link InspectionObject}
     */
    public static InspectionObject of(Long id, String name, InspectionObjectType type) {
        ObjectValidator.requireNonNull(id, "巡检对象id不能为空");
        ObjectValidator.requireNonNull(type, "巡检对象类型不能为空");
        return new InspectionObject(id, name, type);
    }

    /**
     * 创建车场类型的巡检对象
     *
     * @param id    ID {@link Long}
     * @param name  名称 {@link String}
     * @return 巡检对象 {@link InspectionObject}
     */
    public static InspectionObject ofParklot(Long id, String name) {
        return new InspectionObject(id, name, InspectionObjectType.PARKLOT);
    }

    /**
     * 创建通道类型的巡检对象
     *
     * @param id    ID {@link Long}
     * @param name  名称 {@link String}
     * @return 巡检对象 {@link InspectionObject}
     */
    public static InspectionObject ofParkingChannel(Long id, String name) {
        return new InspectionObject(id, name, InspectionObjectType.PARKING_CHANNEL);
    }

    /**
     * 创建车位类型的巡检对象
     *
     * @param id    ID {@link Long}
     * @param name  名称 {@link String}
     * @return 巡检对象 {@link InspectionObject}
     */
    public static InspectionObject ofParkingPlace(Long id, String name) {
        return new InspectionObject(id, name, InspectionObjectType.PARKING_PLACE);
    }

    /**
     * 是否为车位类型
     *
     * @return 是否为车位类型 {@code true} 是 {@code false} 否
     */
    public boolean isParkingPlaceType() {
		return InspectionObjectType.PARKING_PLACE.equals(type);
	}

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof InspectionObject)) {
            return false;
        }
        InspectionObject that = (InspectionObject) o;
        return Objects.equals(id, that.id) && type == that.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, type);
    }
}
