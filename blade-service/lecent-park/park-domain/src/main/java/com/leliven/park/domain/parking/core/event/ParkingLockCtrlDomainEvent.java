package com.leliven.park.domain.parking.core.event;

import com.leliven.park.domain.parking.core.model.objectvalue.ParkingLockCtrlExecuteResult;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingLockCtrlType;
import com.leliven.ddd.core.event.AbstractDomainEvent;

import javax.annotation.Nonnull;

/**
 * 路边车位锁控制领域事件
 *
 * <AUTHOR>
 */
public class ParkingLockCtrlDomainEvent extends AbstractDomainEvent<ParkingLockCtrlExecuteResult, ParkingLockCtrlType> {


	/**
	 * Create a new DomainEvent.
	 *
	 * @param source    the object on which the event initially occurred (never {@code null})
	 * @param eventType domain event type
	 * @param payload   the payload object (never {@code null})
	 */
	protected ParkingLockCtrlDomainEvent(Object source, @Nonnull ParkingLockCtrlExecuteResult payload) {
		super(source, payload.getCtrlType(), payload);
	}

	public static ParkingLockCtrlDomainEvent create(Object source, @Nonnull ParkingLockCtrlExecuteResult payload) {
		return new ParkingLockCtrlDomainEvent(source, payload);
	}

}
