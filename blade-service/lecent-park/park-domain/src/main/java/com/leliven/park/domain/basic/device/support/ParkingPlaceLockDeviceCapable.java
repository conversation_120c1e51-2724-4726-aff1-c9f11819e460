package com.leliven.park.domain.basic.device.support;

import com.leliven.park.domain.basic.device.gateway.DeviceGatewayI;
import com.leliven.park.domain.basic.place.model.valueobject.BasicLockCtrlParam;
import com.leliven.park.domain.basic.place.model.valueobject.LockUpCtrlParam;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.park.common.model.valueobject.BasicParkingScene;
import com.leliven.park.domain.basic.device.model.ParkingDeviceCommand;
import com.leliven.park.domain.basic.device.model.valueobject.DeviceSn;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;

import javax.annotation.Nonnull;
import java.util.function.Consumer;

/**
 * 停车位锁设备能力
 *
 * <AUTHOR>
 * @since 2024/9/9
 */
@Slf4j
@DomainService
public class ParkingPlaceLockDeviceCapable {

	private final DeviceGatewayI deviceGateway;

	public ParkingPlaceLockDeviceCapable(DeviceGatewayI deviceGateway) {
        this.deviceGateway = deviceGateway;
	}

	/**
	 * 升锁
	 *
	 * @param parkingDevice   设备
	 * @param commandConsumer 命令消费者
	 * @return 下发锁命令结果
	 */
	public R<String> lock(@Nonnull ParkingDevice parkingDevice,
						  @Nonnull Consumer<ParkingDeviceCommand<LockUpCtrlParam>> commandConsumer) {
		ParkingDeviceCommand<LockUpCtrlParam> command = new ParkingDeviceCommand<>(DeviceSn.of(parkingDevice.getSn()));
		commandConsumer.andThen(this::initCmdParams).accept(command);
		command.initParamIfNecessary(LockUpCtrlParam::new);
		return deviceGateway.executeLockCommand(command);
	}

	/**
	 * 降锁
	 *
	 * @param parkingDevice   设备
	 * @param commandConsumer 命令消费者
	 * @return 下发锁命令结果
	 */
	public R<String> unlock(@Nonnull ParkingDevice parkingDevice,
							@Nonnull Consumer<ParkingDeviceCommand<BasicLockCtrlParam>> commandConsumer) {
		ParkingDeviceCommand<BasicLockCtrlParam> command = new ParkingDeviceCommand<>(DeviceSn.of(parkingDevice.getSn()));
		commandConsumer.andThen(this::initCmdParams).accept(command);
		command.initParamIfNecessary(BasicLockCtrlParam::new);
		return deviceGateway.executeUnlockCommand(command);
	}

	/**
     * 初始化命令
     *
     * @param command 命令
     */
    private void initCmdParams(ParkingDeviceCommand<? extends BasicLockCtrlParam> command) {
        command.initRequestIdIfNecessary().initSourceIfNecessary(() -> BasicParkingScene.UNKNOWN);
    }

}
