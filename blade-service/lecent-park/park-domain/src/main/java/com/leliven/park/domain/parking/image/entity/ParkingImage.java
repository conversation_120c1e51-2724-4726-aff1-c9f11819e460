package com.leliven.park.domain.parking.image.entity;

import com.leliven.ddd.core.model.BaseDomain;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.Func;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 停车图片领域对象
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ParkingImage extends BaseDomain {

	private static final long serialVersionUID = 554493805532666695L;

	@Getter
	private ParkingImageType type;
	private List<String> urls;

	public ParkingImage(ParkingImageType type) {
		this(null, type, new ArrayList<>());
	}

	public ParkingImage(ParkingImageType type, List<String> imageUrls) {
		this(null, type, imageUrls);
	}

	public ParkingImage(Long id, ParkingImageType type, List<String> urls) {
		super(id);
		this.type = type;
		this.urls = Optional.ofNullable(urls)
			.map(list -> list.stream()
				.filter(Func::isNotBlank)
				.collect(Collectors.toList())
		).orElseGet(ArrayList::new);
	}

	public List<String> getUrls() {
		if (Objects.isNull(this.urls)) {
			this.urls = new ArrayList<>();
		}

		return urls;
	}

	public boolean hasUrls() {
		return size() > 0;
	}

	public int size() {
		if (Objects.isNull(this.urls)) {
			return 0;
		}

		return this.urls.size();
	}
}
