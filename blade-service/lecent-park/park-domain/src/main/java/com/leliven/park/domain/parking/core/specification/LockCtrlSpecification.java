package com.leliven.park.domain.parking.core.specification;

import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.ddd.core.specification.Specification;

/**
 * 锁控制规约
 *
 * <p>
 * 说明：<br>
 * 本接口用于定义车位锁控制相关的业务规约（Specification），如升锁、降锁等操作的前置业务判断。<br>
 * 规约的入口参数通常为“车位”对象 {@link ParkingSpace}，因为升降锁的物理操作对象是车位。<br>
 * 但实际业务判断往往依赖于车位当前关联的订单（如订单状态、是否在免费时段等），
 * 因此实现类中通常会通过车位ID查找当前订单，再基于订单属性进行判断。<br>
 * 这样设计既保证了物理操作的直接性，也兼顾了业务判断的灵活性，便于后续扩展和维护。
 * </p>
 *
 * <p>
 * 实现类应根据具体业务场景，实现对车位锁操作的各种判断逻辑。
 * </p>
 *
 * <AUTHOR>
 */
public interface LockCtrlSpecification extends Specification<ParkingSpace> {

}
