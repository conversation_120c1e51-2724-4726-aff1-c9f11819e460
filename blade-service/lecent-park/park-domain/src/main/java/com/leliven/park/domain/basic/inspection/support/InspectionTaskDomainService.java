package com.leliven.park.domain.basic.inspection.support;

import com.leliven.park.domain.basic.inspection.InspectionSubTaskGateway;
import com.leliven.park.domain.basic.inspection.InspectionTaskGateway;
import com.leliven.park.domain.basic.inspection.event.InspectionTaskEvent;
import com.leliven.park.domain.basic.inspection.model.InspectionPlan;
import com.leliven.park.domain.basic.inspection.model.InspectionSubTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskGenerator;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionTaskStatus;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡检任务领域服务
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class InspectionTaskDomainService {

    private final InspectionTaskGateway taskGateway;
	private final InspectionSubTaskGateway subTaskGateway;
	private final InspectionTaskGeneratorFactory taskGeneratorFactory;
	private final InspectionSubTaskDomainService subTaskDomainService;


    /**
     * 根据巡检计划生成巡检任务
     */
	@Transactional(rollbackFor = Exception.class)
    public List<InspectionTask> generateTasks(InspectionPlan plan) {
        // 使用工厂方法创建生成器
        InspectionTaskGenerator generator = taskGeneratorFactory.createTaskGenerator(plan);
        // 生成任务
        InspectionTaskGenerator.TaskGenerationResult generate = generator.generate();
        List<InspectionTask> tasks = generate.getTasks();

		if (tasks.isEmpty()) {
			return tasks;
		}

		List<InspectionSubTask> subTasks = tasks.stream()
			.map(task -> taskGeneratorFactory.createSubTaskGenerator(task).generate())
			.flatMap(List::stream)
			.collect(Collectors.toList());

		// 保存任务和子任务
		taskGateway.batchSave(tasks);
		subTaskGateway.batchSave(subTasks);

        return tasks;
    }

	/**
	 * 更新未开始的任务为进行中
	 *
	 * @param now 当前时间
	 * @return 更新的任务数量
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<InspectionTask> updateTasksToInProgress(LocalDateTime now) {
		List<InspectionTask> tasks = taskGateway.listTasksByStatusAndStartTimeBefore(
			InspectionTaskStatus.NOT_STARTED, now);

		if (tasks.isEmpty()) {
			return tasks;
		}

		List<InspectionTask> inProgressTasks = tasks.stream()
			.filter(InspectionTask::start)
			.collect(Collectors.toList());

		taskGateway.batchUpdate(inProgressTasks);

		// 发布任务开始事件
		inProgressTasks.forEach(inProgressTask -> SpringDomainEventPublisher.publish(
			InspectionTaskEvent.of(this, InspectionTaskEvent.EventType.STARTED, inProgressTask)));

		return inProgressTasks;
	}

	/**
	 * 更新进行中的超时任务状态
	 *
	 * @param now 当前时间
	 * @return 更新的任务数量
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<InspectionTask> updateTasksToTimeout(LocalDateTime now) {
		List<InspectionTask> tasks = taskGateway.listTasksByStatusAndEndTimeBefore(
			InspectionTaskStatus.IN_PROGRESS, now);

		if (tasks.isEmpty()) {
			return tasks;
		}

		List<InspectionTask> timeoutTasks = tasks.stream().filter(InspectionTask::changeStatusToTimeout)
			.collect(Collectors.toList());

		taskGateway.batchUpdate(timeoutTasks);

		timeoutTasks.forEach(timeoutTask -> SpringDomainEventPublisher.publish(
			InspectionTaskEvent.of(this, InspectionTaskEvent.EventType.TIMEOUT, timeoutTask)));

		return timeoutTasks;
	}

	/**
	 * 取消任务
	 * <p>
	 * 将任务状态变更为已取消，同时将对应的子任务状态也变更为已取消
	 * </p>
	 *
	 * @param taskId 任务ID
	 * @return 是否取消成功 {@code true} 取消成功, {@code false} 取消失败
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean cancelTask(Long taskId) {
		InspectionTask task = taskGateway.getOrElseThrow(taskId);

		// 取消任务
		boolean success = task.cancel();
		if (!success) {
			return false;
		}

		// 保存任务状态变更
		taskGateway.update(task);

		// 取消对应的子任务
		subTaskDomainService.cancelSubTasksByTaskId(taskId);

		// 发布任务取消事件
		SpringDomainEventPublisher.publish(
			InspectionTaskEvent.of(this, InspectionTaskEvent.EventType.CANCELLED, task));

		return true;
	}

}
