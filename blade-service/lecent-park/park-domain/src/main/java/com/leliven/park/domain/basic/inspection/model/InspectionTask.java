package com.leliven.park.domain.basic.inspection.model;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionTaskStatus;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionTaskTimeWindow;
import com.leliven.park.domain.basic.inspection.model.valueobject.Inspector;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.core.conditional.ConditionalExecutable;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.log.exception.ServiceException;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 巡检任务
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
public class InspectionTask extends TenantDomain implements ConditionalExecutable<InspectionTask> {

    /**
     * 任务完成率100%
     */
    private static final BigDecimal FULL_COMPLETION_RATE = new BigDecimal("100.00");

	/**
	 * 任务名称
	 */
	private String name;

    /**
     * 计划ID
     */
    private final Long planId;

    /**
     * 车场ID
     */
    private final Long parklotId;

    /**
     * 任务时间窗口
     */
    private InspectionTaskTimeWindow timeWindow;

    /**
     * 巡检项ID列表
     */
    private List<Long> itemIds;

    /**
     * 任务状态
     */
    private InspectionTaskStatus taskStatus;

    /**
     * 任务完成率
     */
    private BigDecimal completionRate;

    /**
     * 最近任务完成时间
     */
    private LocalDateTime latestCompletedTime;

    /**
     * 任务完成实际耗时
     */
    private Duration actualCompletionDuration;

    /**
     * 执行人列表
     */
    private final List<InspectionTaskExecutor> executors;

    /**
     * 构造函数
     *
     * @param id 任务ID {@link Long}
     * @param planId 计划ID {@link Long}
     * @param parklotId 车场ID {@link Long}
     */
    public InspectionTask(Long id, Long planId, Long parklotId) {
        super(id);
        this.planId = planId;
        this.parklotId = parklotId;
        this.executors = new ArrayList<>();
    }

    /**
     * 创建巡检任务
     *
     * @param planId 计划ID
     * @param parklotId 车场ID
     * @param timeWindow 时间窗口 {@link InspectionTaskTimeWindow}
     * @return 巡检任务 {@link InspectionTask}
     */
    public static InspectionTask create(Long planId, Long parklotId, InspectionTaskTimeWindow timeWindow) {
        return new InspectionTask(IdWorker.getId(), planId, parklotId)
            .timeWindow(timeWindow)
            .completionRate(BigDecimal.ZERO)
            .taskStatus(InspectionTaskStatus.NOT_STARTED);
    }

    /**
    * 更新任务进度（基于完成率自动判断并更新任务状态）
    *
    * @param latestCompletedTime 最近任务完成时间
    * @param completionRate 完成率
    */
    public void updateTaskProgress(LocalDateTime latestCompletedTime, BigDecimal completionRate) {
        this.latestCompletedTime(latestCompletedTime)
            .completionRate(completionRate)
            .tryChangeStatusToCompleted();
    }

    /**
     * 启动任务
     * <p>
     * 将任务状态从未开始变更为进行中
     * </p>
     *
     * @return 是否启动成功 {@code true} 启动成功, {@code false} 启动失败
     */
    public boolean start() {
        if (this.taskStatus != InspectionTaskStatus.NOT_STARTED) {
            log.warn("任务[{}]当前状态为[{}]，不能启动", this.getId(), this.taskStatus.getDesc());
            return false;
        }

        // 变更任务状态为进行中
        this.taskStatus(InspectionTaskStatus.IN_PROGRESS);

        return true;
    }

    /**
     * 更新任务状态为超时
     *
     * @return 是否更新成功 {@code true} 更新成功, {@code false} 更新失败
     */
    public boolean changeStatusToTimeout() {
        if (this.taskStatus != InspectionTaskStatus.IN_PROGRESS) {
            return false;
        }
        this.taskStatus(InspectionTaskStatus.TIMEOUT);
        return true;
    }

    /**
     * 更新任务状态为已完成
     *
     * @return 是否更新成功 {@code true} 更新成功, {@code false} 更新失败
     */
	InspectionTask changeStatusToCompleted() {
        this.taskStatus(InspectionTaskStatus.COMPLETED);
        return this;
    }

    /**
     * 取消任务
     * <p>
     * 将任务状态变更为已取消，只有未完成的任务才能取消
     * </p>
     *
     * @return 是否取消成功 {@code true} 取消成功, {@code false} 取消失败
     */
    public boolean cancel() {
        if (this.taskStatus == InspectionTaskStatus.COMPLETED || this.taskStatus == InspectionTaskStatus.TIMEOUT) {
            log.warn("任务[{}]当前状态为[{}]，不能取消", this.getId(), this.taskStatus.getDesc());
            return false;
        }

        // 变更任务状态为已取消
        this.taskStatus(InspectionTaskStatus.CANCELLED);

        log.info("任务[{}]已成功取消", this.getId());
        return true;
    }

	/**
     * 设置任务名称
     *
     * @param name 任务名称
     * @return 巡检任务 {@link InspectionTask}
     */
	public InspectionTask name(String name) {
		this.name = name;
		return this;
	}

    /**
     * 设置租户
     *
     * @param tenantId 租户ID
     * @return InspectionTask
     */
    public InspectionTask belongToTenant(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    /**
     * 设置巡检项ID列表
     *
     * @param itemIds 巡检项ID列表
     * @return 巡检任务
     */
    public InspectionTask itemIds(List<Long> itemIds) {
        this.itemIds = itemIds;
        return this;
    }

    /**
     * 设置任务时间窗口
     *
     * @param timeWindow 时间窗口 {@link InspectionTaskTimeWindow}
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask timeWindow(InspectionTaskTimeWindow timeWindow) {
        this.timeWindow = timeWindow;
        return this;
    }

    /**
     * 设置任务状态
     *
     * @param taskStatus 任务状态 {@link InspectionTaskStatus}
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask taskStatus(InspectionTaskStatus taskStatus) {
        InspectionTaskStatus oldStatus = this.taskStatus;
        this.taskStatus = taskStatus;
        super.status = taskStatus.getValue();

        // 处理状态变更监听
        if (oldStatus != taskStatus) {
            handleStatusChange(oldStatus, taskStatus);
        }

        return this;
    }

    /**
     * 处理状态变更监听
     * 根据不同的状态变更执行相应的逻辑
     *
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    private void handleStatusChange(InspectionTaskStatus oldStatus, InspectionTaskStatus newStatus) {
        // 任务结束时计算实际耗时
        if (isTaskEnded(newStatus)) {
            calculateActualCompletionDuration();
        }

    }

    /**
     * 判断任务状态是否为结束状态
     *
     * @return 是否为结束状态
     */
    private boolean isTaskEnded() {
        return taskStatus != null && taskStatus.isEnd();
    }

    /**
     * 判断任务状态是否为结束状态
     *
     * @param status 任务状态
     * @return 是否为结束状态
     */
    private boolean isTaskEnded(InspectionTaskStatus status) {
        return status != null && status.isEnd();
    }

    /**
     * 设置最近任务完成时间
     *
     * @param latestCompletedTime 最近任务完成时间
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask latestCompletedTime(LocalDateTime latestCompletedTime) {
        if (Objects.isNull(this.latestCompletedTime) || this.latestCompletedTime.isBefore(latestCompletedTime)) {
            this.latestCompletedTime = latestCompletedTime;
        }
        return this;
    }

    /**
     * 设置任务完成率
     *
     * @param completionRate 任务完成率
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask completionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
        return this;
    }

    /**
     * 设置任务完成实际耗时
     *
     * @param actualCompletionDuration 任务完成实际耗时
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask actualCompletionDuration(Duration actualCompletionDuration) {
        this.actualCompletionDuration = actualCompletionDuration;
        return this;
    }

    /**
     * 添加执行人
     *
     * @param executor 执行人 {@link InspectionTaskExecutor}
     * @return 巡检任务
     */
    public InspectionTask addExecutor(InspectionTaskExecutor executor) {
        ObjectValidator.requireNonNull(executor, "executor must not be null");
        this.executors.add(executor.setTaskId(this.getId()));
        return this;
    }

    /**
     * 添加执行人列表
     *
     * @param executors 执行人列表
     * @return 巡检任务
     */
    public InspectionTask addExecutors(List<InspectionTaskExecutor> executors) {
        executors.forEach(this::addExecutor);
        return this;
    }

    /**
     * 变更执行人
     * <p>
     * 将任务的所有执行人替换为新的执行人列表
     * </p>
     *
     * @param newExecutors 新的执行人列表
     * @return 巡检任务
     */
    public InspectionTask changeExecutors(Supplier<List<InspectionTaskExecutor>> supplier) {
        ObjectValidator.requireNonNull(supplier, "执行人列表供应商接口不能为空");

        verifyShouldCanChangeExecutors();
        List<InspectionTaskExecutor> newExecutors = supplier.get();
        ObjectValidator.requireNotEmpty(newExecutors, "新的执行人列表不能为空");
        // 清空现有执行人
        this.executors.clear();
        // 添加新的执行人
        this.addExecutors(newExecutors);

        return this;
    }

    /**
     * 判断任务是否已完成
     *
     * @return 是否已完成 {@code true} 已完成, {@code false} 未完成
     */
    public boolean isCompleted() {
        return Objects.equals(InspectionTaskStatus.COMPLETED.getValue(), status);
    }

	/**
	 * 判断任务是否未完成
	 *
	 * @return 是否未完成 {@code true} 未完成, {@code false} 已完成
	 */
	public boolean isUncompleted() {
		return !isCompleted();
	}

    /**
     * 根据传入的时间判断任务是否超时
     *
     * @param dateTime 时间
     * @return 是否超时 {@code true} 超时, {@code false} 未超时
     */
    public boolean isTimeout(LocalDateTime dateTime) {
        return this.timeWindow.isAfterEndTime(dateTime);
    }

    /**
     * 验证当前巡检任务状态是否允许完成巡检子任务
     *
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask verifyStatusShouldAllowSubTaskCompletion() {
        if (this.taskStatus != InspectionTaskStatus.IN_PROGRESS) {
            throw new ServiceException("当前巡检任务状态为[" + this.taskStatus.getDesc() + "], 不能完成巡检子任务");
        }
        return this;
    }

    /**
     * 校验巡检人员权限，传入巡检人员ID，校验是否在执行人列表中
     *
     * @param inspector 巡检人员 {@link Inspector}
     * @return 巡检任务 {@link InspectionTask}
     */
    public InspectionTask verifyExecutorPermission(Inspector inspector) {
       if (this.executors.stream().noneMatch(e -> e.equalsExecutorId(inspector))) {
           throw new ServiceException("当前巡检人员[" + inspector.getInspectorName() + "]没有权限完成巡检任务");
       }
        return this;
    }

    /**
     * 校验变更执行人规则
     * <p>
     * 只有未完成的任务才能变更执行人
     * </p>
     */
    public void verifyShouldCanChangeExecutors() {
        if (isTaskEnded()) {
            throw new ServiceException("任务已结束，不能变更执行人");
        }
    }

    /**
     * 获取任务开始时间
     *
     * @return 任务开始时间 {@link LocalDateTime}
     */
    public LocalDateTime getStartTime() {
        return timeWindow != null ? timeWindow.getStartTime() : null;
    }

    /**
     * 获取任务结束时间
     *
     * @return 任务结束时间 {@link LocalDateTime}
     */
    public LocalDateTime getEndTime() {
        return timeWindow != null ? timeWindow.getEndTime() : null;
    }

    /**
     * 尝试将任务状态更改为已完成
     *
     * @return 巡检任务 {@link InspectionTask}
     */
    private InspectionTask tryChangeStatusToCompleted() {
        if (isFullyCompleted(completionRate) && !this.isCompleted()) {
            this.changeStatusToCompleted();
        }

        return this;
    }

    /**
     * 判断任务是否完全完成（完成率100%）
     *
     * @param completionRate 完成率
     * @return 是否完全完成
     */
    private boolean isFullyCompleted(BigDecimal completionRate) {
        return completionRate != null && completionRate.compareTo(FULL_COMPLETION_RATE) >= 0;
    }

    /**
     * 计算任务完成实际耗时
     * 只有在任务结束时才计算耗时
     */
    void calculateActualCompletionDuration() {
        if (this.timeWindow == null || this.timeWindow.getStartTime() == null) {
            return;
        }

        LocalDateTime startTime = this.timeWindow.getStartTime();
        LocalDateTime endTime = null;

        // 根据任务状态确定实际结束时间
        switch (this.taskStatus) {
            case COMPLETED:
                // 正常完成：使用最后完成时间
                endTime = this.latestCompletedTime != null ? this.latestCompletedTime : LocalDateTime.now();
                break;
            case TIMEOUT:
                // 超时：使用任务结束时间
                endTime = this.timeWindow.getEndTime();
                break;
            case CANCELLED:
                // 取消：使用当前时间
                endTime = LocalDateTime.now();
                break;
            default:
                // 其他状态不计算耗时
                return;
        }

        // 计算实际耗时
        Duration actualDuration = Duration.between(startTime, endTime);

        // 边界控制：实际完成时间不应超过任务周期
        Duration taskPeriod = this.timeWindow.getDuration();
        if (actualDuration.compareTo(taskPeriod) > 0) {
            actualDuration = taskPeriod;
        }

        this.actualCompletionDuration = actualDuration;
    }


	@Override
	public boolean equals(Object o) {
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		if (!super.equals(o)) {
			return false;
		}
		InspectionTask that = (InspectionTask) o;
		return Objects.equals(id, that.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id);
	}

}
