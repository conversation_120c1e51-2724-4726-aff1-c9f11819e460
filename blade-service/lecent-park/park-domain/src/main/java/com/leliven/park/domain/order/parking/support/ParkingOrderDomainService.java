package com.leliven.park.domain.order.parking.support;

import cn.hutool.core.math.Money;
import com.lecent.park.core.log.utils.AuditLogger;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.common.UnusualCost;
import com.leliven.park.domain.order.parking.ParkingOrderRepositoryI;
import com.leliven.park.domain.order.parking.entity.ParkingEventInfo;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.parking.entity.valueobject.EnterInfoChangeType;
import com.leliven.park.domain.order.parking.event.EnterInfoChangedDomainEvent;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import com.leliven.park.domain.order.parking.event.ParkingOrderEventType;
import com.leliven.park.domain.parking.image.ParkingImageRepositoryI;
import com.leliven.park.domain.parking.image.entity.ParkingImages;
import com.leliven.park.domain.parking.image.factory.ParkingImageFactory;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 停车订单领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ParkingOrderDomainService {

	private final ParkingOrderValidator parkingOrderValidator;
	private final ParkingOrderRepositoryI parkingOrderRepository;
	private final ParkingOrderRepeatEntryProcessor parkingOrderRepeatEntryProcessor;
	private final ParkingImageRepositoryI parkingImageRepository;

	/**
	 * 手动补录进场信息
	 *
	 * @param parkingOrderId   停车订单ID
	 * @param parkingEventInfo 进场信息
	 */
	public void manualCompensatoryRegister(Long parkingOrderId, ParkingEventInfo parkingEventInfo) {
		ParkingOrderAgg parkingOrderAgg = this.parkingOrderRepository.getById(parkingOrderId);
		this.parkingOrderValidator.verifyParkingOrderShouldNotNull(parkingOrderAgg);
		this.parkingOrderValidator.verifyParkingOrderShouldPresent(parkingOrderAgg);
		this.changeEnterInfo(EnterInfoChangeType.MANUAL_COMPENSATORY_REGISTER, parkingEventInfo, parkingOrderAgg);
	}

	/**
	 * 变更进场信息
	 *
	 * @param changeType      变更类型 {@link EnterInfoChangeType}
	 * @param latestEnterInfo 最新进场信息 {@link ParkingEventInfo}
	 * @param parkingOrderAgg 停车订单聚合 {@link ParkingOrderAgg}
	 */
	public void changeEnterInfo(EnterInfoChangeType changeType,
								ParkingEventInfo latestEnterInfo,
								ParkingOrderAgg parkingOrderAgg) {
		// 校验停车订单
		this.parkingOrderValidator.verifyParkingOrderShouldNotNull(parkingOrderAgg);
		// 旧进场信息
		ParkingEventInfo oldEnterInfo = parkingOrderAgg.getEnterInfo().copy();
		// 变更进场信息
		parkingOrderAgg.changeEnterInfo(latestEnterInfo);
		// 当最新的触发方式非车位锁触发时，删除旧的进场图片（兼容视频桩先推送进场消息，车位锁后推送消息的情况）
		if (!latestEnterInfo.isTriggerWayMatching(ParkingTriggerWay.LOCK)) {
			this.parkingImageRepository.deleteByParkingOrderId(parkingOrderAgg.getId(), ParkingStatusEnum.PARK_IN);
		}
		// 保存新的进场图片
		ParkingImages parkingImages = ParkingImageFactory.createEnterImages(
			parkingOrderAgg.getId(), parkingOrderAgg.getParklotId(), latestEnterInfo.getImageCollection());
		this.parkingImageRepository.saveOrUpdate(parkingImages);
		// 重复入场处理
		this.parkingOrderRepeatEntryProcessor.process(parkingOrderAgg);
		// 更新停车订单
		this.update(parkingOrderAgg, order ->
			new EnterInfoChangedDomainEvent(this, changeType, oldEnterInfo, order));

		log.info("[修改车牌]停车记录[{}]: {} -> {}", parkingOrderAgg.getId(), oldEnterInfo, latestEnterInfo);
		// 进场信息变更日志
		AuditLogger.add(String.valueOf(parkingOrderAgg.getId()), "parkingOrder", "变更车牌",
			Func.toJson(oldEnterInfo.getVehicle().getPlate()),
			Func.toJson(latestEnterInfo.getVehicle().getPlate()),
			Func.toJson(parkingOrderAgg.getEnterInfo()),
			null);
	}

	/**
	 * 清除停车订单
	 * @param parkingOrderAgg 停车订单聚合
	 */
	public void clean(ParkingOrderAgg parkingOrderAgg) {
		this.parkingOrderValidator.verifyParkingOrderShouldNotNull(parkingOrderAgg);
		// 清除停车订单
		boolean deleted = parkingOrderRepository.deletePhysical(parkingOrderAgg);
		if (deleted) {
			ParkingOrderDomainEvent domainEvent =
				new ParkingOrderDomainEvent(this, ParkingOrderEventType.CLEARED, parkingOrderAgg);
			SpringDomainEventPublisher.publish(domainEvent);
		}
	}

	/**
	 * 更新停车订单
	 *
	 * @param parkingOrderAgg 停车订单聚合
	 * @return 是否更新成功
	 */
	public boolean update(ParkingOrderAgg parkingOrderAgg) {
		return update(parkingOrderAgg, (ParkingOrderEventType) null);
	}

	/**
	 * 更新停车订单
	 *
	 * @param parkingOrderAgg 停车订单聚合
	 * @param eventType       事件类型
	 * @return 是否更新成功
	 */
	public boolean update(ParkingOrderAgg parkingOrderAgg, @Nullable ParkingOrderEventType eventType) {
		return this.update(parkingOrderAgg, order ->
			Optional.ofNullable(eventType)
				.map(et -> new ParkingOrderDomainEvent(this, et, order))
				.orElse(null)
		);
	}

	/**
	 * 更新停车订单
	 *
	 * @param parkingOrderAgg     停车订单聚合
	 * @param createEventFunction 创建事件函数
	 * @return 是否更新成功
	 */
	public boolean update(ParkingOrderAgg parkingOrderAgg,
						  @Nonnull Function<ParkingOrderAgg, ParkingOrderDomainEvent> createEventFunction) {
		ObjectValidator.requireNonNull(createEventFunction, "createEventFunction must not be null");
		// 更新停车订单
		boolean updated = this.parkingOrderRepository.update(parkingOrderAgg);
		// 发布领域事件
		ParkingOrderDomainEvent domainEvent = createEventFunction.apply(parkingOrderAgg);
		if (updated && Objects.nonNull(domainEvent)) {
			SpringDomainEventPublisher.publish(domainEvent);
		}

        return updated;
	}

	/**
	 * 将出场更改为入场
	 *
	 * @param parkingOrderId 停车订单id
	 */
	public void changeExitToEnter(Long parkingOrderId) {
		ParkingOrderAgg parkingOrderAgg = this.parkingOrderRepository.getById(parkingOrderId);
		parkingOrderAgg.setParkingStatus(ParkingStatusEnum.PARK_IN);
		// 校验停车订单
		this.parkingOrderValidator.verifyParkingOrderShouldNotNull(parkingOrderAgg);

		Money subtract = new Money(BigDecimal.ZERO.subtract(parkingOrderAgg.getParkingCost().getUnusualAmount().getAmount()));
		parkingOrderAgg.getParkingCost().addExpectedReceiveAmount(subtract);
		parkingOrderAgg.getParkingCost().addUnusualCost(new UnusualCost(new Money(0), 2));

		// 修改：先移除所有"重复进场，合并订单"节点，再添加新的
		parkingOrderAgg.getParkingSchedule()
				.removeByName("车辆出场")
				.removeByName("重复进场，合并订单")
				.resetDateByName(new Date(), "重复进场，合并订单");

		// 更新停车订单
		boolean result = this.parkingOrderRepository.changeExitToEnterUpdateParking(parkingOrderAgg);
		log.info("[合并停车记录]更新停车记录结果:{}", result);
		if (result) {
			// 更新车位状态
			SpringDomainEventPublisher.publish(
					new ParkingOrderDomainEvent(this, ParkingOrderEventType.MERGED, parkingOrderAgg));
			log.info("[合并停车记录]停车记录[{}]: {}", parkingOrderAgg.getId(), parkingOrderAgg.getEnterInfo());
			// 进场信息变更日志
			AuditLogger.add(String.valueOf(parkingOrderAgg.getId()), "parkingOrder", "合并停车记录",
					"", "", null,
					DateUtil.formatDateTime(new Date()) + " 将本次进场合并到上次出场记录中");
		}
	}
}
