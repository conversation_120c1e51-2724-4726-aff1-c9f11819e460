package com.leliven.park.domain.basic.inspection.model;

import com.leliven.ddd.core.model.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.enums.EnableStatus;
import org.springblade.core.log.exception.ServiceException;

import java.util.Objects;

/**
 * 巡检异常原因领域实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionAbnormalReason extends BaseDomain {

    /**
     * 异常原因编码
     */
    private String code;

    /**
     * 异常原因名称
     */
    private String name;

    /**
     * 异常原因描述
     */
    private String description;

    /**
     * 创建异常原因
     *
     * @param id 异常原因ID
     * @param code 异常原因编码
     * @param name 异常原因名称
     * @param description 异常原因描述
     * @return 异常原因实例
     */
    public static InspectionAbnormalReason of(Long id, String code, String name, String description) {
        InspectionAbnormalReason reason = new InspectionAbnormalReason();
        reason.id = id;
        reason.code = code;
        reason.name = name;
        reason.description = description;
        return reason;
    }

	/**
     * 创建异常原因
     *
     * @param code 异常原因编码
     * @param name 异常原因名称
     * @param description 异常原因描述
     * @return 异常原因实例
     */
    public static InspectionAbnormalReason create(String code, String name, String description) {
        InspectionAbnormalReason reason = new InspectionAbnormalReason();
        reason.code = code;
        reason.name = name;
        reason.description = description;
        return reason;
    }

    /**
     * 启用状态
     *
     * @return 启用异常原因 {@link InspectionAbnormalReason}
     */
    public InspectionAbnormalReason enable() {
        if (isEnabled()) {
            throw new ServiceException("异常原因已经是启用状态");
        }
        this.status = EnableStatus.ENABLE.getCode();
        return this;
    }

    /**
     * 禁用状态
     *
     * @return 禁用异常原因 {@link InspectionAbnormalReason}
     */
    public InspectionAbnormalReason disable() {
        if (!isEnabled()) {
            throw new ServiceException("异常原因已经是禁用状态");
        }
        this.status = EnableStatus.DISABLE.getCode();
        return this;
    }

    /**
     * 判断是否启用
     *
     * @return 是否启用
     */
    public boolean isEnabled() {
        return Objects.equals(EnableStatus.ENABLE.getCode(), this.status);
    }
}
