package com.leliven.park.domain.basic.inspection.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.ObjectValidator;

import java.util.Objects;

/**
 * 巡检现象转发规则
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionPhenomenonForwardRule {

	/**
	 * 转发目标类型
	 */
	private TargetType targetType;

	/**
	 * 转发目标ID
	 *
	 * <p>
	 * 若目标类型为工单，则目标ID为工单配置ID <br>
	 * 若目标类型为异常停车工单，则目标ID为异常停车类型
	 * </p>
	 */
	private String targetId;

	/**
	 * 是否强制转发
	 */
	private boolean forceForward;

	/**
	 * 构造
	 *
	 * @param targetType 转发目标类型
	 * @param targetId 转发目标ID
	 * @param forceForward 是否强制转发
	 */
	private InspectionPhenomenonForwardRule(TargetType targetType, String targetId, boolean forceForward) {
		this.targetType = targetType;
		this.targetId = targetId;
		this.forceForward = forceForward;
	}

	/**
	 * 创建
	 *
	 * @param targetTypeCode 转发目标类型编码
	 * @param targetId 转发目标ID
	 * @param forceForward 是否强制转发
	 * @return 转发规则
	 */
	public static InspectionPhenomenonForwardRule of(String targetTypeCode, String targetId, boolean forceForward) {
		TargetType targetType = TargetType.resolve(targetTypeCode);
		ObjectValidator.requireNonNull(targetType, "无效的转发目标类型编码：{}", targetTypeCode);

		return new InspectionPhenomenonForwardRule(targetType, targetId, forceForward);
	}

	/**
	 * 创建转发规则
	 *
	 * @param targetType 转发目标类型
	 * @param targetId 转发目标ID
	 * @param forceForward 是否强制转发
	 * @return 转发规则
	 */
	public static InspectionPhenomenonForwardRule of(TargetType targetType, String targetId, boolean forceForward) {
		ObjectValidator.requireNonNull(targetType, "转发目标类型不能为空");
		ObjectValidator.requireNonNull(targetId, "转发目标不能为空");
		return new InspectionPhenomenonForwardRule(targetType, targetId, forceForward);
	}

	/**
	 * 判断是否为工单目标类型
	 *
	 * @return 是否为工单目标类型 {@code true} 是 {@code false} 否
	 */
	public boolean isWorkOrderTargetType() {
		return equalsTargetType(TargetType.WORK_ORDER);
	}

	/**
	 * 根据传入的目标类型判断与当前对象是否相同
	 *
	 * @param targetType 目标类型
	 * @return 是否相同 {@code true} 相同 {@code false} 不相同
	 */
	public boolean equalsTargetType(TargetType targetType) {
		return Objects.equals(this.targetType, targetType);
	}

	@Getter
	@AllArgsConstructor
	public enum TargetType {

		WORK_ORDER,
		ABNORMAL_PARKING_ORDER,
		;

		public static TargetType resolve(String targetType) {
			for (TargetType value : values()) {
				if (value.name().equals(targetType)) {
					return value;
				}
			}
			return null;
		}
	}
}
