package com.leliven.park.domain.basic.place.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.ddd.core.support.LazyLoader;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springblade.common.enums.EnableStatus;

import java.util.Objects;

/**
 * 停车位领域对象
 *
 * <p>
 * 陆续替换掉原有的ParkingPlaceDomain
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParkingSpace extends TenantDomain {

	/**
	 * 车场ID
	 */
	protected Long parklotId;
	/**
	 * 区域ID
	 */
	protected Long regionId;

	/**
	 * 楼层id
	 */
	protected Long floorId;

	/**
	 * 车位类型(1.普通车 2.月卡车 3.产权车 4.VIP车位)
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	protected Integer placeType;
	/**
	 * 车位编号
	 */
	protected String placeCode;

	/**
	 * 扫码车位跳转短域名
	 */
	protected String shortUrl;

	/**
	 * 长链接地址
	 */
	protected String longUrl;

	/**
	 * 支付号
	 */
	protected String payCode;

	/**
	 * 备注
	 */
	protected String memo;

	/**
	 * 车位实时状况
	 */
	@Getter(AccessLevel.PRIVATE)
	@JsonIgnore
	protected LazyLoader<ParkingPlaceStatus> realTimeStatusLoader;

	/**
	 * 判断车位是否启用
	 *
	 * @return {@code true} 启用 {@code false} 禁用
	 */
	public boolean isEnabled() {
		return Objects.equals(this.status, EnableStatus.ENABLE.getCode());
	}

	/**
	 * 获取车位实时状态
	 *
	 * @return 车位实时状态 {@link ParkingPlaceStatus}
	 */
	@JsonIgnore
	public ParkingPlaceStatus getRealTimeStatus() {
		if(null == realTimeStatusLoader) {
			return null;
		}
		return realTimeStatusLoader.get();
	}

	/**
	 * 设置车位实时状态加载器
	 *
	 * @param realTimeStatusLoader 车位实时状态加载器 {@link LazyLoader}<{@link ParkingPlaceStatus}>
	 */
	public void setRealTimeStatusLoader(LazyLoader<ParkingPlaceStatus> realTimeStatusLoader) {
		Objects.requireNonNull(realTimeStatusLoader, "realTimeStatusLoader is null");
		this.realTimeStatusLoader = realTimeStatusLoader;
	}

}
