package com.leliven.park.domain.basic.inspection.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 巡检子任务附加常量
 *
 * <AUTHOR>
 */
@ValueObject
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionSubTaskAdditionalConst {

    /**
     * 车牌
     */
    public static final String PLATE = "plate";

    /**
     * 车牌颜色
     */
    public static final String PLATE_COLOR = "plateColor";

    /**
     * 车牌图片
     */
    public static final String PLATE_IMAGE = "plateImage";

    /**
     * 场景图片
     */
    public static final String SCENE_IMAGE = "sceneImage";

    /**
     * 最近抓拍图片URL
     */
    public static final String LAST_CAMERA_CAPTURE_IMAGE_URL = "lastCameraCaptureImageUrl";

    /**
     * 最近抓拍时间
     */
    public static final String LAST_CAMERA_CAPTURE_TIME = "lastCameraCaptureTime";

}

