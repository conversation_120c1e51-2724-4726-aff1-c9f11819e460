package com.leliven.park.domain.order.parking.specification;

import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.ddd.core.specification.CompositeSpecification;

import java.util.Objects;

/**
 * 停车订单相同规约
 *
 * <p>
 * 用于判断停车订单是否相同
 * </p>
 *
 * <AUTHOR>
 */
public class ParkingOrderSameSpec extends CompositeSpecification<ParkingOrderAgg> {

	private final Long orderId;

	public ParkingOrderSameSpec(Long orderId){
		this.orderId = orderId;
	}

	@Override
	public boolean isSatisfiedBy(ParkingOrderAgg candidate) {
		return Objects.nonNull(candidate) && candidate.getId().equals(orderId);
	}
}
