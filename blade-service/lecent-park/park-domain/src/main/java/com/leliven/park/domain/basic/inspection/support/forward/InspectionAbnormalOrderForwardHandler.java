package com.leliven.park.domain.basic.inspection.support.forward;

import com.google.common.collect.Lists;
import com.leliven.park.domain.basic.inspection.model.InspectionForwardContext;
import com.leliven.park.domain.basic.inspection.model.InspectionRecord;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionPhenomenonForwardRule;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionSubTaskAdditionalConst;
import com.leliven.park.domain.order.abnormal.ParkingPlaceAbnormalOrderGateway;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.vehicle.model.Vehicle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 巡检停车异常订单转发处理器
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class InspectionAbnormalOrderForwardHandler implements InspectionForwardHandler {

    private final ParkingPlaceAbnormalOrderGateway abnormalOrderGateway;

    @Override
    public void handle(List<InspectionForwardContext> forwardContexts) {
        forwardContexts.stream()
            .filter(c -> supports(c.getTargetType()) && c.isNeedForward())
            .forEach(this::forward);
    }

    @Override
    public InspectionPhenomenonForwardRule.TargetType getTargetType() {
        return InspectionPhenomenonForwardRule.TargetType.ABNORMAL_PARKING_ORDER;
    }

    /**
     * 转发停车异常订单
     *
     * @param context 转发上下文 {@link InspectionForwardContext}
     */
    private void forward(InspectionForwardContext context) {
        InspectionRecord inspectionRecord = context.getInspectionRecord();
        InspectionPhenomenonForwardRule forwardRule = context.getForwardRule();
        Map<String, String> additionalInfo = inspectionRecord.getAdditionalInfo();
        String plate = additionalInfo.get(InspectionSubTaskAdditionalConst.PLATE);
        String plateColor = additionalInfo.get(InspectionSubTaskAdditionalConst.PLATE_COLOR);
        List<String> attachments = Lists.newArrayList(
            additionalInfo.get(InspectionSubTaskAdditionalConst.PLATE_IMAGE),
            additionalInfo.get(InspectionSubTaskAdditionalConst.SCENE_IMAGE));
        abnormalOrderGateway.asPublisher()
            .publishAbnormalOrder(forwardRule.getTargetId(), inspectionRecord.getObject().getId(), Vehicle.of(plate, plateColor), attachments, inspectionRecord.getInspector(), inspectionRecord.getInspectionTime());
    }



}
