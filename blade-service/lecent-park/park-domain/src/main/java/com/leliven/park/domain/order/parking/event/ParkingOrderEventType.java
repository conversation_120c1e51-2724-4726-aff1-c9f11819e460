package com.leliven.park.domain.order.parking.event;

import com.google.common.collect.Sets;
import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 停车订单事件类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ParkingOrderEventType implements DomainEventType {

	ENTERED("进场"),
	ENTER_INFO_CHANGED("进场信息变更"),
	MERGED("合并订单"),
	COVERED("覆盖"),
	EXITED("出场"),
	CLEARED("清除")
	;

	private final String description;

	public static ParkingOrderEventType resolve(String name) {
		for (ParkingOrderEventType temp : ParkingOrderEventType.values()) {
			if (temp.name().equalsIgnoreCase(name)) {
				return temp;
			}
		}
		return null;
	}

	public boolean isEnterGroup() {
		return Group.ENTER == Group.resolve(this);
	}

	public boolean isExitGroup() {
		return Group.EXIT == Group.resolve(this);
	}

	public Group getGroup() {
		return Group.resolve(this);
	}

	@AllArgsConstructor
	public enum Group {

		ENTER(Sets.newHashSet(ENTERED, ENTER_INFO_CHANGED, MERGED)),
		EXIT(Sets.newHashSet(COVERED, EXITED, CLEARED)),
		;
		private final Set<ParkingOrderEventType> values;

		public static Group resolve(ParkingOrderEventType eventType) {
			for (Group temp : Group.values()) {
				if (temp.values.contains(eventType)) {
                    return temp;
                }
			}
			return null;
		}

	}
}
