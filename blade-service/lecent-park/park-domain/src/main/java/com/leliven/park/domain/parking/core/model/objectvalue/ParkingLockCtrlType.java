package com.leliven.park.domain.parking.core.model.objectvalue;

import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 路边车位锁控制类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ParkingLockCtrlType implements DomainEventType {

	LOCK("升锁"),
	UNLOCK("降锁"),
	;

	/**
	 * 描述
	 */
	private final String desc;

	/**
	 * 是否为升锁指令类型
	 *
	 * @return {@code ture} 升锁 {@code false} 降锁
	 */
	public boolean isLock() {
		return this == LOCK;
	}
}
