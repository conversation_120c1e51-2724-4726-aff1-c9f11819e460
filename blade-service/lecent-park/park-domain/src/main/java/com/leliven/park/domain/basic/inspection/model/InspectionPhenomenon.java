package com.leliven.park.domain.basic.inspection.model;

import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionPhenomenonForwardRule;
import com.leliven.ddd.core.model.BaseDomain;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springblade.common.enums.EnableStatus;
import org.springblade.core.log.exception.ServiceException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 巡检现象领域对象
 *
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class InspectionPhenomenon extends BaseDomain {

	private static final Long DEFAULT_ABNORMAL_REASON_ID = 0L;

	private String name;
	private String code;
	private String description;
	private final List<InspectionPhenomenonForwardRule> forwardRules;
	private final Set<Long> abnormalReasonIds;

	public InspectionPhenomenon(Long id) {
		this.id = id;
		this.forwardRules = new ArrayList<>();
		this.abnormalReasonIds = new HashSet<>();
	}

	/**
	 * 构造函数
	 *
	 * @param name 名称
	 * @param code 编码
	 * @param description 描述
	 */
	private InspectionPhenomenon(String name, String code, String description) {
		this(null);
		this.name = name;
		this.code = code;
		this.description = description;
	}

	/**
	 * 获取巡检现象
	 *
	 * @param id 巡检现象ID
	 * @return 巡检现象
	 */
	public static InspectionPhenomenon of(Long id) {
		return new InspectionPhenomenon(id);
	}

	/**
	 * 创建巡检现象
	 *
	 * @param name 巡检现象名称
	 * @param code 巡检现象编码
	 * @param description 巡检现象描述
	 * @return 巡检现象
	 */
	public static InspectionPhenomenon create(String name, String code, String description) {
        return new InspectionPhenomenon(name, code, description)
            .addDefaultAbnormalReasonId();
	}

	/**
	 * 启用
	 *
	 * @return this
	 */
	public InspectionPhenomenon enable() {
		if (isEnabled()) {
			throw new ServiceException("巡检现象已启用，请勿重复操作");
		}
		this.status = EnableStatus.ENABLE.getCode();
		return this;
	}

	/**
	 * 禁用
	 *
	 * @return this
	 */
	public InspectionPhenomenon disable() {
		if (isDisabled()) {
			throw new ServiceException("巡检现象已禁用，请勿重复操作");
		}
		this.status = EnableStatus.DISABLE.getCode();
		return this;
	}

	/**
	 * 是否启用
	 *
	 * @return {@code true}-启用 {@code false}-禁用
	 */
	public boolean isEnabled() {
		return Objects.equals(this.status, EnableStatus.ENABLE.getCode());
	}

	/**
	 * 是否禁用
	 *
	 * @return {@code true}-禁用 {@code false}-启用
	 */
	public boolean isDisabled() {
		return !isEnabled();
	}

	/**
	 * 设置名称
	 *
	 * @param name 巡检现象名称
	 * @return this
	 */
	public InspectionPhenomenon name(String name) {
		this.name = name;
		return this;
	}

	/**
	 * 设置编码
	 *
	 * @param code 巡检现象编码
	 * @return this
	 */
	public InspectionPhenomenon code(String code) {
		this.code = code;
		return this;
	}

	/**
	 * 添加描述
	 *
	 * @param description 描述
	 * @return this
	 */
	public InspectionPhenomenon description(String description) {
		this.description = description;
		return this;
	}

	/**
	 * 添加一堆转发规则
	 *
	 * @param forwardRule 转发规则
	 * @return this
	 */
	public InspectionPhenomenon addForwardRules(List<InspectionPhenomenonForwardRule> forwardRule) {
		this.forwardRules.addAll(forwardRule);
		return this;
	}

	/**
	 * 添加默认异常原因ID
	 *
	 * @return this
	 */
	public InspectionPhenomenon addDefaultAbnormalReasonId() {
		this.abnormalReasonIds.add(DEFAULT_ABNORMAL_REASON_ID);
		return this;
	}

	/**
	 * 添加多个异常原因ID（自动去重）
	 *
	 * @param abnormalReasonIds 异常原因ID列表
	 * @return this
	 */
	public InspectionPhenomenon addAbnormalReasonIds(List<Long> abnormalReasonIds) {
		if (abnormalReasonIds != null) {
			abnormalReasonIds.stream()
				.filter(Objects::nonNull)
				.forEach(this.abnormalReasonIds::add);
		}
		return this;
	}

	/**
	 * 获取异常原因ID列表
	 *
	 * @return 异常原因ID列表
	 */
	public List<Long> getAbnormalReasonIdList() {
		return new ArrayList<>(abnormalReasonIds);
	}

	/**
	 * 判断是否包含指定的异常原因ID
	 *
	 * @param abnormalReasonId 异常原因ID
	 * @return 是否包含
	 */
	public boolean containsAbnormalReasonId(Long abnormalReasonId) {
		return abnormalReasonIds.contains(abnormalReasonId);
	}

	/**
	 * 获取工单转发规则
	 */
	public Optional<InspectionPhenomenonForwardRule> getWorkOrderForwardRule() {
		return getForwardRule(InspectionPhenomenonForwardRule.TargetType.WORK_ORDER);
	}

	/**
	 * 根据转发目标类型获取转发规则
	 *
	 * @param targetType 转发目标类型
	 * @return 转发规则
	 */
	public Optional<InspectionPhenomenonForwardRule> getForwardRule(InspectionPhenomenonForwardRule.TargetType targetType) {
		return this.forwardRules.stream()
			.filter(rule -> Objects.equals(rule.getTargetType(), targetType))
			.findFirst();
	}

}
