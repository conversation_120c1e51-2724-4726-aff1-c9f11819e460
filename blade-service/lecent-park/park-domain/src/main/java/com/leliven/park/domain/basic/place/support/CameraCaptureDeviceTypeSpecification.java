package com.leliven.park.domain.basic.place.support;

import com.google.common.collect.Sets;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.specification.Specification;

import java.util.Objects;
import java.util.Set;

/**
 * 摄像头抓拍设备类型规约
 *
 * <AUTHOR>
 */
@DomainService
public class CameraCaptureDeviceTypeSpecification implements Specification<ParkingDevice> {

	private final Set<DeviceType> deviceTypes;

	public CameraCaptureDeviceTypeSpecification() {
		this.deviceTypes = Sets.newHashSet(
			DeviceType.VIDEO_PILE, DeviceType.HIGH_VIDEO_CAMERA, DeviceType.PATROL_VEHICLE);
	}

	@Override
	public boolean isSatisfiedBy(ParkingDevice candidate) {
		return Objects.nonNull(candidate) && deviceTypes.contains(candidate.getType());
	}
}
