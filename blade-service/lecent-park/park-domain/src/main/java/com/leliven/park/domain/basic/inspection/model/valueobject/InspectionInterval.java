package com.leliven.park.domain.basic.inspection.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import com.leliven.ddd.core.valueobject.TimeUnit;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 巡检间隔
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionInterval {

    /**
     * 巡检间隔单位
     */
    private TimeUnit unit;
    /**
     * 巡检间隔值
     */
    private Integer value;

    private InspectionInterval(TimeUnit unit, Integer value) {
        this.unit = unit;
        this.value = value;
    }

    public static InspectionInterval of(Integer intervalUnit, Integer intervalValue) {
        return of(TimeUnit.resolve(intervalUnit), intervalValue);
    }

    public static InspectionInterval of(TimeUnit unit, Integer value) {
        return new InspectionInterval(unit, value);
    }
}
