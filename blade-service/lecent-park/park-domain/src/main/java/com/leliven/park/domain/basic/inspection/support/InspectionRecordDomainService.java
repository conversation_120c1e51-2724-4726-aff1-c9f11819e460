package com.leliven.park.domain.basic.inspection.support;

import com.leliven.park.domain.basic.inspection.InspectionRecordGateway;
import com.leliven.park.domain.basic.inspection.event.InspectionRecordBatchCreatedEvent;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionObjectRecord;
import com.leliven.park.domain.basic.inspection.model.InspectionRecord;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.Collections;
import java.util.List;

/**
 * 巡检记录领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class InspectionRecordDomainService {

    private final DomainEventPublisher domainEventPublisher;
    private final InspectionRecordGateway inspectionRecordGateway;

    /**
     * 上报巡检记录
     *
     * @param inspectionObjectRecord 巡检对象记录
     * @return 创建的巡检记录列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<InspectionRecord> report(InspectionObjectRecord inspectionObjectRecord) {
        return create(inspectionObjectRecord);
    }

    /**
     * 上报巡检记录并创建工单
     *
     * @param inspectionObjectRecord 巡检对象记录
     * @return 创建的巡检记录列表
     */
    public List<InspectionRecord> create(@Nonnull InspectionObjectRecord inspectionObjectRecord) {
        List<InspectionRecord> records = inspectionObjectRecord.toInspectionRecords();

        if (Func.isEmpty(records)) {
            log.warn("子任务 {} 完成，但未生成任何巡检记录", inspectionObjectRecord.getSubTaskId());
            return Collections.emptyList();
        }

        inspectionRecordGateway.saveBatch(records);

        domainEventPublisher.publishEvent(InspectionRecordBatchCreatedEvent.of(records));

        return records;
    }

}
