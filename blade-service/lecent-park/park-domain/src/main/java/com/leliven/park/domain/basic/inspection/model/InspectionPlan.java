package com.leliven.park.domain.basic.inspection.model;

import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionExecutorAssignConfig;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionInterval;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.ddd.core.valueobject.DateTimeRange;
import com.leliven.ddd.core.valueobject.TimeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springblade.core.log.exception.ServiceException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡检计划领域对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectionPlan extends TenantDomain {

    /**
     * 计划名称
     */
    private String name;

    /**
     * 有效期区间
     */
    private DateTimeRange validityPeriod;

    /**
     * 工作时间区间
     */
    private TimeRange workTimeRange;

    /**
     * 巡检间隔
     */
    private InspectionInterval interval;

	/**
	 * 任务名称配置(生成任务时使用)
	 */
	private String taskName;

    /**
     * 任务优先级
     */
    private Integer taskPriority;

    /**
     * 巡检项ID列表
     */
    private List<Long> itemIds;

    /**
     * 车场ID列表
     */
    private List<Long> parklotIds;

    /**
     * 执行人员配置
     */
    private List<ExecutorConfig> executorConfigs;

    /**
     * 巡检人员指派配置
     */
    private InspectionExecutorAssignConfig executorAssignConfig;

    /**
     * 备注
     */
    private String remark;


    public InspectionPlan() {

    }

    public InspectionPlan(Long id) {
        super(id);
    }

    /**
     * 更新巡检计划
     *
     * @param plan 新的巡检计划
     */
    public InspectionPlan edit(InspectionPlan plan) {
        this.name = plan.getName();
        this.validityPeriod = plan.getValidityPeriod();
        this.workTimeRange = plan.getWorkTimeRange();
        this.interval = plan.getInterval();
        this.itemIds = plan.getItemIds();
        this.parklotIds = plan.getParklotIds();
        this.executorConfigs = plan.getExecutorConfigs();
        this.executorAssignConfig = plan.executorAssignConfig;
        this.taskPriority = plan.getTaskPriority();
        this.remark = plan.getRemark();
		return this;
    }

    /**
     * 配置计划名称
     *
     * @param name 计划名称
     * @return 当前对象
     */
    public InspectionPlan configureName(String name) {
        this.name = name;
        return this;
    }

    /**
     * 配置有效期区间
     *
     * @param validityPeriod 有效期区间
     * @return 当前对象
     */
    public InspectionPlan configureValidityPeriod(DateTimeRange validityPeriod) {
        this.validityPeriod = validityPeriod;
        return this;
    }

    /**
     * 配置工作时间区间
     *
     * @param workTimeRange 工作时间区间
     * @return 当前对象
     */
    public InspectionPlan configureWorkTimeRange(TimeRange workTimeRange) {
        this.workTimeRange = workTimeRange;
        return this;
    }

    /**
     * 配置巡检间隔
     *
     * @param interval 巡检间隔
     * @return 当前对象
     */
    public InspectionPlan configureInterval(InspectionInterval interval) {
        this.interval = interval;
        return this;
    }

    /**
     * 配置巡检项并确保无重复
     *
     * @param itemIds 巡检项ID列表
     * @return 当前对象
     */
    public InspectionPlan configureItems(List<Long> itemIds) {
        this.itemIds = itemIds.stream().distinct().collect(Collectors.toList());
        return this;
    }

    /**
     * 配置巡检车场并确保无重复
     *
     * @param parklotIds 车场ID列表
     * @return 当前对象
     */
    public InspectionPlan configureParklots(List<Long> parklotIds) {
        this.parklotIds = parklotIds.stream().distinct().collect(Collectors.toList());
        return this;
    }

    /**
     * 配置执行人员
     *
     * @param executorConfigs 执行人员配置列表
     * @return 当前对象
     */
    public InspectionPlan configureExecutors(List<ExecutorConfig> executorConfigs) {
        this.executorConfigs = executorConfigs;
        return this;
    }

    /**
     * 设置巡检人员指派配置
     *
     * @param executorAssignConfig 巡检人员指派配置
     * @return 当前对象
     */
    public InspectionPlan configureExecutorAssignConfig(InspectionExecutorAssignConfig executorAssignConfig) {
        this.executorAssignConfig = executorAssignConfig;
        return this;
    }

    /**
     * 配置任务优先级
     *
     * @param taskPriority 任务优先级
     * @return 当前对象
     */
    public InspectionPlan configureTaskPriority(Integer taskPriority) {
        this.taskPriority = taskPriority;
        return this;
    }

    /**
     * 配置备注
     *
     * @param remark 备注
     * @return 当前对象
     */
    public InspectionPlan configureRemark(String remark) {
        this.remark = remark;
        return this;
    }


    /**
     * 启用巡检计划
	 *
	 * @return 当前对象
     */
    public InspectionPlan enable() {
        if (isExpired()) {
            throw new ServiceException("巡检计划已过期,无法启用");
        }
        this.status = 1;

		return this;
    }

    /**
	 * 停用巡检计划
	 *
	 * @return 当前对象
	 */
    public InspectionPlan disable() {
        this.status = 0;

		return this;
    }

	/**
	 * 判断是否可以生成任务
	 *
	 * @return 是否可以生成任务 {@code true} 可以生成任务 {@code false} 不可以生成任务
	 */
	public boolean canGenerateTask() {
        return !isExpired() && isEnabled();
    }

	/**
	 * 获取启用状态
	 *
	 * @return 启用状态 {@code true} 启用 {@code false} 停用
	 */
	public boolean isEnabled() {
        return status == 1;
    }
    /**
     * 计划是否过期
     *
     * @return 是否过期 {@code true} 过期 {@code false} 未过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(validityPeriod.getEndDateTime());
    }

    /**
     * 获取巡检间隔单位值
     * 用于与数据对象和DTO交互
     *
     * @return 巡检间隔单位值（1：小时，2：天，3：周）
     */
    public Integer getIntervalUnit() {
        if (interval == null) {
            return null;
        }
        switch (interval.getUnit()) {
            case HOUR:
                return 1;
            case DAY:
                return 2;
            case WEEK:
                return 3;
            default:
                throw new IllegalStateException("不支持的时间单位：" + interval.getUnit());
        }
    }

    /**
     * 获取巡检间隔值
     * 用于与数据对象和DTO交互
     *
     * @return 巡检间隔值
     */
    public Integer getIntervalValue() {
        return interval != null ? interval.getValue() : null;
    }

    @Data
    @Accessors(chain = true)
    public static class ExecutorConfig {
        /**
         * 分配类型
         */
        private String assignType;

        /**
         * 角色ID列表
         */
        private List<String> roleIds;
    }
}

