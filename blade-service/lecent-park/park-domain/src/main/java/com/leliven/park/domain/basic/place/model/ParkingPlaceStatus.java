package com.leliven.park.domain.basic.place.model;

import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.vehicle.model.Vehicle;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.ObjectValidator;

import java.time.Duration;
import java.util.Date;

/**
 * 车位状况关联领域对象
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParkingPlaceStatus extends TenantDomain {

    /**
     * 车位ID
     */
    private Long parkingPlaceId;
	/**
	 * 车场ID
	 */
	private Long parklotId;
    /**
     * 车辆
     */
    private Vehicle vehicle;
    /**
     * 停车订单 id
     */
    private Long parkingOrderId;
    /**
     * 进场时间
     */
    private Date enterTime;
    /**
     * 进场方式
     * @see ParkingTriggerWay
     */
    private Integer enterWay;
	/**
	 * 上一次停车订单 id
	 */
	private Long lastParkingOrderId;
    /**
     * 上一次离场时间
     */
    private Date lastExitTime;
    /**
     * 空闲状态 0：占用 1：空闲
     */
    private ParkingPlaceIdleState idleState;

    /**
     * 获取停车时长
     *
     * @param endTime 截止时间
     * @return 停车时长
     */
    public Duration getParkingDuration(Date endTime) {
        ObjectValidator.requireNonNull(endTime, "endTime must not be null");

        return null == enterTime ? Duration.ZERO : Duration.between(enterTime.toInstant(), endTime.toInstant());
    }

    /**
     * 是否被占用
     *
     * @return {@code true} 被占用 {@code false} 空闲
     */
    public boolean isOccupied() {
        return ParkingPlaceIdleState.OCCUPIED == idleState;
    }

    /**
     * 是否空闲
     *
     * @return {@code true} 空闲 {@code false} 被占用
     */
    public boolean isIdle() {
        return ParkingPlaceIdleState.IDLE == idleState;
    }
}
