package com.leliven.park.domain.basic.billingrule.model;

import cn.hutool.core.math.Money;
import com.leliven.ddd.core.valueobject.TimeRange;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.log.exception.ServiceException;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 临停计费分段配置领域对象
 *
 * <p>
 * 该对象表示停车场在特定时间段内的计费规则配置
 * </p>
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TempParkingBillingSegmentCfg extends TenantDomain {

    /**
     * 计费时间段
     */
    private TimeRange billingTimeRange;

    /**
     * 计费周期时长（分钟）
     */
    private Integer billingCycleMinutes;

    /**
     * 计费周期费用
     */
    private Money billingCycleAmount;

    /**
     * 封顶金额，0表示不封顶
     */
    private Money maxBillingAmount;

    /**
     * 跨段免费时长（分钟）- 当停车时长跨越到下一个分段时的免费时长
     */
    private Integer crossSegmentFreeMinutes;

    /**
     * 创建临停计费分段配置
     *
     * @param billingTimeRange 计费时间段
     * @param billingCycleMinutes 计费周期时长（分钟）
     * @param billingCycleAmount 计费周期费用
     * @param maxBillingAmount 封顶金额
     * @param crossSegmentFreeMinutes 跨段免费时长（分钟）
     * @return 临停计费分段配置 {@link TempParkingBillingSegmentCfg}
     */
    public static TempParkingBillingSegmentCfg of(TimeRange billingTimeRange,
                                                  Integer billingCycleMinutes,
                                                  Money billingCycleAmount,
                                                  Money maxBillingAmount,
                                                  Integer crossSegmentFreeMinutes) {

        // 参数校验
        Objects.requireNonNull(billingTimeRange, "计费时间段不能为空");
        Objects.requireNonNull(billingCycleMinutes, "计费周期时长不能为空");
        Objects.requireNonNull(billingCycleAmount, "计费周期费用不能为空");
        Objects.requireNonNull(maxBillingAmount, "封顶金额不能为空");
        Objects.requireNonNull(crossSegmentFreeMinutes, "跨段免费时长不能为空");

        if (billingCycleMinutes <= 0) {
            throw new ServiceException("计费周期时长必须大于0");
        }

        if (billingCycleAmount.getCent() < 0) {
            throw new ServiceException("计费周期费用不能为负数");
        }

        if (maxBillingAmount.getCent() < 0) {
            throw new ServiceException("封顶金额不能为负数");
        }

        if (crossSegmentFreeMinutes < 0) {
            throw new ServiceException("跨段免费时长不能为负数");
        }

        return new TempParkingBillingSegmentCfg(
            billingTimeRange,
            billingCycleMinutes,
            billingCycleAmount,
            maxBillingAmount,
            crossSegmentFreeMinutes
        );
    }

    /**
     * 判断指定时间是否在计费时间段内（闭区间）
     *
     * @param time 指定时间 {@link LocalTime}
     * @return 是否在计费时间段内 {@code true} 是, {@code false} 否
     */
    public boolean isWithinBillingTimeRange(@Nonnull LocalTime time) {
        return billingTimeRange.contains(time);
    }

    /**
     * 判断指定时间是否在计费时间段内（闭区间）
     *
     * @param dateTime 指定时间 {@link LocalDateTime}
     * @return 是否在计费时间段内 {@code true} 是, {@code false} 否
     */
    public boolean isWithinBillingTimeRange(@Nonnull LocalDateTime dateTime) {
        return isWithinBillingTimeRange(dateTime.toLocalTime());
    }

	/**
	 * 判断指定时间是否处于本分段且为免费分段
	 *
	 * @param dateTime 指定时间 {@link LocalTime}
	 * @return {@code true} 表示该时间点处于本分段且为免费分段，否则 {@code false}
	 */
	public boolean isWithinFreeSegment(@Nonnull LocalTime dateTime) {
		return isWithinBillingTimeRange(dateTime) && isFreeSegment();
	}

	/**
	 * 判断指定时间是否处于本分段且为免费分段
	 *
	 * @param dateTime 指定时间 {@link LocalDateTime}
	 * @return {@code true} 表示该时间点处于本分段且为免费分段，否则 {@code false}
	 */
	public boolean isWithinFreeSegment(@Nonnull LocalDateTime dateTime) {
		return isWithinFreeSegment(dateTime.toLocalTime());
	}

    /**
     * 判断是否跨天计费
     *
     * @return 是否跨天计费 {@code true} 是, {@code false} 否
     */
    public boolean isCrossDayBilling() {
        return billingTimeRange.getEndTime().isBefore(billingTimeRange.getStartTime());
    }

    /**
     * 判断是否设置封顶金额
     *
     * @return 是否设置封顶金额 {@code true} 是, {@code false} 否
     */
    public boolean hasMaxBillingAmount() {
        return maxBillingAmount.getCent() > 0;
    }

    /**
     * 判断是否设置跨段免费时长
     *
     * @return 是否设置跨段免费时长 {@code true} 是, {@code false} 否
     */
    public boolean hasCrossSegmentFreeMinutes() {
        return crossSegmentFreeMinutes > 0;
    }

    /**
     * 判断是否为免费分段（{@code billingCycleAmount}为0）
     *
     * @return {@code true} 免费分段, {@code false} 收费分段
     */
    public boolean isFreeSegment() {
        return billingCycleAmount != null && billingCycleAmount.getCent() == 0;
    }

    /**
     * 获取计费时间段的字符串表示
     *
     * @return 时间段字符串，格式：HH:mm-HH:mm
     */
    public String getBillingTimeRangeString() {
        return String.format("%02d:%02d-%02d:%02d",
            billingTimeRange.getStartTime().getHour(),
            billingTimeRange.getStartTime().getMinute(),
            billingTimeRange.getEndTime().getHour(),
            billingTimeRange.getEndTime().getMinute());
    }

    /**
     * 获取计费规则描述（对应UI显示）
     *
     * @return 计费规则描述
     */
    public String getBillingRuleDescription() {
        String timeRange = getBillingTimeRangeString();
        String cycleDesc = String.format("每%d分钟收费%s元", billingCycleMinutes, billingCycleAmount.getAmount());
        String crossDesc = hasCrossSegmentFreeMinutes() ? String.format("，跨段%d分钟内免费", crossSegmentFreeMinutes) : "";
        String maxDesc = hasMaxBillingAmount() ? String.format("，封顶%s元", maxBillingAmount.getAmount()) : "";

        return String.format("%s %s%s%s", timeRange, cycleDesc, crossDesc, maxDesc);
    }

    /**
     * 获取跨段计费说明
     *
     * @return 跨段计费说明
     */
    public String getCrossSegmentBillingDescription() {
        if (hasCrossSegmentFreeMinutes()) {
            return String.format("当停车时长跨越到下一个分段时，在下一个分段中享受%d分钟的免费时长", crossSegmentFreeMinutes);
        } else {
            return "无跨段免费时长";
        }
    }

    /**
     * 获取计费周期费用（BigDecimal格式）
     *
     * @return 计费周期费用
     */
    public BigDecimal getBillingCycleAmountAsBigDecimal() {
        return billingCycleAmount.getAmount();
    }

    /**
     * 获取封顶金额（BigDecimal格式）
     *
     * @return 封顶金额
     */
    public BigDecimal getMaxBillingAmountAsBigDecimal() {
        return maxBillingAmount.getAmount();
    }
}
