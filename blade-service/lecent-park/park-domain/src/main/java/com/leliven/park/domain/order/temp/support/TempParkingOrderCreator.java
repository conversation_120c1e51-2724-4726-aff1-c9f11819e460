package com.leliven.park.domain.order.temp.support;

import cn.hutool.core.math.Money;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.en.temporder.CreateWay;
import com.leliven.park.domain.basic.parklot.ParklotCacheRepositoryI;
import com.leliven.park.domain.basic.parklot.exception.NotFoundParklotException;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.domain.common.Discount;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.domain.order.parking.ParkingOrderRepositoryI;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.parking.support.ParkingOrderValidator;
import com.leliven.park.domain.order.temp.TempParkingOrderRepositoryI;
import com.leliven.park.domain.order.temp.event.TempParkingOrderEvent;
import com.leliven.park.domain.order.temp.event.TempParkingOrderEventType;
import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.park.domain.order.temp.model.TempParkingUnifiedOrderContext;
import com.leliven.park.domain.order.todo.entity.ParkingTodo;
import com.leliven.park.domain.order.todo.service.ParkingTodoValidator;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.common.utils.SequenceNoUtils;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 临停订单创建器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TempParkingOrderCreator {

    public static final long PAY_TEMP_PARKING_TIME_OUT = 90;

    @Resource
    private ParkingTodoValidator parkingTodoValidator;
    @Resource
    private ParkingOrderValidator parkingOrderValidator;
    @Resource
    private ParkingOrderRepositoryI parkingOrderRepository;
    @Resource
    private ParklotCacheRepositoryI parklotCacheRepository;
    @Resource
    private TempParkingOrderRepositoryI tempParkingOrderRepository;


    public TempParkingOrderDomain create(@NonNull TempParkingUnifiedOrderContext context) {
        // 校验支付代办
        verifyPayTodo(context.getParkingTodo());
        // 查询停车订单
        ParkingOrderAgg parkingOrder = parkingOrderRepository.getById(context.getParkingTodo().getParkingOrderId());
        // 校验停车记录
        verifyParkingOrder(parkingOrder);

        return createOrder(context);
    }

    protected void verifyPayTodo(ParkingTodo parkingTodo) {
        // 校验支付代办
        parkingTodoValidator.verifyPayTodo(parkingTodo);
    }

    protected void verifyParkingOrder(ParkingOrderAgg parkingOrder) {
        // 校验停车记录是否存在
        parkingOrderValidator.verifyParkingOrderShouldNotNull(parkingOrder);
    }


    private TempParkingOrderDomain createOrder(@NonNull TempParkingUnifiedOrderContext context) {
        ParklotDomain parklot = this.parklotCacheRepository.getOneOptById(context.getParkingTodo().getParklotId())
            .orElseThrow(() -> new NotFoundParklotException(context.getParkingTodo().getParklotId()));
        // 创建临停订单
        TempParkingOrderDomain tempParkingOrder = createOrder(context.getParkingTodo());

        return tempParkingOrder.description(parklot.getName())
            .attach("停车费用支付")
            .expireDuration(PAY_TEMP_PARKING_TIME_OUT)
            .payScene(context.getScene().getPayScene())
            .assignReceivingMerchant(context.getPayMerchant());
    }

    private TempParkingOrderDomain createOrder(@NonNull ParkingTodo parkingTodo) {
        TempParkingOrderDomain order = TempParkingOrderDomain.create();
        order.setTradeNo(SequenceNoUtils.generateNo());
        order.setParkingId(parkingTodo.getParkingOrderId());
        order.setChannelId(parkingTodo.getParkingInfo().getChannelId());
        order.setParklotId(parkingTodo.getParklotId());
        order.setVehicle(parkingTodo.getParkingInfo().getVehicle());
        order.setMerchantIds(Func.toStr(parkingTodo.getMerchantId(), StringPool.EMPTY));
        order.setTodoId(parkingTodo.getId());
        order.setCreateDept(parkingTodo.getCreateDept());
        order.setCreateUser(parkingTodo.getCreateUser());
        order.setChargeUserId(parkingTodo.getCreateUser());
        order.setTenantId(parkingTodo.getTenantId());
        order.setChargeData(parkingTodo.getData());
        order.setCreateWay(parkingTodo.getCreateWay());
        if (Func.notNull(parkingTodo.getCouponId())) {
            order.setCouponIds(parkingTodo.getCouponId().toString());
        }

        ProjectCost projectCost = parkingTodo.getData();
        ObjectValidator.requireNonNull(projectCost, "停车费用异常");
        order.setParkingCost(createParkingCost(projectCost));

        // 已出场都是补缴订单
		if (CreateWay.ABSENT == parkingTodo.getCreateWay()) {
			order.setOrderStyle(4);
		}

        return order;
    }

    private ParkingCost createParkingCost(ProjectCost projectCost) {
        return ParkingCost.create()
            .addExpectedReceiveAmount(new Money(projectCost.getCurrentExpectedReceiveAmount()))
            .addDiscount(new Discount(CouponCategory.MERCHANT, new Money(projectCost.getCurrentMerchantAmount())))
            // 先不分辨其他优惠类型，后续再处理
            .addDiscount(new Discount(CouponCategory.OTHER,
                new Money(projectCost.getCurrentDiscountAmount().subtract(projectCost.getCurrentMerchantAmount()))));
    }

    public void save(TempParkingOrderDomain order) {
        this.tempParkingOrderRepository.save(order);
        SpringDomainEventPublisher.publish(new TempParkingOrderEvent(this, TempParkingOrderEventType.CREATED, order));
    }

}
