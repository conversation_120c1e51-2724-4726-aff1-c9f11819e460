package com.leliven.park.domain.parking.core.support.factory;

import com.leliven.park.common.model.valueobject.BasicParkingScene;
import com.leliven.park.domain.basic.billingrule.TempParkingBillingRuleCfgGateway;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.order.parking.ParkingOrderRepositoryI;
import com.leliven.park.domain.order.parking.specification.ParkingOrderSameSpec;
import com.leliven.park.domain.order.parking.specification.ParkingOrderStatusPresentSpec;
import com.leliven.park.domain.parking.core.ParkingRepositoryI;
import com.leliven.park.domain.parking.core.model.objectvalue.RoadsideParkingLockCtrlSceneParam;
import com.leliven.park.domain.parking.core.specification.LockCtrlSpecification;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.specification.ConjunctionSpecification;
import com.leliven.ddd.core.specification.Specification;
import com.leliven.ddd.core.support.LazyLoader;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.exception.NotFoundException;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 车位锁控制规约工厂
 *
 * <p>
 * 设计初衷说明：<br>
 * 升锁/降锁的物理操作对象是“车位”，因此规约的入口参数采用车位（ParkingSpace）。<br>
 * 但实际业务判断（如是否允许升锁/降锁）可能会依赖于该车位当前关联的订单（如订单状态、是否在免费时段等）。<br>
 * 因此，规约内部会通过车位ID查找当前关联的订单（parkingOrderAgg），再基于订单属性进行判断。<br>
 * 这样设计既保证了物理操作的直接性，也兼顾了业务判断的灵活性，便于后续扩展和维护。
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@Slf4j
@DomainService
public class ParkingLockCtrlSpecFactory {

    /**
     * 升锁场景规约映射
     */
    private final Map<String, Function<RoadsideParkingLockCtrlSceneParam, LockCtrlSpecification>> lockSceneSpecs = new HashMap<>();
    /**
     * 降锁场景规约映射
     */
    private final Map<String, Function<RoadsideParkingLockCtrlSceneParam, LockCtrlSpecification>> unlockSceneSpecs = new HashMap<>();

	private final ParkingRepositoryI parkingRepo;
    private final ParkingOrderRepositoryI parkingOrderRepo;
    private final TempParkingBillingRuleCfgGateway billingRuleCfgGateway;
    private final ParkingOrderFreeDurationSpecFactory freeDurationSpecFactory;

    public ParkingLockCtrlSpecFactory(ParkingRepositoryI parkingRepo,
									  ParkingOrderRepositoryI parkingOrderRepo,
									  TempParkingBillingRuleCfgGateway billingRuleCfgGateway,
									  ParkingOrderFreeDurationSpecFactory freeDurationSpecFactory) {
		this.parkingRepo = parkingRepo;
		this.parkingOrderRepo = parkingOrderRepo;
        this.billingRuleCfgGateway = billingRuleCfgGateway;
        this.freeDurationSpecFactory = freeDurationSpecFactory;
    }

    @PostConstruct
    public void init() {
        registerLockSceneSpec();
        registerUnlockSceneSpec();
    }


    public LockCtrlSpecification getLockSpec(RoadsideParkingLockCtrlSceneParam param) {
        if (!lockSceneSpecs.containsKey(param.getScene().getValue())) {
            throw new NotFoundException("未找到对应的升锁规约，场景：" + param.getScene().getValue());
        }

        return lockSceneSpecs.get(param.getScene().getValue()).apply(param);
    }

	public LockCtrlSpecification getUnlockSpec(RoadsideParkingLockCtrlSceneParam param) {
		return unlockSceneSpecs.getOrDefault(param.getScene().getValue(), this::createDefaultLockSpec)
            .apply(param);
	}

    private void registerLockSceneSpec() {
        lockSceneSpecs.put(BasicParkingScene.START_BILLING.getValue(), AutoLockSpec::new);
        lockSceneSpecs.put(BasicParkingScene.PAID_FREE_TIMEOUT.getValue(), AutoLockSpec::new);
        lockSceneSpecs.put(BasicParkingScene.MANUAL_OPT.getValue(), this::createDefaultLockSpec);
    }

    private void registerUnlockSceneSpec() {
        // 未定义对应场景的降锁规约，使用默认规约
    }

	private LockCtrlSpecification createDefaultLockSpec(RoadsideParkingLockCtrlSceneParam param) {
		return parkingSpace -> true;
	}


	/**
     * 自动升锁规约
     *
     * <p>
     * 目前使用于下面2种场景：
     * <ul>
     * <li>1. 临停订单开始计费后，自动升锁 {@link BasicParkingScene#START_BILLING }</li>
     * <li>2. 临停订单缴费后免费时长超时，自动升锁 {@link BasicParkingScene#PAID_FREE_TIMEOUT}</li>
     * </ul>
     * 注：其他场景请先验证自动升锁规约是否满足
     * </p>
     *
     * <p>
     * 本规约是由多个规约组合成一个合并规约，用于判断是否满足升锁条件，包含以下规则：
     * <ul>
     *     <li>1. 车位当前订单应与升锁通知中的订单是相同订单 {@link ParkingOrderSameSpec}</li>
     *     <li>2. 车位当前订单状态应为在场 {@link ParkingOrderStatusPresentSpec}</li>
     *     <li>3. 车位当前订单不处于入场免费时长内、车位当前订单不处于缴费后免费时长内
	 *     合并规约 {@link ParkingOrderFreeDurationSpecFactory#createParkingFreeDurationSpec(TempParkingBillingRuleCfg)}
	 *     </li>
	 *     <li>
	 *         4. 当租户开启 {@code 免费分段不升锁} {@link AutoLockSpec#isNoLockInFreeSegmentEnabled(String)} 条件时，
	 *         计费分段自动升锁规约 {@link AutoLockSpec#createBillingSegmentAutoLockSpec()} 生效
	 *     </li>
     * </ul>
     * </p>
     *
     * @see RoadsideParkingLockCtrlSceneParam
     */
	public class AutoLockSpec extends ConjunctionSpecification<ParkingSpace> implements LockCtrlSpecification {

		private LazyLoader<TempParkingBillingRuleCfg> billingRuleLazyLoader;

		public AutoLockSpec(RoadsideParkingLockCtrlSceneParam param) {
			super(new ArrayList<>());
			addSpecification(createParkingOrderAutoLockSpec(param));
		}

		@Override
		public boolean isSatisfiedBy(ParkingSpace candidate) {
			if (billingRuleLazyLoader == null) {
				billingRuleLazyLoader = getBillingRuleLazyLoader(candidate.getParklotId());
			}

			if (isNoLockInFreeSegmentEnabled(candidate.getTenantId())) {
				// 如果启用免费分段不升锁功能，则动态添加免费分段自动升锁规约
				addSpecification(createBillingSegmentAutoLockSpec());
			}

			return super.isSatisfiedBy(candidate);
		}


		private Specification<ParkingSpace> createParkingOrderAutoLockSpec(RoadsideParkingLockCtrlSceneParam param) {
			return parkingSpace -> new ConjunctionSpecification<>(
				order -> new ParkingOrderSameSpec(param.getParkingOrderId()).isSatisfiedBy(order),
				ParkingOrderStatusPresentSpec.INSTANCE::isSatisfiedBy,
				order -> freeDurationSpecFactory.createParkingFreeDurationSpec(billingRuleLazyLoader.get())
					.not().isSatisfiedBy(order)
			).isSatisfiedBy(parkingOrderRepo.getPresentByPlaceId(parkingSpace.getId()));
		}

		private Specification<ParkingSpace> createBillingSegmentAutoLockSpec() {
			return parkingSpace -> {
				// 启用免费时段不升锁功能，若在免费时段内，不满足该规约, 则不发送升锁指令
				boolean isSatisfied = !billingRuleLazyLoader.get().isWithinFreeSegment(LocalDateTime.now());

				log.info("车位[{}]所属租户[{}]已启用免费时段不升锁功能，是否满足计费分段自动升锁规约：{}",
					parkingSpace.getPayCode(), parkingSpace.getTenantId(), isSatisfied);

				return isSatisfied;
			};
		}

		/**
		 * 判断指定租户是否开启 {@code 免费分段不升锁} 功能
		 *
		 * @param tenantId 租户ID
		 * @return {@code true} 已开启，{@code false} 未开启
		 */
		private boolean isNoLockInFreeSegmentEnabled(String tenantId) {
			return parkingRepo.getLockCtrlRuleConfig().isNoLockInFreeSegmentEnabled(tenantId);
		}

		/**
		 * 根据车位获取所属车场的临停收费规则
		 *
		 * @param parklotId 车场ID
		 * @return 临停计费规则 {@link TempParkingBillingRuleCfg}
		 */
		private LazyLoader<TempParkingBillingRuleCfg> getBillingRuleLazyLoader(Long parklotId) {
			// 暂时使用小型车收费规则
			return new LazyLoader<>(() ->
				billingRuleCfgGateway.asCacheSupplier().getDefaultByParklotId(parklotId)
					.orElseThrow(() -> new NotFoundException("未找到停车场默认收费规则，停车场ID：" + parklotId))
			);
		}
	}


}
