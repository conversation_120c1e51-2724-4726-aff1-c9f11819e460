package com.leliven.park.domain.order.temp.support;

import com.leliven.park.domain.basic.payway.entity.ParklotPayMerchant;
import com.leliven.park.domain.order.temp.event.TempParkingOrderPaySuccessEvent;
import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.park.domain.order.temp.model.TempParkingUnifiedOrderAdapter;
import com.leliven.park.domain.order.todo.entity.ParkingTodo;
import com.leliven.park.domain.order.unified.model.UnifiedOrder;
import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import com.leliven.park.domain.order.unified.model.valueobject.UnifiedOrderResponse;
import com.leliven.park.domain.order.unified.support.interfaces.UnifiedOrderCapable;
import com.leliven.park.domain.parking.unconscious.model.UnconsciousUnifiedOrderContext;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 无感统一下单处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnconsciousUnifiedOrderProcessor
    extends AbstractTempParkingUnifiedOrderProcessor<UnconsciousUnifiedOrderContext> {

    public UnconsciousUnifiedOrderProcessor(
        UnifiedOrderCapable<UnconsciousUnifiedOrderContext> compositeUnconsciousProcessor) {
        super(compositeUnconsciousProcessor);
    }

    @Override
    protected boolean beforeUnifiedOrder(UnconsciousUnifiedOrderContext context) {
        List<ParklotPayMerchant> merchants = this.getMerchantManager(context.getParkingTodo().getParklotId())
            .getAssignedUnconsciousMerchants(context.getAssignedPayChannels());
        context.setUnconsciousMerchants(merchants);
        return !merchants.isEmpty();
    }

    /**
     * 处理下单流程
     *
     * <p>
     * 重写方法，变更下单流程
     * </p>
     *
     * @param context 上下文
     * @return 支付结果
     */
    @Override
    protected R<UnifiedOrderResponse> processUnifiedOrder(UnconsciousUnifiedOrderContext context) {
        for (ParklotPayMerchant merchant : context.getUnconsciousMerchants()) {
            createOrUpdateUnifiedOrder(context, merchant);
            R<UnifiedOrderResponse> result = super.doUnifiedOrder(context);
            // 无感下单成功，则代表支付成功，只要其中一个渠道支付成功，则返回结果
            if (resolveUnifiedOrderResponse(result)) {
                // 根据支付结果去处理后续业务
                processUnifiedOrderResponse(result.getData(), context);
                return result;
            }
        }
        return R.fail("无感支付失败, 无扣费成功的支付渠道商户");
    }

    /**
     * 创建统一下单
     *
     * <p>
     * 重写方法，增加无感支付订单扩展信息
     * </p>
     *
     * @param context 上下文
     * @return 统一下单
     */
    @Override
    protected UnifiedOrder createUnifiedOrder(UnconsciousUnifiedOrderContext context) {
        TempParkingUnifiedOrderAdapter unifiedOrder = (TempParkingUnifiedOrderAdapter) super.createUnifiedOrder(context);
        unifiedOrder.extendedInfo(extend -> {
            ParkingTodo parkingTodo = context.getParkingTodo();
            extend.setVehicle(parkingTodo.getParkingInfo().getVehicle());
            extend.setEnterTime(resolveEnterDatetime(parkingTodo));
            extend.setExitTime(parkingTodo.getParkingInfo().getDatetime());
            extend.setParkingOrderId(parkingTodo.getParkingOrderId());
        });
        return unifiedOrder;
    }

    /**
     * 创建或更新统一订单
     *
     * <p>
     * 如果上下文中不存在统一订单，则创建统一订单
     * 否则重新生成交易流水号，切换支付商户
     * </p>
     *
     * @param context  上下文 {@link UnconsciousUnifiedOrderContext}
     * @param merchant 商户 {@link ParklotPayMerchant}
     */
    private void createOrUpdateUnifiedOrder(UnconsciousUnifiedOrderContext context, ParklotPayMerchant merchant) {
        if (Objects.isNull(context.getUnifiedOrder())) {
            context.setPayMerchant(merchant);
            context.setUnifiedOrder(createUnifiedOrder(context));
            return;
        }

        context.regenerateTradeNo();
        context.switchPayMerchant(merchant);
    }

    /**
     * 解析统一下单结果
     *
     * @param result 无感支付结果
     * @return 是否支付成功 {@code true} 成功 {@code false} 失败
     */
    private boolean resolveUnifiedOrderResponse(R<UnifiedOrderResponse> result) {
        UnifiedOrderResponse response = result.getData();
        if (!result.isSuccess() || Objects.isNull(response)) {
            if (log.isDebugEnabled()) {
                log.debug("无感支付失败, result: {}", Func.toJson(result));
            }
            return false;
        }

        return response.isSuccess();
    }

    /**
     * 处理统一下单响应
     *
     * @param response 统一下单响应
     * @param context  上下文
     */
    private void processUnifiedOrderResponse(UnifiedOrderResponse response, UnconsciousUnifiedOrderContext context) {
		if(null == response.getPayTime()){
			response.setPayTime(new Date());
		}
        // 仅保存支付成功的临停订单，支付状态不直接设置为成功，避免回调幂等判断不通过，
        TempParkingUnifiedOrderAdapter unifiedOrderAdapter = (TempParkingUnifiedOrderAdapter) context.getUnifiedOrder();
        TempParkingOrderDomain tempParkingOrder = unifiedOrderAdapter.getRawOrder();
        tempParkingOrder.setPayTime(response.getPayTime());
        this.tempParkingOrderCreator.save(tempParkingOrder);
        // 通过发布支付成功领域事件，去处理具体的逻辑
        this.publishPaymentSuccessEvent(response);
    }

    /**
     * 发布支付成功领域事件
     *
     * @param response 统一下单响应（支付结果）
     * @see TempParkingOrderPaySuccessEvent
     */
    private void publishPaymentSuccessEvent(UnifiedOrderResponse response) {
        TempParkingOrderPaySuccessEvent event = new TempParkingOrderPaySuccessEvent(
            this,
            PaymentSuccessPayload.builder()
                .tradeNo(response.getOutTradeNo())
                .thirdTradeNo("")
                .dateTime(response.getPayTime())
                .build()
        );
        SpringDomainEventPublisher.publish(event);
    }

    /**
     * 解析入场时间
     *
     * <p>
     * 如果存在最后支付时间，则说明该订单已经支付过，以最后支付时间为准为本次费用产生的入场时间
     * </p>
     *
     * @param parkingTodo 停车待办
     * @return 入场时间
     */
    private Date resolveEnterDatetime(ParkingTodo parkingTodo) {
        if (Objects.nonNull(parkingTodo.getLastPayTime())) {
            return parkingTodo.getLastPayTime();
        }

        return parkingTodo.getEnterTime();
    }

}
