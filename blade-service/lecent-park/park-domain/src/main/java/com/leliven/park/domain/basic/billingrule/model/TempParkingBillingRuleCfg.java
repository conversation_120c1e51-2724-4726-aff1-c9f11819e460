package com.leliven.park.domain.basic.billingrule.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.leliven.park.domain.basic.billingrule.model.valueobject.TempParkingChargeType;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.DateUtil;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 临停收费规则领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TempParkingBillingRuleCfg extends TenantDomain {

	/**
	 * 收费规则名称
	 */
	private String name;
	/**
	 * 备注，描述
	 */
	private String memo;
	/**
	 * 所属车场id
	 */
	private Long parklotId;

	/**
	 * 计费类型
	 * @see TempParkingChargeType
	 */
	private TempParkingChargeType chargeType;

	/**
	 * 超过免费时长时计费是否包含免费时长
	 */
	private boolean includeFreeTime;
	/**
	 * 免费时长 单位:分钟
	 */
	private Duration freeDuration;
	/**
	 * 提前支付免费离场时长 单位：分
	 */
	private Duration freeLeaveDurationAfterPaid;


	/**
	 * 入场后的前chargePreTime分钟按 chargePreFee元/chargePreMinute分钟
	 */
	private Integer chargePreTime;
	/**
	 * 入场后的前chargePreTime分钟按 chargePreFee元/chargePreMinute分钟 计算
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal chargePreFee;
	/**
	 * 入场后的前chargePreTime分钟按 chargePreFee元/chargePreMinute分钟 计算
	 */
	private Integer chargePreMinute;
	/**
	 * 入场后的前preTopTime分钟,封顶收费preTopFee元
	 */
	private Integer preTopTime;
	/**
	 * 入场后的前preTopTime分钟,封顶收费preTopFee元
	 */
	private BigDecimal preTopFee;

	/**
	 * 每perNHour小时封顶收费perNHourCappingFee元
	 *
	 * <p>
	 * 按停车时长分段计费 {@link TempParkingChargeType#SEGMENTED_PARKING_DURATION} 类型时有效
	 * </p>
	 */
	private Integer perNHour;
	/**
	 * 每perNHour小时封顶收费perNHourCappingFee元
	 *
	 * <p>
	 * 按停车时长分段计费 {@link TempParkingChargeType#SEGMENTED_PARKING_DURATION} 类型时有效
	 * </p>
	 */
	private BigDecimal perNHourCappingFee;
	/**
	 * 每perNHour小时；前0至n分钟固定收费m元
	 *
	 * <p>
	 * 按停车时长分段计费 {@link TempParkingChargeType#SEGMENTED_PARKING_DURATION} 类型时有效
	 * </p>
	 */
	private Integer perFirstTime;
	/**
	 * 每perNHour小时；前0至n分钟固定收费m元
	 *
	 * <p>
	 * 按停车时长分段计费 {@link TempParkingChargeType#SEGMENTED_PARKING_DURATION} 类型时有效
	 * </p>
	 */
	private BigDecimal perFixedFree;

	/**
	 * 分段配置列表，配置为以下类型时可用
	 *
	 * <ul>
	 * <li>{@link TempParkingChargeType#SEGMENTED_24_HOUR} 24小时分段</li>
	 * <li>{@link TempParkingChargeType#SEGMENTED_PARKING_DURATION} 按停车时长分段计费</li>
	 * </ul>
	 */
	private List<TempParkingBillingSegmentCfg> segmentConfigs;

	/**
	 * 构造函数
	 */
	public TempParkingBillingRuleCfg() {
		this.segmentConfigs = new ArrayList<>();
	}

	/**
	 * 计算开始收费时间
	 *
	 * @param enterTime 入场时间
	 * @return 开始收费时间
	 */
	public Date calculateStartBillingDate(Date enterTime) {
		return DateUtil.plus(enterTime, freeDuration);
	}

	/**
	 * 计算免费离场时间
	 *
	 * @param payTime 支付时间
	 * @return 免费离场时间
	 */
	public Date calculateFreeLeaveDateAfterPaid(Date payTime) {
		return DateUtil.plus(payTime, freeLeaveDurationAfterPaid);
	}

	/**
	 * 添加分段配置
	 *
	 * @param segmentConfig 分段配置 {@link TempParkingBillingSegmentCfg}
	 */
	public void addSegmentConfig(TempParkingBillingSegmentCfg segmentConfig) {
		segmentConfigs.add(segmentConfig);
	}

	/**
	 * 判断指定时间是否处于免费分段中
	 *
	 * @param dateTime 指定时间
	 * @return 是否处于免费分段中 {@code true} 处于免费分段中 {@code false} 不处于免费分段中
	 */
	public boolean isWithinFreeSegment(LocalDateTime dateTime) {
		LocalTime time = dateTime.toLocalTime();
		return segmentConfigs.stream()
			.anyMatch(segment -> segment.isWithinFreeSegment(time));
	}

	/**
	 * 查询所有免费分段配置
	 *
	 * @return 免费分段配置列表 {@link List}<{@link TempParkingBillingSegmentCfg}>
	 */
	public List<TempParkingBillingSegmentCfg> getFreeSegmentConfigs() {
		return segmentConfigs.stream()
			.filter(TempParkingBillingSegmentCfg::isFreeSegment)
			.collect(java.util.stream.Collectors.toList());
	}

}
