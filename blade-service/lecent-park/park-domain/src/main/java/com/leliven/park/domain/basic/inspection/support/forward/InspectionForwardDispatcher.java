package com.leliven.park.domain.basic.inspection.support.forward;

import com.leliven.park.domain.basic.inspection.model.InspectionForwardContext;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionPhenomenonForwardRule;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springblade.core.log.exception.ServiceException;

/**
 * 巡检转发分发器实现
 * 负责按目标类型分组并分发给对应的处理器
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
public class InspectionForwardDispatcher {

    private final Map<InspectionPhenomenonForwardRule.TargetType, InspectionForwardHandler> forwardHandlerMap;


    public InspectionForwardDispatcher(List<InspectionForwardHandler> forwardHandlers) {
        this.forwardHandlerMap = forwardHandlers.stream()
            .collect(Collectors.toMap(InspectionForwardHandler::getTargetType, Function.identity()));
    }

    /**
     * 分发转发任务
     *
     * @param forwardContexts 转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     */
    public void dispatch(List<InspectionForwardContext> forwardContexts) {
        // 按目标类型分组
        Map<InspectionPhenomenonForwardRule.TargetType, List<InspectionForwardContext>> typeGroups = forwardContexts
            .stream()
            .collect(Collectors.groupingBy(InspectionForwardContext::getTargetType));

        // 分发给对应的处理器
        for (Map.Entry<InspectionPhenomenonForwardRule.TargetType, List<InspectionForwardContext>> entry : typeGroups.entrySet()) {
            dispatchToHandler(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 分发给指定类型的处理器
     *
     * @param targetType 目标类型 {@link InspectionPhenomenonForwardRule.TargetType}
     * @param contexts 转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     */
    private void dispatchToHandler(InspectionPhenomenonForwardRule.TargetType targetType,
                                   List<InspectionForwardContext> contexts) {
        log.info("开始处理目标类型: {}, 数量: {}", targetType, contexts.size());

        findHandler(targetType).handle(contexts);
    }


    /**
     * 根据转发目标类型查找转发处理器，如果未找到，则抛出异常
     *
     * @param targetType 目标类型 {@link InspectionPhenomenonForwardRule.TargetType}
     * @return 转发处理器 {@link InspectionForwardHandler}
     */
    private InspectionForwardHandler findHandler(InspectionPhenomenonForwardRule.TargetType targetType) {
        InspectionForwardHandler handler = forwardHandlerMap.get(targetType);
        if (Objects.isNull(handler)) {
            throw new ServiceException("未查找到类型为[" + targetType + "]的转发处理器");
        }

        return handler;
    }

}
