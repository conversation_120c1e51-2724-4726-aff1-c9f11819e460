package com.leliven.park.domain.basic.device.model.valueobject;

import com.leliven.park.common.model.valueobject.DeviceIdentifier;
import com.leliven.ddd.core.model.Identifier;

/**
 * 设备唯一标识
 *
 * <AUTHOR>
 * @since 2024/8/27
 */
public class DeviceId implements DeviceIdentifier, Identifier<Long> {

    private final Long uniqueIdentifier;

    public DeviceId(Long uniqueIdentifier) {
        this.uniqueIdentifier = uniqueIdentifier;
    }

    public static DeviceId of(Long uniqueIdentifier) {
        return new DeviceId(uniqueIdentifier);
    }

    @Override
    public Long getValue() {
        return uniqueIdentifier;
    }
}
