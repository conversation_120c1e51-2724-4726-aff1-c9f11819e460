package com.leliven.park.domain.basic.inspection.model;

import com.leliven.ddd.core.model.TenantDomain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡检任务项领域对象
 * 代表一个计划内的、需要执行的具体巡检项
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class InspectionTaskItem extends TenantDomain {

    /**
     * 巡检任务ID
     */
    private Long taskId;

    /**
     * 巡检子任务ID
     */
    private Long subTaskId;

    /**
     * 关联的基础巡检项ID
     */
    private Long itemId;

    /**
     * 构造函数
     *
     * @param taskId   巡检任务ID
     * @param tenantId 租户ID
     */
    public InspectionTaskItem(Long taskId, String tenantId) {
        this.taskId = taskId;
        this.tenantId = tenantId;
    }

    /**
     * 创建巡检任务项 (基于子任务和基础巡检项)
     *
     * @param subTask 巡检子任务 {@link InspectionSubTask}
     * @param item    基础巡检项 {@link InspectionItem}
     * @return 巡检任务项 {@link InspectionTaskItem}
     */
    public static InspectionTaskItem create(InspectionSubTask subTask, InspectionItem item) {
        InspectionTaskItem taskItem = new InspectionTaskItem(subTask.getTaskId(), subTask.getTenantId());
        taskItem.setSubTaskId(subTask.getId());
        taskItem.setItemId(item.getId());
        // 不再设置初始状态
        return taskItem;
    }

    /**
     * 创建巡检任务项列表
     *
     * @param subTask 巡检子任务 {@link InspectionSubTask}
     * @param items   基础巡检项列表 {@link InspectionItem}
     * @return 巡检任务项列表 {@link List<InspectionTaskItem>}
     */
    public static List<InspectionTaskItem> createTaskItems(InspectionSubTask subTask, List<InspectionItem> items) {
        return items.stream()
            .map(item -> create(subTask, item))
            .collect(Collectors.toList());
    }

}
