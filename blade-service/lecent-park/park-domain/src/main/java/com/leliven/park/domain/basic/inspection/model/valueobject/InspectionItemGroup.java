package com.leliven.park.domain.basic.inspection.model.valueobject;

import com.leliven.park.domain.basic.inspection.model.InspectionItem;
import com.leliven.ddd.core.annotations.ValueObject;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.stream.Collectors;

import org.springblade.common.utils.ObjectValidator;

/**
 * 根据 objectType 分组的巡检项
 *
 * <AUTHOR>
 */
@ValueObject
public class InspectionItemGroup {

    /**
     * 关联对象类型与巡检项列表的映射
     */
    private Map<InspectionObjectType, List<InspectionItem>> itemsMap;

    /**
     * 私有构造函数，用于内部使用
     */
    private InspectionItemGroup(Map<InspectionObjectType, List<InspectionItem>> itemsMap) {
        this.itemsMap = itemsMap;
    }

    /**
     * 初始化方法，根据传入的巡检项列表构建 itemsMap
     *
     * @param items 巡检项列表
     * @return 初始化后的 InspectionItemGroup 对象
     */
    public static InspectionItemGroup fromItems(List<InspectionItem> items) {
        return new InspectionItemGroup(
            Optional.ofNullable(items).filter(t -> !t.isEmpty())
                .map(t -> t.stream().collect(Collectors.groupingBy(InspectionItem::getObjectType)))
                .orElseGet(() -> new EnumMap<>(InspectionObjectType.class)));
    }

    /**
     * 根据关联对象类型获取巡检项列表
     *
     * @param objectType 关联对象类型 {@link InspectionObjectType}
     * @return 巡检项列表 {@link List<InspectionItem>}
     */
    public List<InspectionItem> getItems(InspectionObjectType objectType) {
        return itemsMap.getOrDefault(objectType, new ArrayList<>());
    }

    /**
     * 获取所有巡检项
     *
     * @return 所有巡检项列表 {@link List<InspectionItem>}
     */
    public List<InspectionItem> getAllItems() {
        return itemsMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 遍历 itemsMap
     *
     * @param consumer 遍历函数
     */
    public void forEach(BiConsumer<InspectionObjectType, List<InspectionItem>> consumer) {
        ObjectValidator.requireNonNull(consumer, "consumer is null");

        itemsMap.forEach(consumer);
    }

    /**
     * 检查是否包含指定的对象类型
     *
     * @param objectType 关联对象类型 {@link InspectionObjectType}
     * @return 是否包含该对象类型的巡检项
     */
    public boolean containsObjectType(InspectionObjectType objectType) {
        return itemsMap.containsKey(objectType) && !itemsMap.get(objectType).isEmpty();
    }
}
