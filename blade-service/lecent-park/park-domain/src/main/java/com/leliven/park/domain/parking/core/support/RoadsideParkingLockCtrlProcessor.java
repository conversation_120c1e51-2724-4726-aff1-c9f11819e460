package com.leliven.park.domain.parking.core.support;

import com.leliven.park.domain.basic.billingrule.TempParkingBillingRuleCfgGateway;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.basic.device.model.ParkingDeviceCommand;
import com.leliven.park.domain.basic.device.support.ParkingPlaceLockDeviceCapable;
import com.leliven.park.domain.basic.place.model.ParkingPlaceDeviceManager;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.model.valueobject.BasicLockCtrlParam;
import com.leliven.park.domain.basic.place.model.valueobject.LockUpCtrlParam;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceDevice;
import com.leliven.park.domain.parking.core.specification.LockCtrlSpecification;
import com.leliven.park.domain.basic.place.support.ParkingDeviceManagementContext;
import com.leliven.park.domain.parking.core.event.ParkingLockCtrlDomainEvent;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingLockCtrlExecuteResult;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingLockCtrlType;
import com.leliven.park.domain.parking.core.model.objectvalue.RoadsideParkingLockCtrlSceneParam;
import com.leliven.park.domain.parking.core.model.objectvalue.UnlockWithDelayedLockSceneParam;
import com.leliven.park.domain.parking.core.support.factory.ParkingLockCtrlSpecFactory;
import com.leliven.park.domain.parking.core.support.publisher.RoadsideParkingLockCtrlPublisher;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.exception.NotFoundException;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.scheduling.annotation.Async;

import java.time.Duration;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.lecent.park.common.constant.ParkLockNameConstant.LOCK_NAME;

/**
 * 路边停车锁控制处理器
 *
 * <AUTHOR>
 * @since 2024/9/11
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class RoadsideParkingLockCtrlProcessor {

	public static final String LOCK_CTRL_PARAM_KEY = LOCK_NAME + ".place:device:lock:ctrl:";

	private final RedisLockClient redisLockClient;
    private final ParkingLockCtrlSpecFactory lockCtrlSpecFactory;
    private final ParkingPlaceLockDeviceCapable lockDeviceCapable;
    private final RoadsideParkingLockCtrlPublisher lockCtrlPublisher;
	private final ParkingDeviceManagementContext deviceManagementContext;
    private final TempParkingBillingRuleCfgGateway tempParkingBillingRuleCfgGateway;


	/**
	 * 升锁
	 *
	 * @param param 路边停车位锁控制场景参数
	 * @return
	 */
	public ParkingLockCtrlExecuteResult lock(RoadsideParkingLockCtrlSceneParam param) {
		return this.invoke(param, ParkingLockCtrlType.LOCK);
	}

	/**
	 * 异步升锁
	 *
	 * @param param 路边停车位锁控制场景参数
	 */
	@Async
	public void asyncLock(RoadsideParkingLockCtrlSceneParam param) {
        try {
            this.lock(param);
        } catch (Exception e) {
            log.error("异步升锁异常, 车位[id={}], 场景[{}]", param.getParkingPlaceId(), param.getScene().getValue(), e);
        }
    }

	/**
	 * 降锁
	 *
	 * @param param 路边停车位锁控制场景参数
	 * @return
	 */
	public ParkingLockCtrlExecuteResult unlock(RoadsideParkingLockCtrlSceneParam param) {
		return this.invoke(param, ParkingLockCtrlType.UNLOCK);
	}

	/**
	 * 异步降锁
	 *
	 * @param param 路边停车位锁控制场景参数
	 */
	@Async
	public void asyncUnlock(RoadsideParkingLockCtrlSceneParam param) {
		try {
           this.unlock(param);
        } catch (Exception e) {
            log.error("异步降锁异常, 车位[id={}], 场景[{}]", param.getParkingPlaceId(), param.getScene().getValue(), e);
        }
	}

	/**
     * 降锁并延迟升锁
     *
	 * @param param 路边停车位锁控制场景参数
     */
	@Async
    public void asyncUnlockWithDelayedLock(UnlockWithDelayedLockSceneParam param) {
		invoke(param.getUnlockSceneParam(), ParkingLockCtrlType.UNLOCK, delayedLockConsumer(param));
    }

	/**
     * 延迟升锁（暂时默认一个场景，后续有需要再扩展）
     *
     * <p>
     * 延迟时长：默认为临停计费规则中的缴费后免费时长
     * </p>
     *
	 * @param param 路边停车位锁控制场景参数
     */
    private Consumer<ParkingPlaceDevice> delayedLockConsumer(UnlockWithDelayedLockSceneParam param) {
        return lockDevice -> {
			TempParkingBillingRuleCfg chargeRule = getTempParkingChargeRule(lockDevice.getParkingSpace().getParklotId());

			Duration delayDuration = Optional.ofNullable(param.getDatetimeBeforeDelay())
				.map(datetimeBeforeDelay -> DateUtil.between(chargeRule.calculateFreeLeaveDateAfterPaid(datetimeBeforeDelay), new Date()))
				.orElseGet(chargeRule::getFreeLeaveDurationAfterPaid);

			if (delayDuration.isNegative()) {
				log.warn("车位[{}]延迟时长[{}s]异常，放弃下发延迟升锁指令。",
					lockDevice.getParkingSpace().getPayCode(), delayDuration.getSeconds());
				return;
			}

			lockCtrlPublisher.publishLockDelayedEvent(param.createLockUpDelayedEvent(), delayDuration);
		};
    }

	/**
	 * 执行锁控制命令
	 *
	 * @return
	 */
	private ParkingLockCtrlExecuteResult invoke(RoadsideParkingLockCtrlSceneParam param, ParkingLockCtrlType lockCtrlType) {
		return this.invoke(param, lockCtrlType, null);
	}

	private ParkingLockCtrlExecuteResult invoke(RoadsideParkingLockCtrlSceneParam param,
						ParkingLockCtrlType lockCtrlType, Consumer<ParkingPlaceDevice> afterCustomer) {
		ObjectValidator.requireNonNull(param, "Param must not be null");
		ObjectValidator.requireNonNull(lockCtrlType, "LockCtrlType must not be null");

		ParkingPlaceDeviceManager deviceManager = deviceManagementContext.asDeviceManager(param.getParkingPlaceId());
		return deviceManager.getLockDeviceWithParkingPlace()
			.map(lockDevice -> invoke(lockDevice, param, lockCtrlType, afterCustomer))
			.orElseGet(() -> ParkingLockCtrlExecuteResult.fail("车位未绑定锁设备"));
	}

	private ParkingLockCtrlExecuteResult invoke(ParkingPlaceDevice lockDevice,
												RoadsideParkingLockCtrlSceneParam param,
												ParkingLockCtrlType lockCtrlType,
												Consumer<ParkingPlaceDevice> afterCustomer) {

		String distributedLockKey = createDistributedLockKey(lockDevice.getParkingSpace().getId());

			try {
				// 尝试获取分布式锁
				this.tryLock(distributedLockKey);
				// 检查车位是否满足锁控制条件
				if (!getLockCtrlSpec(param, lockCtrlType).isSatisfiedBy(lockDevice.getParkingSpace())) {
					String warnMsg = String.format("车位[%s]在场景[%s]下的锁控制条件未满足，放弃下发锁控制指令[%s]",
						lockDevice.getParkingSpace().getPayCode(), param.getScene().getDescription(), lockCtrlType.getDesc());
					log.warn(warnMsg);
					return ParkingLockCtrlExecuteResult.fail(warnMsg);
				}
				// 调用设备能力接口
				R<String> invokeResult = invokeDeviceCapability(lockDevice, lockCtrlType, param);

				if (invokeResult.isSuccess()) {
					// 执行后续操作
					Optional.ofNullable(afterCustomer).ifPresent(customer -> customer.accept(lockDevice));
				}

				ParkingLockCtrlExecuteResult executeResult = ParkingLockCtrlExecuteResult.builder()
					.parkingPlaceDevice(lockDevice)
					.ctrlType(lockCtrlType)
					.sceneParam(param)
					.success(invokeResult.isSuccess())
					.message(invokeResult.getMsg())
					.build();

				SpringDomainEventPublisher.publish(ParkingLockCtrlDomainEvent.create(this, executeResult));

				return executeResult;

			} catch (InterruptedException e) {
				log.warn("发送设备命令异常: ", e);
				Thread.currentThread().interrupt();
				throw new ServiceException("发送设备命令异常, 请稍后再试");
			} finally {
				this.redisLockClient.unLock(distributedLockKey, LockType.FAIR);
			}
	}

	/**
	 * 获取锁设备控制规约
	 *
	 * @param param 路边停车位锁控制场景参数
	 * @param lockCtrlType 锁控制类型
	 * @return 锁控制规约
	 */
	private LockCtrlSpecification getLockCtrlSpec(RoadsideParkingLockCtrlSceneParam param, ParkingLockCtrlType lockCtrlType) {
		switch (lockCtrlType) {
			case LOCK:
				return lockCtrlSpecFactory.getLockSpec(param);
			case UNLOCK:
				return lockCtrlSpecFactory.getUnlockSpec(param);
			default:
				throw new ServiceException("不支持的锁控制类型");
		}
	}

	/**
	 * 调用锁设备能力接口
	 *
	 * @param lockDevice   锁设备
	 * @param lockCtrlType 锁控制类型
	 * @param param        锁控制场景参数
	 * @return
	 */
	private R<String> invokeDeviceCapability(ParkingPlaceDevice lockDevice,
											 ParkingLockCtrlType lockCtrlType,
											 RoadsideParkingLockCtrlSceneParam param) {
		switch (lockCtrlType) {
			case LOCK:
				return lockDeviceCapable.lock(lockDevice, lockCommand(lockDevice, param));
			case UNLOCK:
				return lockDeviceCapable.unlock(lockDevice, unlockCommand(param));
			default:
				throw new ServiceException("不支持的锁控制类型");
		}
	}

	private Consumer<ParkingDeviceCommand<LockUpCtrlParam>> lockCommand(ParkingPlaceDevice lockDevice,
																		RoadsideParkingLockCtrlSceneParam param) {
		return command -> command.requestId(param.getRequestId())
			.source(param.getScene())
			.param(new LockUpCtrlParam(getTurnBack(lockDevice.getParkingSpace())));
	}

	private Consumer<ParkingDeviceCommand<BasicLockCtrlParam>> unlockCommand(RoadsideParkingLockCtrlSceneParam param) {
		return command -> command.requestId(param.getRequestId())
			.source(param.getScene())
			.param(BasicLockCtrlParam.DEFAULT);
	}

	/**
	 * 获取锁参数-回弹距离
	 *
	 * @param parkingPlace 停车位
	 * @return 回弹距离
	 */
	private Integer getTurnBack(ParkingSpace parkingPlace) {
		return null;
	}

	/**
	 * 尝试获取分布式锁
	 *
	 * <p>
	 * 保证同一时间只可以操作同一个停车位的锁设备
	 * </p>
	 *
	 * @param distributedLockKey 分布式锁键
	 * @throws InterruptedException 线程中断异常
	 */
	private void tryLock(String distributedLockKey) throws InterruptedException {
		boolean isLocked = this.redisLockClient.tryLock(distributedLockKey, LockType.FAIR, 5L, 5L, TimeUnit.SECONDS);
		if (!isLocked) {
			throw new ServiceException("设备正在处理其他命令中, 请稍后再试");
		}
	}

	/**
	 * 获取分布式锁键
	 *
	 * @param parkingPlaceId 停车位ID
	 * @return 分布式锁键
	 */
	private String createDistributedLockKey(Long parkingPlaceId) {
		return LOCK_CTRL_PARAM_KEY + parkingPlaceId;
	}

    /**
     * 根据车位获取所属车场的临停收费规则
     *
     * @param parklotId 车场ID
     * @return 临停计费规则 {@link TempParkingBillingRuleCfg}
     */
    private TempParkingBillingRuleCfg getTempParkingChargeRule(Long parklotId) {
        // 暂时使用小型车收费规则
        return this.tempParkingBillingRuleCfgGateway.asCacheSupplier()
			.getDefaultByParklotId(parklotId)
            .orElseThrow(() -> new NotFoundException("未找到停车场默认收费规则，停车场ID：" + parklotId));
    }

}
