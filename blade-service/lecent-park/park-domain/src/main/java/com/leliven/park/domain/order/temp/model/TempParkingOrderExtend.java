package com.leliven.park.domain.order.temp.model;

import com.lecent.pay.core.enums.PayScene;
import com.leliven.park.domain.basic.payway.entity.ParklotPayMerchant;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 临停订单扩展领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TempParkingOrderExtend extends TenantDomain {

    /**
	 * 商品描述
	 */
	private String description;
	/**
	 * 附加信息
	 */
	private String attach;
	/**
	 * 过期时长 (秒)
	 */
	private Long expireDuration;
    /**
     * 支付场景
     */
    private PayScene payScene;
    /**
     * 收款商户
     */
    private ParklotPayMerchant receivingMerchant;
}
