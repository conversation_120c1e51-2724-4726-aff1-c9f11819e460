package com.leliven.park.domain.basic.parklot.model;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.lecent.park.core.log.annottion.LogName;
import com.lecent.park.en.parklot.ParkLotType;
import com.leliven.park.domain.basic.parklot.model.valueobject.*;
import com.leliven.ddd.core.model.TenantDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.StringPool;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 车场领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ParklotDomain extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 车场编号
	 */
	private String no;
	/**
	 * 名称
	 */
	@LogName("名称")
	private String name;
	/**
	 * 全称
	 */
	@LogName("车场名全称")
	private String fullName;
	/**
	 * 车场名称拼音全拼
	 */
	private String namePinyinFull;
	/**
	 * 车场名称拼音全拼
	 */
	private String namePinyinInitial;
	/**
	 * 车场图片
	 */
	@ApiModelProperty(value = "车场图片")
	private String imgUrl;
	/**
	 * 车场类型
	 */
	@ApiModelProperty(value = "车场类型（0-路外室内 1-路外室外 2-路内）")
	private ParkLotType type;
	/**
	 * 备注，描述
	 */
	@LogName("备注")
	private String memo;
	/**
	 * 经度
	 */
	@LogName("经度")
	private BigDecimal lng;
	/**
	 * 纬度
	 */
	@LogName("纬度")
	private BigDecimal lat;
	/**
	 * 区域编码
	 */
	@LogName("区域编码")
	private String regionCode;
	/**
	 * 区域地址
	 */
	@LogName("区域地址")
	private String regionAddress;
	/**
	 * 地址
	 */
	@LogName("详细地址")
	private String address;
	/**
	 * 创建部门祖籍
	 */
	private String createDeptAncestors;
	/**
	 * 车场父id（场中场使用）
	 */
	private Long parentId;
	/**
	 * 商户ID
	 */
	private Long merchantId;
	/**
	 * 是否嵌套车场(场中场)
	 */
	private boolean isNest;
	/**
	 * 临停收费规则设置
	 */
	private ParklotTempParkingChargeRuleSetting tempParkingChargeRuleSetting;
	/**
	 * 车场容量信息
	 */
	private ParklotCapacity capacity;
	/**
	 * 车场规则
	 */
	private ParklotOperationRules operationRules;
	/**
	 * 通用配置
	 */
	private ParklotUniversalConfig universalConfig;
	/**
	 * 车场租赁卡规则
	 */
	private ParklotRentalCardRule rentalCardRule;
	/**
	 * 车场小程序配置
	 */
	private ParklotMiniAppsConfig miniAppsConfig;

	public ParklotDomain(Long id) {
		this.id = id;
	}

	public void create(String no) {
		this.no = no;
		this.updateNamePinYin();
		this.operationRules = ParklotOperationRules.create();
	}

	public void update(ParklotDomain oldParklot) {
		this.validateOperationRules();
		this.validateRentalCardRule();
		this.updateNamePinYinIfDiff(oldParklot.getFullName());
	}

	public void updateTempParkingChargeRule(ParklotTempParkingChargeRuleSetting tempParkingChargeRuleSetting) {
		this.tempParkingChargeRuleSetting = tempParkingChargeRuleSetting;
	}

	/**
	 * 根据入参对比全名是否一致，
	 *
	 * @param otherFullName 对比的全名
	 */
	public boolean isDiffFullName(String otherFullName) {
		return !this.fullName.equals(otherFullName);
	}

	/**
	 * 根据入参对比全名是否一致，
	 * 不一致则更新拼音
	 *
	 * @param otherFullName 对比的全名
	 */
	public void updateNamePinYinIfDiff(String otherFullName) {
		if (isDiffFullName(otherFullName)) {
			updateNamePinYin();
		}
	}

	/**
	 * 更新名称拼音
	 * 包含全拼，简拼
	 */
	public void updateNamePinYin() {
		this.namePinyinFull = PinyinUtil.getPinyin(this.fullName, StringPool.EMPTY);
		this.namePinyinInitial = PinyinUtil.getFirstLetter(this.fullName, StringPool.EMPTY);
	}

	/**
	 * 校验车场运营规则
	 */
	public void validateOperationRules() {
		Optional.ofNullable(this.operationRules)
			.ifPresent(ParklotOperationRules::validateTempParkingRestrictedTime);
	}

	/**
	 * 校验车场租赁卡规则
	 */
	public void validateRentalCardRule() {
		Optional.ofNullable(this.rentalCardRule)
			.ifPresent(ParklotRentalCardRule::validateChangeRule);
	}

	/**
	 * 是否为路边车场
	 *
	 * @return {@code true} 路边车场 {@code false} 非路边车场
	 */
	public boolean isRoadside() {
		return type.isRoadside();
	}

	public boolean supportReceiptPrint() {
		return Optional.ofNullable(this.miniAppsConfig).map(ParklotMiniAppsConfig::getScanMimiId).isPresent() &&
		Optional.ofNullable(this.universalConfig).map(ParklotUniversalConfig::getReceiptPrintConfigId).isPresent();
	}

}
