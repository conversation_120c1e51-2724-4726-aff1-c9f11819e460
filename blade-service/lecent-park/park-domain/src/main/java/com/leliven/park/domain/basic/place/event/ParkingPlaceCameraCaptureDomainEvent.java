package com.leliven.park.domain.basic.place.event;

import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCapture;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureType;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Objects;

/**
 * 车位摄像头抓拍领域事件
 *
 * <AUTHOR>
 */
@Getter
public class ParkingPlaceCameraCaptureDomainEvent
    extends AbstractDomainEvent<ParkingPlaceCameraCapture, ParkingPlaceCameraCaptureDomainEvent.EventType> {

    private final ParkingPlaceCameraCaptureType captureType;

    /**
     * Create a new DomainEvent.
     *
     * @param source      事件源 (never {@code null})
     * @param eventType   领域事件
     * @param captureType 车位摄像头抓拍类型
     * @param payload     事件载荷 (never {@code null})
     */
    protected ParkingPlaceCameraCaptureDomainEvent(Object source,
                                                   EventType eventType,
                                                   ParkingPlaceCameraCaptureType captureType,
                                                   ParkingPlaceCameraCapture payload) {
        super(source, eventType, payload);
        this.captureType = captureType;
    }

    /**
     * 创建车位摄像头抓拍已创建领域事件
     *
     * @param source      事件源 (never {@code null})
     * @param captureType 车位摄像头抓拍类型
     * @param payload     事件载荷 (never {@code null})
     * @return this {@link ParkingPlaceCameraCaptureDomainEvent}
     */
    public static ParkingPlaceCameraCaptureDomainEvent ofCreated(@Nonnull Object source,
                                                                 @Nonnull ParkingPlaceCameraCaptureType captureType,
                                                                 @Nonnull ParkingPlaceCameraCapture payload) {
        Objects.requireNonNull(captureType, "Param[captureType] must not be null");
        Objects.requireNonNull(payload, "Param[payload] must not be null");
        return new ParkingPlaceCameraCaptureDomainEvent(source, EventType.CREATED, captureType, payload);
    }

    /**
     * 是否为定时抓拍
     *
     * @return {@code true} 定时抓拍
     */
    public boolean isTimedCapture() {
        return ParkingPlaceCameraCaptureType.TIMED.equals(captureType);
    }

    /**
     * 车位摄像头抓拍事件类型
     *
     * <AUTHOR>
     */
    @Getter
    @AllArgsConstructor
    public enum EventType implements DomainEventType {

        CREATED,
        ;
    }
}
