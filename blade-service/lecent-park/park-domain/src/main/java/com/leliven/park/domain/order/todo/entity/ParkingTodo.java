package com.leliven.park.domain.order.todo.entity;

import cn.hutool.core.math.Money;
import com.baomidou.mybatisplus.annotation.TableField;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.en.PassTemplate;
import com.lecent.park.en.channeltodo.ChargeEnum;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.CardInfo;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.common.model.valueobject.MultipleChargeType;
import com.leliven.park.domain.order.parking.entity.AuthCardRelation;
import com.leliven.park.domain.order.parking.entity.ParkingEventInfo;
import com.leliven.park.domain.order.parking.entity.ParkingSchedule;
import com.leliven.park.domain.order.todo.exception.TodoTimeoutException;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.DateUtil;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParkingTodo extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 车场ID
	 */
	private Long parklotId;
	/**
	 * 通道编号
	 */
	private Integer channelNo;
	/**
	 * 通道类型：1，入口；2，出口
	 */
	private Integer channelType;
	/**
	 * 车位ID
	 */
	@TableField(exist = false)
	private Long placeId;
	/**
	 * 停车信息
	 */
	private ParkingEventInfo parkingInfo;
	/**
	 * 停车费用
	 */
	private ParkingCost parkingCost;
	/**
	 * 停车订单id
	 */
	private Long parkingOrderId;
	/**
	 * 月卡信息
	 */
	private CardInfo cardInfo;
	/**
	 * 优惠券ID
	 */
	private Long couponId;
	/**
	 * 月卡起作用
	 */
	private boolean cardTakeEffect;
	/**
	 * 通行类型
	 */
	private PassTemplate passType;
	/**
	 * 代办类型
	 */
	private String type;
	/**
	 * 代办编号
	 */
	private String no;
	/**
	 * 其他数据
	 */
	private ProjectCost data;
	/**
	 * 超时费用
	 */
	private Money overTimeAmount;
	/**
	 * 临停收费规则
	 */
	private Long chargeRuleId;
	/**
	 * 计费类型
	 */
	private ChargeEnum chargeType;
	/**
	 * 异常原因ID
	 */
	private Long reasonId;
	/**
	 * 代办处理信息
	 */
	private ParkingTodoHandleInfo handleInfo;
	/**
	 * 原始数据
	 */
	private String originalData;
	/**
	 * 后车停车记录ID
	 */
	private Long afterCarParkingId;
	/**
	 * 多位多车收费方式
	 */
	private MultipleChargeType multipleChargeType;
	/**
	 * 进场时间（仅当离场且有进场记录）
	 */
	private Date enterTime;
	/**
	 * 进场图片（仅当离场且有进场记录）
	 */
	private String enterImageUrl;
	/**
	 * 停车时长（仅当离场且有进场记录）
	 */
	private String duration;
	/**
	 * 临停标识 0-否 1-是
	 */
	private boolean tempCar;
	/**
	 * 是否占车位
	 */
	private boolean occupied;
	/**
	 * 授权卡关联信息
	 */
	private AuthCardRelation authCardRelation;
	/**
	 * 创建渠道：1，场内缴费，2，通道自助缴费，3，岗亭收费员 4 模拟 5自动扣费
	 */
	private CreateWay createWay;
	/**
	 * 停车目标ID（场中场所有的子车场存同一值）
	 */
	private Long targetParkingId;
	/**
	 * 待办类型描述
	 */
	private String typeDes;
	/**
	 * 停车时段
	 */
	private ParkingSchedule parkingSchedule;
	/**
	 * 车场商户ID
	 */
	private Long merchantId;
	/**
	 * 状态: 0 创建、1已处理、2自主缴费、3自动放行、4过期
	 */
	private Integer status;

	private ParkingTodoEdition edition;
	/**
	 * 是否提前缴费
	 */
	private boolean beforePaid;
	/**
	 * TODO 未使用
	 * 版本
	 */
	private Integer version;

	/**
	 * 车辆类型剩余天数
	 */
	private String days;
	/**
	 * 房间号
	 */
	private String roomNum;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 电话号码
	 */
	private String phone;
	/**
	 * 车位数
	 */
	private Integer placeNum;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 支付类型
	 */
	private Integer payType;

	private String orderUserId;
	/**
	 * 是否走MQ开闸
	 */
	private boolean isMqOpenGate;
	/**
	 * 第二次出场
	 */
	private boolean isSecondExit;
	/**
	 * 是否是循环车
	 */
	private boolean isCycleCar;
	/**
	 * 扩展语音
	 */
	private String extendVoice;
	/**
	 * 临停订单ID
	 */
	private Long tempParkingOrderId;
	/**
	 * 最后一次支付时间
	 */
	private Date lastPayTime;

	public ParkingTodo() {
		this.parklotId = 0L;
		this.channelNo = 0;
		this.channelType = 0;
		this.multipleChargeType = MultipleChargeType.DEFAULT;
		this.status = ParkingTodoStatus.TODO_STATUS_CREATE.getValue();
		this.createWay = CreateWay.INIT;
		this.no = UUID.randomUUID().toString();
		this.overTimeAmount = new Money();
		this.tempCar = true;
		this.parkingCost = ParkingCost.create();
		this.edition = new ParkingTodoEdition();
		this.parkingSchedule = ParkingSchedule.create();
	}

	public void occupiedParking(AuthCardRelation relation) {
		this.occupied = true;
		this.authCardRelation.changeRelationId(relation.getRelationId());
	}

	public void confirmPassage(Long parkingOrderId) {
		Objects.requireNonNull(parkingOrderId, "parkingOrderId must be not null");
		this.setParkingOrderId(parkingOrderId);
		this.setHandleInfo(new ParkingTodoHandleInfo(ParkingTodoHandleStatus.CONFIRM_PASSAGE));
	}

	/**
	 * 是否通过
	 *
	 * @return true 可通过、false 不可通过
	 */
	public boolean isPass() {
		return ParkingTodoStatus.isPassed(this.status);
	}

	/**
	 * 校验代办是否超时
	 *
	 * @param expireMinutes 过期分钟
	 */
	public void verifyTimeout(long expireMinutes) {
		if (isTimeout(expireMinutes)) {
			throw new TodoTimeoutException();
		}
	}

	/**
	 * 验证代办应该是需要支付的，应付金额大于0
	 */
	public void verifyShouldNeedPay() {
		if (!isNeedPay()) {
			throw new ServiceException("没有支付金额，请有费用后再支付！");
		}
	}

	/**
	 * 验证代办应该是未支付状态
	 */
	public void verifyShouldBeUnpaid() {
		if (isPaid()) {
			throw new ServiceException("订单已支付，请勿重复支付...");
		}
	}

	public boolean isTimeout(long expireMinutes) {
		return isTimeout(Duration.ofMinutes(expireMinutes));
	}

	public boolean isTimeout(Duration expireDuration) {
		Objects.requireNonNull(expireDuration);
		Duration between = DateUtil.between(this.createTime, new Date());
		return between.getSeconds() > expireDuration.getSeconds();
	}

	public boolean isNeedPay() {
		return this.parkingCost.isNeedPay();
	}

	public boolean hasUnpaidCost() {
		return this.parkingCost.hasUnpaidCost();
	}

	/**
	 * 待办是否已支付
	 *
	 * @return boolean true-已支付 false-未支付
	 */
	public boolean isPaid() {
		return ParkingTodoStatus.isPaid(this.status);
	}

	/**
	 * 是否前车收费类型
	 * @return true-是 false-否
	 */
	public boolean isFrontVehicleChargeType() {
		return Objects.nonNull(this.multipleChargeType)
			&& this.multipleChargeType == MultipleChargeType.FRONT_VEHICLE_CHARGE;
	}

	/**
	 * 是否后车收费类型
	 * @return true-是 false-否
	 */
	public boolean isLastVehicleChargeType() {
		return Objects.nonNull(this.multipleChargeType)
			&& this.multipleChargeType == MultipleChargeType.LAST_VEHICLE_CHARGE;
	}
}
