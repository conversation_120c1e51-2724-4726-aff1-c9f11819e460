package com.leliven.park.domain.basic.inspection.support;

import com.leliven.park.domain.basic.inspection.model.*;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;

/**
 * 巡检任务生成器工厂服务
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class InspectionTaskGeneratorFactory {

    private final InspectionTaskDataProvider dataProvider;


    /**
     * 创建巡检任务生成器
     *
     * @param plan 巡检计划 {@link InspectionPlan}
     * @return 巡检任务生成器 {@link InspectionTaskGenerator}
     */
	public InspectionTaskGenerator createTaskGenerator(InspectionPlan plan) {
		// 使用生成器创建子任务
		return InspectionTaskGenerator.createFor(plan, dataProvider);
	}

    /**
     * 创建巡检子任务生成器
     *
     * @param task 巡检任务 {@link InspectionTask}
     * @return 巡检子任务生成器 {@link InspectionSubTaskGenerator}
     */
	public InspectionSubTaskGenerator createSubTaskGenerator(InspectionTask task) {
		// 使用生成器创建子任务
		return InspectionSubTaskGenerator.createFor(task, dataProvider);
	}


}
