package com.leliven.park.domain.parking.notice.support.factory;

import com.leliven.park.domain.basic.billingrule.TempParkingBillingRuleCfgGateway;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.basic.parklot.ParklotCacheRepositoryI;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.domain.basic.parklot.exception.NotFoundParklotException;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.parking.notice.model.ParkingBillingNoticeContext;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import org.springblade.common.exception.NotFoundException;

/**
 * 停车计费通知上下文工厂
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class ParkingBillingNoticeContextFactory {

	private final ParkingPlaceGateway parkingPlaceGateway;
	private final ParklotCacheRepositoryI parklotCacheRepository;
	private final TempParkingBillingRuleCfgGateway tempParkingBillingRuleCfgGateway;


	public ParkingBillingNoticeContext create(ParkingOrderAgg parkingOrder) {
		ParklotDomain parklot = parklotCacheRepository.getOneOptById(parkingOrder.getParklotId())
			.orElseThrow(() -> new NotFoundParklotException(parkingOrder.getParklotId()));

		TempParkingBillingRuleCfg tempParkingBillingRuleCfg = tempParkingBillingRuleCfgGateway.asCacheSupplier()
			.getDefaultByParklot(parklot)
			.orElseThrow(() -> new NotFoundException("未查询到临停收费规则：车场ID = " + parklot.getId()));

		ParkingSpace parkingSpace = parkingPlaceGateway.asCacheSupplier().getOrElseThrow(parkingOrder.getPlaceId());

		return new ParkingBillingNoticeContext(parkingOrder)
			.belongToParklot(parklot)
			.belongToParkingSpace(parkingSpace)
			.billingRuleConfig(tempParkingBillingRuleCfg);
	}

}
