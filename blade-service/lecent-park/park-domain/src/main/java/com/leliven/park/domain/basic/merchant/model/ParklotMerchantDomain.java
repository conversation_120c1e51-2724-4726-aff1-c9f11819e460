package com.leliven.park.domain.basic.merchant.model;

import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场商户领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParklotMerchantDomain extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 商户名称
	 */
	private String name;
	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态：0无效、1有效
	 */
	private Integer status;
}
