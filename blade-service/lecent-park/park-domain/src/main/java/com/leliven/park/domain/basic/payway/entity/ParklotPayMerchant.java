package com.leliven.park.domain.basic.payway.entity;

import com.lecent.park.core.log.annottion.LogName;
import com.lecent.pay.core.enums.PayChannel;
import com.leliven.ddd.core.model.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车场支付商户配置领域对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParklotPayMerchant extends BaseDomain {

	private static final long serialVersionUID = 8413131730552218932L;
	/**
	 * 商户ID
	 */
	@LogName("商户ID")
	@ApiModelProperty(value = "商户ID")
	private String merchantId;

	/**
	 * 商户类型列表：0支付宝、1微信支付、
	 * 2建行聚合支付、3工行聚合支付、4云闪付、5农行聚合支付、20ETC支付等
	 * {@link com.lecent.pay.core.enums.PayChannel}
	 */
	@LogName("支付渠道类型")
	@ApiModelProperty(value = "支付渠道类型")
	private PayChannel payChannelType;

	/**
	 * 收款商户号（向支付提供商的申请的商户号）
	 */
	@ApiModelProperty(value = "收款商户号")
	private String merchantNo;
	/**
	 * 商户提供商编码
	 */
	private String merchantProviderCode;

	/**
	 * 商户名称
	 */
	@LogName("商户名称")
	@ApiModelProperty(value = "商户名称")
	private String merchantName;

	/**
	 * 是否支持无感支付
	 */
	@LogName("是否支持无感支付")
	@ApiModelProperty(value = "是否支持无感支付[false-不支持,true-支持]")
	private boolean supportUnconsciousPay;

	@LogName("优先级")
	@ApiModelProperty(value = "优先级")
	private Integer priorityLevel;
}
