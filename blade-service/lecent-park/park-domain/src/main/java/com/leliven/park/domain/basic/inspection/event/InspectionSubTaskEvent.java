package com.leliven.park.domain.basic.inspection.event;

import com.leliven.park.domain.basic.inspection.model.InspectionSubTask;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

import javax.annotation.Nonnull;


public class InspectionSubTaskEvent extends AbstractDomainEvent<InspectionSubTask, InspectionSubTaskEvent.EventType> {

/**
     * Create a new DomainEvent.
     *
     * @param source    the object on which the event initially occurred (never {@code null})
     * @param eventType domain event type
     * @param payload   the payload object (never {@code null})
     */
    protected InspectionSubTaskEvent(Object source, EventType eventType, @Nonnull InspectionSubTask payload) {
        super(source, eventType, payload);
    }

    /**
     * 创建巡检子任务已完成事件
     *
     * @param source  事件源
     * @param subTask 巡检子任务
     * @return 巡检子任务已完成事件
     */
    public static InspectionSubTaskEvent completed(Object source, @Nonnull InspectionSubTask subTask) {
        return new InspectionSubTaskEvent(source, EventType.COMPLETED, subTask);
    }

    /**
     * 判断事件类型是否为已完成事件
     *
     * @return 是否为已完成事件 {@code true} 是 {@code false} 否
     */
    public boolean isCompletedType() {
        return this.getEventType() == EventType.COMPLETED;
    }

    /**
     * 事件类型
     */
    public enum EventType implements DomainEventType {
        COMPLETED,
        ;
    }
}
