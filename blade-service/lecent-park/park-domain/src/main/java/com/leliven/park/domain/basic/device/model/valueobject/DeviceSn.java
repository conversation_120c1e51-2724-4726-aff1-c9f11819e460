package com.leliven.park.domain.basic.device.model.valueobject;

import com.leliven.park.common.model.valueobject.DeviceIdentifier;
import com.leliven.ddd.core.model.Identifier;

/**
 * 设备序列号标识
 *
 * <AUTHOR>
 * @since 2024/8/27
 */
public class DeviceSn implements DeviceIdentifier, Identifier<String> {

    private final String serialNumber;

    public DeviceSn(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public static DeviceSn of(String serialNumber) {
        return new DeviceSn(serialNumber);
    }

    @Override
    public String getValue() {
        return serialNumber;
    }
}
