package com.leliven.park.domain.order.temp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lecent.park.charge.ProjectCost;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.vehicle.model.Vehicle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbstractTempParkingOrder extends TenantDomain {
	private static final long serialVersionUID = -749838664341811027L;

	/**
	 * 停车记录ID
	 */
	@ApiModelProperty(value = "停车记录ID")
	private Long parkingId;
	/**
	 * 收费通道ID
	 */
	@ApiModelProperty(value = "收费通道ID")
	private Long channelId;
	/**
	 * 收费车场ID
	 */
	@ApiModelProperty(value = "收费车场ID")
	private Long parklotId;
	/**
	 * 代办ID
	 */
	@ApiModelProperty(value = "代办ID")
	private Long todoId;
	/**
	 * 车辆信息
	 */
	@ApiModelProperty(value = "车辆信息")
	private Vehicle vehicle;
	/**
	 * 费用
	 */
	private ParkingCost parkingCost;
	/**
	 * 异常金额类型 1多收 2少收
	 */
	@ApiModelProperty(value = "异常金额类型 1多收 2少收")
	private Integer unusualAmountType;
	/**
	 * 计费详细数据
	 */
	@ApiModelProperty(value = "计费详细数据")
	private ProjectCost chargeData;
	/**
	 * 微信ID
	 */
	@ApiModelProperty(value = "微信ID")
	private String openId;
	/**
	 * 收费员ID
	 * 当pay_type=1，对应company_user_id
	 */
	@ApiModelProperty(value = "收费员ID")
	private Long chargeUserId;
	/**
	 * 收费员值班ID
	 */
	@ApiModelProperty(value = "收费员值班ID")
	private Long chargeUserDutyId;
	/**
	 * 支付时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date payTime;
	/**
	 * 删除备注
	 */
	private String deleteRemark;
}

