package com.leliven.park.domain.basic.inspection.support;

import com.alibaba.csp.sentinel.util.function.Supplier;
import com.leliven.park.domain.basic.inspection.InspectionSubTaskGateway;
import com.leliven.park.domain.basic.inspection.InspectionTaskGateway;
import com.leliven.park.domain.basic.inspection.event.InspectionSubTaskEvent;
import com.leliven.park.domain.basic.inspection.model.InspectionSubTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTask;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionObjectRecord;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionRecordItem;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionSubTaskStatus;
import com.leliven.park.domain.basic.inspection.model.valueobject.Inspector;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import com.leliven.ddd.core.valueobject.SimpleLocation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * 巡检子任务领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class InspectionSubTaskDomainService {

    private final InspectionTaskGateway taskGateway;
    private final InspectionSubTaskGateway subTaskGateway;
    private final InspectionRecordDomainService recordDomainService;


    /**
     * 完成巡检子任务
     *
     * @param subTaskId 子任务ID {@link Long}
     * @param inspector 巡检员 {@link Inspector}
     * @param recordItemsSupplier 巡检记录项列表 {@link Supplier}<{@link List}<{@link InspectionRecordItem}>>
     * @param locationSupplier 位置信息 {@link Supplier}<{@link SimpleLocation}>
     * @return 完成后的巡检子任务 {@link InspectionSubTask}
     */
    @Transactional(rollbackFor = Exception.class)
    public InspectionSubTask complete(@Nonnull Long subTaskId,
                                      @Nonnull Inspector inspector,
                                      @Nonnull Supplier<List<InspectionRecordItem>> recordItemsSupplier,
                                      @Nonnull Supplier<SimpleLocation> locationSupplier) {
        // 获取巡检子任务
        InspectionSubTask subTask = this.subTaskGateway.getOrElseThrow(subTaskId);

        // 获取关联的巡检任务并校验
        InspectionTask task = this.taskGateway.getOrElseThrow(subTask.getTaskId())
            .verifyStatusShouldAllowSubTaskCompletion()
            .verifyExecutorPermission(inspector);

        // 完成子任务，生成巡检对象记录
        InspectionObjectRecord objectRecord = subTask.completeWithRecords(
                inspector,
                recordItemsSupplier.get(),
                locationSupplier.get()
            )
            .parklotId(task.getParklotId())
            .build();

        // 更新子任务状态
        subTaskGateway.update(subTask);
        // 保存巡检记录
        recordDomainService.create(objectRecord);
        // 发布子任务完成事件
        publishCompletedEvent(subTask);

        return subTask;
    }

    /**
     * 根据巡检任务ID，更新该任务下所有待处理的巡检子任务状态为超时
     *
     * @param taskId 巡检任务ID {@link Long}
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus2TimeoutByTaskId(Long taskId) {
        if (taskId == null) {
            log.warn("尝试将子任务状态更新为超时，但提供的 taskId 为空。");
            return;
        }

        // 查询待处理子任务
        List<Long> pendingSubTaskIds = this.subTaskGateway.listPendingSubTaskIdsByTaskId(taskId);
        if (Func.isEmpty(pendingSubTaskIds)) {
            log.info("任务 {} 下没有待处理 (PENDING) 的子任务需要设置为超时。", taskId);
            return;
        }

        log.info("准备将任务 {} 下的 {} 个待处理子任务状态更新为超时。IDs: {}",
            taskId, pendingSubTaskIds.size(), pendingSubTaskIds);

        try {
            // 批量更新状态
            this.subTaskGateway.changeStatusBatch(pendingSubTaskIds, InspectionSubTaskStatus.TIMEOUT);
        } catch (Exception e) {
            log.error("批量更新任务 {} 下的子任务状态为超时失败: {}", taskId, e.getMessage(), e);
            throw new ServiceException("批量更新子任务状态为超时失败");
        }
    }

    /**
     * 根据任务ID取消子任务
     * <p>
     * 将指定任务下的所有子任务状态变更为已取消
     * </p>
     *
     * @param taskId 任务ID {@link Long}
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelSubTasksByTaskId(Long taskId) {
        if (taskId == null) {
            log.warn("尝试取消子任务，但提供的 taskId 为空。");
            return;
        }

        // 查询待处理的子任务
        List<Long> pendingSubTaskIds = this.subTaskGateway.listPendingSubTaskIdsByTaskId(taskId);
        if (Func.isEmpty(pendingSubTaskIds)) {
            log.info("任务 {} 下没有待处理 (PENDING) 的子任务需要取消。", taskId);
            return;
        }

        log.info("准备将任务 {} 下的 {} 个待处理子任务状态更新为已取消。IDs: {}",
            taskId, pendingSubTaskIds.size(), pendingSubTaskIds);

        try {
            // 批量更新状态为已取消
            this.subTaskGateway.changeStatusBatch(pendingSubTaskIds, InspectionSubTaskStatus.CANCELLED);
            log.info("成功将任务 {} 下的 {} 个子任务状态更新为已取消", taskId, pendingSubTaskIds.size());
        } catch (Exception e) {
            log.error("批量更新任务 {} 下的子任务状态为已取消失败: {}", taskId, e.getMessage(), e);
            throw new ServiceException("批量更新子任务状态为已取消失败");
        }
    }

    /**
     * 发布巡检子任务完成事件
     *
     * @param subTask 巡检子任务 {@link InspectionSubTask}
     */
    private void publishCompletedEvent(InspectionSubTask subTask) {
        try {
            SpringDomainEventPublisher.publish(InspectionSubTaskEvent.completed(this, subTask));
            if (log.isDebugEnabled()) {
                log.debug("成功发布子任务 {} 完成事件。", subTask.getId());
            }
        } catch (Exception e) {
            log.error("发布子任务 {} 完成事件失败: {}", subTask.getId(), e.getMessage(), e);
        }
    }
}
