package com.leliven.park.domain.basic.inspection.support;

import com.lecent.park.en.SchedulesRole;
import com.leliven.park.domain.basic.inspection.InspectionItemGateway;
import com.leliven.park.domain.basic.inspection.model.InspectionItem;
import com.leliven.park.domain.basic.inspection.model.InspectionSubTaskGenerator;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskExecutor;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskGenerator;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionExecutorAssignConfig;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionItemGroup;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionTaskTimeWindow;
import com.leliven.park.domain.basic.parklot.ParklotRepositoryI;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingPlaceDeviceManager;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.support.ParkingDeviceManagementContext;
import com.leliven.park.domain.basic.scheduling.ParklotSchedulingGateway;
import com.leliven.park.domain.basic.scheduling.model.valueobject.ParklotWatchkeeper;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.valueobject.DateRange;

import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 巡检任务生成数据提供者
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class InspectionTaskDataProvider
	implements InspectionTaskGenerator.TaskDataProvider, InspectionSubTaskGenerator.SubTaskDataProvider {

    private final InspectionItemGateway itemGateway;
    private final ParklotRepositoryI parklotRepository;
    private final ParkingPlaceGateway parkingPlaceGateway;
    private final ParklotSchedulingGateway parklotSchedulingGateway;
    private final ParkingDeviceManagementContext parkingDeviceManagementContext;

    @Override
	public ParklotDomain getParklotById(Long parklotId) {
        return parklotRepository.getById(parklotId);
    }

	@Override
    public List<InspectionItem> listInspectionItemByIds(List<Long> itemIds) {
        return itemGateway.list(itemIds);
    }

    @Override
    public List<ParkingSpace> listParkingPlaceByParklotId(Long parklotId) {
        return parkingPlaceGateway.listByParklotId(parklotId);
    }

    @Override
    public ParkingPlaceDeviceManager getParkingPlaceDeviceManager(ParkingSpace parkingSpace) {
        return parkingDeviceManagementContext.asDeviceManager(parkingSpace);
    }

    @Override
    public List<Long> getParkingChannelsByParkingLotId(Long parklotId) {
        return new ArrayList<>();
    }

    @Override
    public InspectionItemGroup groupInspectionItemsByObjectType(List<Long> itemIds) {
        return itemGateway.groupByObjectType(itemIds);
    }

    @Override
    public List<InspectionTaskExecutor> listTaskExecutor(Long parklotId,
														 InspectionTaskTimeWindow timeWindow,
														 InspectionExecutorAssignConfig config) {
        List<SchedulesRole> schedulesRoles = config.getDutyGroups().stream()
            .map(SchedulesRole::resolve)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        DateRange dateRange = DateRange.of(timeWindow.getStartTime().toLocalDate(), timeWindow.getEndTime().toLocalDate());
        List<ParklotWatchkeeper> watchkeepers = parklotSchedulingGateway.getWatchkeepers(parklotId, schedulesRoles, dateRange);
        return watchkeepers.stream()
            .map(watchkeeper -> InspectionTaskExecutor.create(watchkeeper.getId(), watchkeeper.getName()))
            .collect(Collectors.toList());
    }
}
