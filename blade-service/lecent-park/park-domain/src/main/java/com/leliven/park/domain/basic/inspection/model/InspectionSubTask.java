package com.leliven.park.domain.basic.inspection.model;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.leliven.park.domain.basic.inspection.model.valueobject.*;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.ddd.core.valueobject.SimpleLocation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

/**
 * 巡检子任务
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionSubTask extends TenantDomain {

    /**
     * 巡检任务ID
     */
    private Long taskId;

    /**
     * 巡检对象
     */
    private InspectionObject object;

    /**
     * 巡检人信息
     */
    private Inspector inspector;

    /**
     * 任务完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 巡检任务项 (计划要执行的项)
     */
    private List<InspectionTaskItem> taskItems;

    /**
     * 是否有异常项
     */
    private boolean hasAbnormalItems = false;

    /**
     * 完成位置
     */
    private SimpleLocation completeLocation;

    /**
     * 无参构造
     */
    public InspectionSubTask() {
        this.taskItems = new ArrayList<>();
    }

    /**
     * 创建巡检子任务
     *
     * @param taskId   巡检任务ID
     * @param object   巡检对象 {@link InspectionObject}
     * @param tenantId 租户ID
     * @return 巡检子任务 {@link InspectionSubTask}
     */
    public static InspectionSubTask create(Long taskId, InspectionObject object, String tenantId) {
        InspectionSubTask subTask = new InspectionSubTask();
        subTask.setId(IdWorker.getId());
        subTask.setTaskId(taskId);
        subTask.setObject(object);
        subTask.setTaskItems(new ArrayList<>());
        subTask.taskStatus(InspectionSubTaskStatus.PENDING);
        subTask.setTenantId(tenantId);
        return subTask;
    }

    /**
     * 基于基础巡检项创建计划巡检的任务项列表
     *
     * @param items 基础巡检项列表 {@link InspectionItem}
     * @return 巡检子任务 {@link InspectionSubTask}
     */
    public InspectionSubTask createTaskItems(List<InspectionItem> items) {
        if (Func.isEmpty(items)) {
            log.warn("为子任务 {} 创建任务项时传入的基础巡检项列表为空", this.getId());
            return this;
        }
        return addTaskItems(InspectionTaskItem.createTaskItems(this, items));
    }

    /**
     * 完成巡检子任务并生成巡检对象记录
     *
     * @param inspector   巡检人信息 {@link Inspector}
     * @param recordItems 提交的巡检记录项 {@link InspectionRecordItem}
     * @param location    完成位置 {@link SimpleLocation}
     * @return 巡检对象记录 {@link InspectionObjectRecord}
     */
    public InspectionObjectRecord.InspectionObjectRecordBuilder completeWithRecords(Inspector inspector,
                                                                                    List<InspectionRecordItem> recordItems,
                                                                                    SimpleLocation location) {

        // 验证记录项
        validateRecordItems(recordItems);
        // 检查是否有异常项并更新状态
        this.hasAbnormalItems = recordItems.stream().anyMatch(InspectionRecordItem::isAbnormal);
        // 完成任务
        complete(inspector, location);
        // 创建并返回巡检对象记录
        return InspectionObjectRecord.builder()
            .taskId(this.taskId)
            .subTaskId(this.getId())
            .object(this.object)
            .inspector(inspector)
            .inspectionTime(this.completeTime)
            .type(InspectionType.TASK)
            .location(location)
            .recordItems(recordItems)
            .tenantId(this.tenantId);
    }

    /**
     * 完成巡检子任务 (标记完成状态，记录完成信息)
     * 注意：此方法不再处理具体的巡检结果，结果处理和InspectionRecord的创建由其他方法负责
     *
     * @param inspector     巡检人信息 {@link Inspector}
     * @param location      完成位置 {@link SimpleLocation}
     */
    public void complete(Inspector inspector, SimpleLocation location) {
        // 校验当前状态是否允许完成
        verifyCanBeCompleted();
        // 记录完成人信息和时间
        recordCompletionInfo(inspector);
        // 记录完成位置
        this.completeLocation = location;
        // 更新状态为已完成
        taskStatus(InspectionSubTaskStatus.COMPLETED);
    }

    /**
     * 验证记录项是否满足要求
     *
     * @param recordItems 提交的记录项
     */
    public void validateRecordItems(List<InspectionRecordItem> recordItems) {
		// 验证是否包含任务项 (业务要求必须有任务项才能完成)
		verifyActualTaskItemsShouldNotEmpty();

        if (Func.isEmpty(recordItems)) {
            throw new ServiceException("子任务 "+ this.getId() +" 有计划巡检项，但未提交任何巡检结果！");
        }

        if (recordItems.size() != this.taskItems.size()) {
            throw new ServiceException("提交的巡检记录项数量(" + recordItems.size()
                    + ")与计划数量(" + this.taskItems.size() + ")不一致！子任务ID: " + this.getId());
        }

        // 验证记录项与任务项ID匹配
        Set<Long> plannedItemIds = this.taskItems.stream()
                .map(InspectionTaskItem::getId)
                .collect(Collectors.toSet());

        Set<Long> submittedItemIds = recordItems.stream()
                .map(InspectionRecordItem::getTaskItemId)
                .collect(Collectors.toSet());

        if (plannedItemIds.size() != submittedItemIds.size()) {
            throw new ServiceException("提交的记录项中存在重复的任务项ID！子任务ID: " + this.getId());
        }

        if (!plannedItemIds.equals(submittedItemIds)) {
            Set<Long> missingIds = new HashSet<>(plannedItemIds);
            missingIds.removeAll(submittedItemIds);
            Set<Long> extraIds = new HashSet<>(submittedItemIds);
            extraIds.removeAll(plannedItemIds);
            throw new ServiceException("提交的记录项ID与计划项ID不完全匹配！缺失: " + missingIds + ", 多余: " + extraIds + "，子任务ID: " + this.getId());
        }

        // 验证记录项状态
        for (InspectionRecordItem item : recordItems) {
            if (item.getStatus() == null) {
                throw new ServiceException("记录项[" + item.getTaskItemId() + "]状态不能为空！子任务ID: " + this.getId());
            }

            if (item.isAbnormal() && item.getAbnormalInfo() == null) {
                log.warn("记录项 [{}] 状态为异常，但未提供有效的异常信息对象！子任务ID: {}", item.getTaskItemId(), this.getId());
            }
        }
    }

    /**
     * 添加巡检任务项 (计划项)
     *
     * @param taskItems 计划的巡检任务项 {@link InspectionTaskItem}
     * @return 巡检子任务 {@link InspectionSubTask}
     */
    public InspectionSubTask addTaskItems(List<InspectionTaskItem> taskItems) {
        if (Func.isNotEmpty(taskItems)) {
            this.taskItems.addAll(taskItems);
        }
        return this;
    }

    /**
     * 设置子任务状态
     *
     * @param status 任务状态 {@link InspectionSubTaskStatus}
     * @return 巡检子任务 {@link InspectionSubTask}
     */
    public InspectionSubTask taskStatus(InspectionSubTaskStatus status) {
        if (status == null) {
            throw new ServiceException("设置的子任务状态不能为空");
        }
        this.status = status.getValue();
        return this;
    }

    /**
     * 判断子任务是否为待巡检状态
     *
     * @return 是否为待巡检状态 {@code true} 待巡检 {@code false} 非待巡检
     */
    public boolean isPending() {
        return Objects.equals(this.status, InspectionSubTaskStatus.PENDING.getValue());
    }

    /**
     * 判断子任务是否已完成
     *
     * @return 是否已完成 {@code true} 已完成 {@code false} 未完成
     */
    public boolean isCompleted() {
        return Objects.equals(this.status, InspectionSubTaskStatus.COMPLETED.getValue());
    }

    /**
     * 判断子任务是否已超时
     *
     * @return 是否已超时 {@code true} 已超时 {@code false} 未超时
     */
    public boolean isTimeout() {
        return Objects.equals(this.status, InspectionSubTaskStatus.TIMEOUT.getValue());
    }

    /**
     * 记录完成信息（巡检人、时间）
     *
     * @param inspector 巡检人信息 {@link Inspector}
     */
    private void recordCompletionInfo(Inspector inspector) {
        if (inspector == null) {
            throw new ServiceException("完成子任务时，巡检人信息不能为空");
        }
        this.setInspector(inspector);
        this.setCompleteTime(LocalDateTime.now());
    }

    /**
     * 校验是否可以标记为完成状态
     */
    private void verifyCanBeCompleted() {
        if (this.isCompleted()) {
            throw new ServiceException("当前巡检子任务 " + this.getId() + " 已完成，请勿重复处理");
        }
        if (this.isTimeout()) {
            throw new ServiceException("当前巡检子任务 " + this.getId() + " 已超时，无法完成");
        }
        // 可以根据需要添加其他校验，例如是否处于 PENDING 状态
        if (!this.isPending()){
            throw new ServiceException("当前巡检子任务 " + this.getId() + " 不处于待处理状态，无法完成");
        }
    }

    /**
     * [可选] 校验巡检子任务实际任务项不能为空 (如果业务要求必须有任务项才能完成)
     */
    private void verifyActualTaskItemsShouldNotEmpty() {
        if (Func.isEmpty(this.taskItems)) {
            throw new ServiceException("巡检子任务 " + this.getId() + " 没有计划任务项，无法完成");
        }
    }

    /**
     * 获取巡检人ID
     *
     * @return 巡检人ID
     */
    public Long getInspectorId() {
        return inspector != null ? inspector.getInspectorId() : null;
    }

    /**
     * 获取巡检人名称
     *
     * @return 巡检人名称
     */
    public String getInspectorName() {
        return inspector != null ? inspector.getInspectorName() : null;
    }
}
