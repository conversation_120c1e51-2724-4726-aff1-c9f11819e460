package com.leliven.park.domain.basic.inspection.event;

import com.leliven.park.domain.basic.inspection.model.InspectionTask;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

import javax.annotation.Nonnull;
import java.util.Objects;

/**
 * 巡检任务领域事件
 *
 * <AUTHOR>
 */
public class InspectionTaskEvent extends AbstractDomainEvent<InspectionTask, InspectionTaskEvent.EventType> {

    /**
     * Create a new DomainEvent.
     *
     * @param source    the object on which the event initially occurred (never {@code null})
     * @param eventType domain event type
     * @param payload   the payload object (never {@code null})
     */
    protected InspectionTaskEvent(Object source, EventType eventType, @Nonnull InspectionTask payload) {
        super(source, eventType, payload);
    }

    /**
     * 创建巡检任务已创建事件
     *
     * @param source  事件源
     * @param task    巡检任务
     * @return 巡检任务已创建事件
     */
    public static InspectionTaskEvent created(Object source, @Nonnull InspectionTask task) {
        return new InspectionTaskEvent(source, EventType.CREATED, task);
    }

    /**
     * 创建巡检任务事件
     *
     * @param source  事件源
     * @param eventType 事件类型
     * @param payload   事件负载
     * @return 巡检任务事件
     */
    public static InspectionTaskEvent of(Object source, EventType eventType, @Nonnull InspectionTask payload) {
        return new InspectionTaskEvent(source, eventType, payload);
    }

    /**
     * 为否为已超时事件
     *
     * @return 是否为已超时事件 {@code true} 是 {@code false} 否
     */
    public boolean isTimeoutEvent() {
        return Objects.equals(EventType.TIMEOUT, this.getEventType());
    }

    public enum EventType implements DomainEventType {

        /**
         * 已创建
         */
        CREATED,
        /**
         * 已开始
         */
        STARTED,
        /**
         * 已超时
         */
        TIMEOUT,
        /**
         * 已完成
         */
        COMPLETED,
        /**
         * 已取消
         */
        CANCELLED,
    }
}
