package com.leliven.park.domain.parking.core.model.objectvalue;

import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceDevice;
import com.leliven.ddd.core.annotations.ValueObject;
import lombok.Builder;
import lombok.Getter;

/**
 * 路边车位锁控制执行结果
 *
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class ParkingLockCtrlExecuteResult {

	private final boolean success;
	private final String message;
	private final ParkingLockCtrlType ctrlType;
	private final ParkingPlaceDevice parkingPlaceDevice;
	private final RoadsideParkingLockCtrlSceneParam sceneParam;

	/**
	 * 创建失败结果
	 *
	 * @param message 失败原因
	 * @return 失败结果
	 */
	public static ParkingLockCtrlExecuteResult fail(String message) {
		return ParkingLockCtrlExecuteResult.builder()
			.success(false)
			.message(message)
			.build();
	}

	/**
	 * 是否处理失败
	 *
	 * @return 是否失败 {@code true} 失败 {@code false} 成功
	 */
	public boolean isFailed() {
		return !this.success;
	}
}
