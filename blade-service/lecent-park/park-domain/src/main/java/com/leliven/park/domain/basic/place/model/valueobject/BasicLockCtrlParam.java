package com.leliven.park.domain.basic.place.model.valueobject;

import com.leliven.park.domain.basic.device.model.ParkingDeviceCommandParam;
import com.leliven.ddd.core.annotations.ValueObject;
import lombok.Getter;

import java.time.Duration;

/**
 * 锁命令控制命令参数
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
public class BasicLockCtrlParam implements ParkingDeviceCommandParam {

    public static final BasicLockCtrlParam DEFAULT = new BasicLockCtrlParam();

    /**
     * 延迟执行时间
     *
     * <p>
     * 为 0秒时，实时下发执行命令
     * </p>
     */
    private final Duration delaySeconds;

    public BasicLockCtrlParam() {
        this(Duration.ZERO);
    }

    public BasicLockCtrlParam(Duration delaySeconds) {
        this.delaySeconds = delaySeconds;
    }

}
