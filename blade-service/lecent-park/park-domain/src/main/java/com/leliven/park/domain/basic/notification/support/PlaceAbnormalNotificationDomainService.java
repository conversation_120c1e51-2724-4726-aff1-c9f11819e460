package com.leliven.park.domain.basic.notification.support;

import com.leliven.park.domain.basic.notification.enums.PlaceAbnormalNotificationTypeEnum;
import com.leliven.park.domain.basic.notification.model.PlaceAbnormalNotification;
import com.leliven.park.domain.basic.notification.model.valueobject.NotificationContent;
import com.leliven.park.domain.basic.notification.model.valueobject.NotificationTarget;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 车位异常通知领域服务
 * 负责构建和处理车位异常通知的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@DomainService
public class PlaceAbnormalNotificationDomainService {

    /**
     * 创建车位异常通知
     *
     * @param title      通知标题
     * @param content    通知内容
     * @param routePath  跳转路径
     * @param routeName  路径名称
     * @param parameters 参数
     * @param userId     用户ID
     * @return 车位异常通知
     */
    public PlaceAbnormalNotification createNotification(String title, String content, String routePath, String routeName,
                                                        String parameters, Long userId) {
        log.info("创建车位异常通知: title={}, content={}, routePath={}, routeName={}, userId={}",
                title, content, routePath, routeName, userId);

        // 创建通知内容
        NotificationContent notificationContent = NotificationContent.of(title, content, LocalDateTime.now());

        // 创建通知目标
        NotificationTarget notificationTarget = NotificationTarget.of(routePath, routeName, parameters);

        // 获取当前租户ID
        String tenantId = AuthUtil.getTenantId();

        // 创建车位异常通知
        PlaceAbnormalNotification notification = PlaceAbnormalNotification.create(
                notificationContent, notificationTarget, userId, tenantId);

        // 验证通知有效性
        if (!notification.isValid()) {
            log.error("车位异常通知无效: {}", notification);
            throw new IllegalArgumentException("车位异常通知信息不完整");
        }

        log.info("车位异常通知创建成功: {}", notification);
        return notification;
    }

    /**
     * 创建车位异常通知（无用户ID）
     *
     * @param title      通知标题
     * @param content    通知内容
     * @param routePath  跳转路径
     * @param routeName  路径名称
     * @param parameters 参数
     * @return 车位异常通知
     */
    public PlaceAbnormalNotification createNotification(String title, String content,
                                                        String routePath, String routeName,
                                                        String parameters) {
        return createNotification(title, content, routePath, routeName, parameters, null);
    }

    /**
     * 构建车位异常上报通知
     * 根据车位异常上报数据构建通知内容
     *
     * @param abnormalType    异常类型
     * @param abnormalOrderId 异常订单ID
     * @param parklotName     车场名称
     * @param placeCode       车位编号
     * @param systemPlate     系统记录的车牌号
     * @param actualPlate     实际在场车牌号
     * @param reportTime      上报时间
     * @param reporterName    上报人姓名
     * @return 车位异常通知
     */
    public PlaceAbnormalNotification buildPlaceAbnormalReportNotification(String abnormalType, Long abnormalOrderId,
                                                                          String parklotName, String placeCode,
                                                                          String systemPlate, String actualPlate,
                                                                          Date reportTime, String reporterName) {

        log.info("构建车位异常上报通知: abnormalType={}, abnormalOrderId={}, parklotName={}, placeCode={}",
                abnormalType, abnormalOrderId, parklotName, placeCode);

        // 根据异常类型获取对应的枚举配置
        PlaceAbnormalNotificationTypeEnum notificationType = PlaceAbnormalNotificationTypeEnum.getByCode(abnormalType);
        LecentAssert.notNull(notificationType, "未找到异常类型对应的通知配置: {}", abnormalType);

        // 构建通知内容
        String content = buildNotificationContent(notificationType.getContentTemplate(),
                parklotName, placeCode, systemPlate, actualPlate, reportTime, reporterName);

        // 构建参数
        String parameters = buildNotificationParameters(notificationType.getParametersTemplate(), placeCode);

        // 创建通知
        return createNotification(
                notificationType.getTitle(),
                content,
                notificationType.getRoutePath(),
                notificationType.getRouteName(),
                parameters
        );
    }

    /**
     * 构建通知内容
     * 使用模板和数据进行替换
     *
     * @param contentTemplate 内容模板
     * @param parklotName     车场名称
     * @param placeCode       车位编号
     * @param systemPlate     系统记录的车牌号
     * @param actualPlate     实际在场车牌号
     * @param reportTime      上报时间
     * @param reporterName    上报人姓名
     * @return 构建后的内容
     */
    private String buildNotificationContent(String contentTemplate, String parklotName, String placeCode,
                                            String systemPlate, String actualPlate, Date reportTime, String reporterName) {
        String content = contentTemplate;

        // 替换占位符
        content = content.replace("{parklotName}", safeGetString(parklotName));
        content = content.replace("{placeCode}", safeGetString(placeCode));
        content = content.replace("{systemPlate}", safeGetString(systemPlate));
        content = content.replace("{actualPlate}", safeGetString(actualPlate));
        content = content.replace("{reportTime}", DateUtil.formatDateTime(reportTime));
        content = content.replace("{reporterName}", safeGetString(reporterName));

        return content;
    }

    /**
     * 构建通知参数
     * 使用模板和数据进行替换
     *
     * @param parametersTemplate 参数模板
     * @param placeCode          车位编号
     * @return 构建后的参数
     */
    private String buildNotificationParameters(String parametersTemplate, String placeCode) {
        String parameters = parametersTemplate;

        // 替换占位符
        parameters = parameters.replace("{placeCode}", placeCode);

        return parameters;
    }

    /**
     * 安全获取字符串，避免null
     *
     * @param value 值
     * @return 安全的字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "未知";
    }
}
