package com.leliven.park.domain.basic.inspection.model.valueobject;

import lombok.Getter;

import com.leliven.ddd.core.annotations.ValueObject;

import lombok.AllArgsConstructor;

/**
 * 巡检人值对象
 *
 * <p>
 * 封装巡检人ID和名称
 * </p>
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class Inspector {

    /**
     * 巡检人ID
     */
    private Long inspectorId;

    /**
     * 巡检人名称
     */
    private String inspectorName;

    /**
     * 创建巡检人值对象
     *
     * @param inspectorId 巡检人ID
     * @param inspectorName 巡检人名称
     * @return 巡检人值对象
     */
    public static Inspector of(Long inspectorId, String inspectorName) {
        return new Inspector(inspectorId, inspectorName);
    }
}
