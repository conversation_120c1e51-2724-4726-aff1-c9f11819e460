package com.leliven.park.domain.order.temp.event;

import com.leliven.park.domain.order.unified.model.valueobject.PaymentSuccessPayload;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;

/**
 * 临停订单支付成功事件
 *
 * <AUTHOR>
 * @since 2024/5/23
 */
public class TempParkingOrderPaySuccessEvent extends AbstractDomainEvent<PaymentSuccessPayload, DomainEventType> {


    /**
     * Create a new DomainEvent.
     *
     * @param source    the object on which the event initially occurred (never {@code null})
     * @param payload   the payload object (never {@code null})
     */
    public TempParkingOrderPaySuccessEvent(Object source, PaymentSuccessPayload payload) {
        super(source, EventType.PAYMENT_SUCCEEDED, payload);
    }

    public enum EventType implements DomainEventType {
        PAYMENT_SUCCEEDED,
    }
}
