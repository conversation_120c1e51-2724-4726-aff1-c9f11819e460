package com.leliven.park.domain.parking.core.support.factory;

import com.google.common.collect.Lists;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.temp.TempParkingOrderRepositoryI;
import com.leliven.park.domain.parking.core.specification.ParkingFreeDurationSpec;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.specification.DisjunctionSpecification;
import com.leliven.ddd.core.specification.Specification;

/**
 * 停车免费时长规约工厂
 *
 * <AUTHOR>
 */
@DomainService
public class ParkingOrderFreeDurationSpecFactory {

	private final TempParkingOrderRepositoryI tempParkingOrderRepository;
	private final ParkingFreeDurationSpecFactory parkingFreeDurationSpecFactory;

	public ParkingOrderFreeDurationSpecFactory(TempParkingOrderRepositoryI tempParkingOrderRepository,
											   ParkingFreeDurationSpecFactory parkingFreeDurationSpecFactory) {
		this.tempParkingOrderRepository = tempParkingOrderRepository;
		this.parkingFreeDurationSpecFactory = parkingFreeDurationSpecFactory;
	}

	/**
	 * 创建停车免费时长规约
	 *
	 * <p>
	 * 用于校验停车订单是否在免费时长内，其中包含两个规约:
	 * <ul>
	 * 	<li>1. 入场免费时长规约</li>
	 * 	<li>2. 提前支付免费离场时长规约</li>
	 * </ul>
	 * 两个规约满足其一，则表示停车订单在免费时长内
	 * </p>
	 *
	 * @param chargeRegulation 临时停车收费规则 {@link TempParkingBillingRuleCfg}
	 * @return 停车免费时长规约 {@link Specification}<{@link ParkingOrderAgg}>
	 */
	public Specification<ParkingOrderAgg> createParkingFreeDurationSpec(TempParkingBillingRuleCfg chargeRegulation) {
		return new DisjunctionSpecification<>(Lists.newArrayList(
			createEntryFreeDurationSpec(chargeRegulation), createPaidFreeLeaveDurationSpec(chargeRegulation)));
	}

	/**
	 * 创建入场免费时长规约
	 *
	 * <p>
	 * 用于校验停车订单是否在入场免费时长内
	 * </p>
	 *
	 * @param chargeRegulation 临时停车收费规则 {@link TempParkingBillingRuleCfg}
	 * @return 入场免费时长规约 {@link Specification}<{@link ParkingOrderAgg}>
	 */
	public Specification<ParkingOrderAgg> createEntryFreeDurationSpec(TempParkingBillingRuleCfg chargeRegulation) {
		return new ParkingOrderEntryFreeDurationSpec(
			this.parkingFreeDurationSpecFactory.createEntryFreeDurationSpec(chargeRegulation));
	}

	/**
	 * 创建提前支付免费离场时长规约
	 *
	 * @param chargeRegulation 临时停车收费规则 {@link TempParkingBillingRuleCfg}
	 * @return 提前支付免费离场时长规约 {@link Specification}<{@link ParkingOrderAgg}>
	 */
	public Specification<ParkingOrderAgg> createPaidFreeLeaveDurationSpec(TempParkingBillingRuleCfg chargeRegulation) {
		return new ParkingOrderPaidFreeLeaveDurationSpec(
			this.parkingFreeDurationSpecFactory.createPaidFreeLeaveDurationSpec(chargeRegulation));
	}

	/**
	 * 停车订单入场免费时长规约
	 *
	 * <AUTHOR>
	 */
	private static class ParkingOrderEntryFreeDurationSpec implements Specification<ParkingOrderAgg> {

		private final ParkingFreeDurationSpec spec;

		public ParkingOrderEntryFreeDurationSpec(ParkingFreeDurationSpec spec) {
			this.spec = spec;
		}

		@Override
		public boolean isSatisfiedBy(ParkingOrderAgg candidate) {
			return spec.isSatisfiedBy(candidate.getEnterInfo().getDatetime());
		}

	}

	/**
	 * 停车订单支付后免费离场时长规约
	 *
	 * <AUTHOR>
	 */
	private class ParkingOrderPaidFreeLeaveDurationSpec implements Specification<ParkingOrderAgg> {

		private final ParkingFreeDurationSpec spec;

		public ParkingOrderPaidFreeLeaveDurationSpec(ParkingFreeDurationSpec spec) {
			this.spec = spec;
		}

		@Override
		public boolean isSatisfiedBy(ParkingOrderAgg candidate) {
			return tempParkingOrderRepository.getCollectionByParkingOrderId(candidate.getId())
				.getLastPaidOrder()
				.map(t -> spec.isSatisfiedBy(t.getPayTime()))
				.orElse(false);
		}
	}


}
