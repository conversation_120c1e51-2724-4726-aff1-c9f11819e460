package com.leliven.park.domain.basic.inspection.model;

import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionObjectType;
import com.leliven.ddd.core.model.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Set;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * 巡检项领域对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectionItem extends BaseDomain {

    /**
     * 项名称
     */
    private String itemName;

    /**
     * 项编码
     */
    private String itemCode;

    /**
     * 分类类型（1：业务项，2：设备项）
     */
    private Integer categoryType;

    /**
     * 关联对象
     */
    private InspectionObjectType objectType;

    /**
     * 关联的设备类型
     */
    private Set<DeviceType> relatedDeviceTypes;

    /**
     * 描述
     */
    private String description;


    /**
     * 判断是否包含指定的设备类型
     *
     * @param deviceType 设备类型
     * @return 是否包含该设备类型
     */
    public boolean containsDeviceType(DeviceType deviceType) {
        if (deviceType == null) {
            return false;
        }
        return relatedDeviceTypes.contains(deviceType);
    }

    /**
     * 判断是否包含任意一个指定的设备类型
     *
     * @param deviceTypes 设备类型集合
     * @return 是否包含任意一个指定的设备类型
     */
    public boolean containsAnyDeviceType(Set<DeviceType> deviceTypes) {
        if (deviceTypes == null || deviceTypes.isEmpty()) {
            return false;
        }
        return !Collections.disjoint(relatedDeviceTypes, deviceTypes);
    }

    /**
     * 获取与指定设备类型集合的交集
     *
     * @param deviceTypes 设备类型集合
     * @return 交集设备类型
     */
    public Set<DeviceType> getIntersectionDeviceTypes(Set<DeviceType> deviceTypes) {
        if (deviceTypes == null || deviceTypes.isEmpty()) {
            return Collections.emptySet();
        }

        return relatedDeviceTypes.stream()
            .filter(deviceTypes::contains)
            .collect(Collectors.toSet());
    }

    /**
     * 判断是否是设备项
     *
     * @return 是否是设备项
     */
    public boolean isDeviceItem() {
        return Integer.valueOf(2).equals(this.categoryType);
    }
}
