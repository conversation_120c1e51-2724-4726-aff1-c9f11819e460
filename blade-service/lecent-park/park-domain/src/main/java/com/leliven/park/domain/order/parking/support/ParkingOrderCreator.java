package com.leliven.park.domain.order.parking.support;

import com.leliven.park.domain.order.parking.ParkingOrderRepositoryI;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import com.leliven.park.domain.order.parking.event.ParkingOrderEventType;
import com.leliven.park.domain.order.parking.support.factory.ParkingOrderFactory;
import com.leliven.park.domain.order.todo.entity.ParkingTodo;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 停车订单创建领域服务
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ParkingOrderCreator implements ApplicationContextAware {

	private final ParkingOrderFactory parkingOrderFactory;
	private final ParkingOrderRepositoryI parkingOrderRepository;

	private List<ParkingOrderCreationPostProcessor> postProcessors;

	/**
	 * 创建订单
	 *
	 * @param enterTodo 入场待办
	 * @return 订单聚合
	 */
	public ParkingOrderAgg create(ParkingTodo enterTodo) {
		ParkingOrderAgg parkingOrderAgg = parkingOrderFactory.create(enterTodo);
		this.creationPostProcess(parkingOrderAgg);
		this.save(parkingOrderAgg);
		return parkingOrderAgg;
	}

	/**
	 * 订单创建后置处理
	 *
	 * @param parkingOrderAgg 订单聚合
	 */
	private void creationPostProcess(ParkingOrderAgg parkingOrderAgg) {
		this.postProcessors.forEach(postProcessor -> postProcessor.postProcess(parkingOrderAgg));
	}

	/**
	 * 保存订单
	 *
	 * @param parkingOrderAgg 订单聚合
	 */
	private void save(ParkingOrderAgg parkingOrderAgg) {
		boolean saved = parkingOrderRepository.save(parkingOrderAgg);
		if (saved) {
			ParkingOrderDomainEvent domainEvent =
				new ParkingOrderDomainEvent(this, ParkingOrderEventType.ENTERED, parkingOrderAgg);
			SpringDomainEventPublisher.publish(domainEvent);
		}
	}

	/**
	 * 提取后置处理器
	 *
	 * @param context 应用上下文
	 * @return 后置处理器列表
	 */
	private List<ParkingOrderCreationPostProcessor> extractPostProcessors(ApplicationContext context) {
		List<ParkingOrderCreationPostProcessor> processors =
			new ArrayList<>(context.getBeansOfType(ParkingOrderCreationPostProcessor.class).values());
		AnnotationAwareOrderComparator.sort(processors);
		return processors;
	}

	@Override
	public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
		if (Func.isEmpty(postProcessors)) {
			postProcessors = extractPostProcessors(applicationContext);
		}
	}
}
