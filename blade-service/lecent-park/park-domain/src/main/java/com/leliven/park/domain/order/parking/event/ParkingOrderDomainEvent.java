package com.leliven.park.domain.order.parking.event;

import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.ddd.core.event.AbstractDomainEvent;

/**
 * 停车订单领域事件
 *
 * <AUTHOR>
 */
public class ParkingOrderDomainEvent extends AbstractDomainEvent<ParkingOrderAgg, ParkingOrderEventType> {
	private static final long serialVersionUID = -8195427617272405299L;

	/**
	 * Create a new DomainEvent.
	 *
	 * @param source    the object on which the event initially occurred (never {@code null})
	 * @param eventType domain event type
	 * @param payload   the payload object (never {@code null})
	 */
	public ParkingOrderDomainEvent(Object source, ParkingOrderEventType eventType, ParkingOrderAgg payload) {
		super(source, eventType, payload);
	}

	/**
	 * 是否为路边停车订单
	 *
	 * @return {@code true} 是路边停车订单 {@code false} 不是路边停车订单
	 */
	public boolean isRoadsideOrderEvent() {
        return getPayload().isOnRoadOrder();
    }

	/**
	 * 是否为清除事件
	 *
	 * @return {@code true} 是清除事件 {@code false} 不是清除事件
	 */
	public boolean isCleared() {
		return ParkingOrderEventType.CLEARED.equals(getEventType());
	}
}
