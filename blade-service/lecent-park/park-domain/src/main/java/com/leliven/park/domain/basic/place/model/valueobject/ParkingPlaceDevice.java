package com.leliven.park.domain.basic.place.model.valueobject;

import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.ddd.core.annotations.ValueObject;
import lombok.Getter;

/**
 * 停车位设备
 *
 * <AUTHOR>
 */
@ValueObject
public class ParkingPlaceDevice implements ParkingDevice {

	private final ParkingDevice device;
	@Getter
	private final ParkingSpace parkingSpace;

	public ParkingPlaceDevice(ParkingDevice device, ParkingSpace parkingSpace) {
		this.device = device;
		this.parkingSpace = parkingSpace;
	}

	@Override
	public Long getId() {
		return device.getId();
	}

	@Override
	public void setId(Long id) {

	}

	@Override
	public String getSn() {
		return device.getSn();
	}

	@Override
	public void setSn(String sn) {

	}

	@Override
	public String getName() {
		return device.getName();
	}

	@Override
	public void setName(String name) {

	}

	@Override
	public DeviceType getType() {
		return device.getType();
	}

	@Override
	public void setType(DeviceType type) {

	}
}
