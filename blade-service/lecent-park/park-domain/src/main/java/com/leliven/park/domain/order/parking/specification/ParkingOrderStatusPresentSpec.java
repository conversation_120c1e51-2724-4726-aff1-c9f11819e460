package com.leliven.park.domain.order.parking.specification;

import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.ddd.core.specification.CompositeSpecification;

import java.util.Objects;

/**
 * 停车订单状态在场规约
 *
 * <p>
 * 用于判断停车订单状态是否在场
 * </p>
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S6548")
public class ParkingOrderStatusPresentSpec extends CompositeSpecification<ParkingOrderAgg> {

	public static final ParkingOrderStatusPresentSpec INSTANCE = new ParkingOrderStatusPresentSpec();

	private ParkingOrderStatusPresentSpec() {
	}

	/**
	 * 判断停车订单停车状态是否在场
	 *
	 * @param candidate 候选对象 {@link ParkingOrderAgg}
	 * @return 是否在场
	 */
	@Override
	public boolean isSatisfiedBy(ParkingOrderAgg candidate) {
		return Objects.nonNull(candidate) && candidate.isPresent();
	}
}
