package com.leliven.park.domain.basic.place.support;

import com.leliven.park.common.model.ParkingDevice;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.event.ParkingPlaceCameraCaptureDomainEvent;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCapture;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCaptureRecord;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureGroup;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureType;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.ObjectValidator;

import java.util.List;

/**
 * 摄像头抓拍记录领域服务
 *
 * <AUTHOR>
 */
@DomainService
@RequiredArgsConstructor
public class ParkingPlaceCameraCaptureDomainService {

	private final ParkingPlaceGateway parkingPlaceGateway;
	private final ParkingPlaceValidator parkingPlaceValidator;


	/**
	 * 创建摄像头抓拍记录
	 *
	 * @param device       车位设备
	 * @param captureGroup 抓拍记录组
	 */
	public void create(ParkingDevice device, ParkingPlaceCameraCaptureGroup captureGroup) {
		ParkingPlaceCameraCaptureValidator.verifyCreateRule(captureGroup);

		ParkingSpace parkingSpace = this.parkingPlaceGateway.getByDevice(device);
		this.parkingPlaceValidator.verifyParkingPlaceShouldNonNull(parkingSpace);

		ParkingPlaceCameraCapture cameraCapture = ParkingPlaceCameraCapture.create(captureGroup.getRecords())
			.belongToDevice(device)
			.belongToParkingSpace(parkingSpace);
		this.parkingPlaceGateway.saveCameraCapture(cameraCapture);
		SpringDomainEventPublisher.publish(
			ParkingPlaceCameraCaptureDomainEvent.ofCreated(this, captureGroup.getType(), cameraCapture));
	}

	/**
	 * 车位摄像头抓拍验证器
	 */
	private static class ParkingPlaceCameraCaptureValidator {

		/**
		 * 验证摄像头抓拍记录创建规则
		 *
		 * @param group 摄像头抓拍记录组 {@link ParkingPlaceCameraCaptureGroup}
		 */
		public static void verifyCreateRule(ParkingPlaceCameraCaptureGroup group) {
			verifyCameraCaptureGroupShouldNonNull(group);
			verifyCameraCaptureTypeShouldNonNull(group.getType());
			verifyCameraCaptureRecordsShouldNotEmpty(group.getRecords());
		}

		/**
		 * 验证摄像头抓拍记录组不能为空
		 *
		 * @param group 摄像头抓拍记录组 {@link ParkingPlaceCameraCaptureGroup}
		 */
		public static void verifyCameraCaptureGroupShouldNonNull(ParkingPlaceCameraCaptureGroup group) {
			ObjectValidator.requireNonNull(group, "captureGroup must not be null");
		}

		/**
		 * 验证摄像头抓拍类型不能为空
		 *
		 * @param type 摄像头抓拍类型 {@link ParkingPlaceCameraCaptureType}
		 */
		public static void verifyCameraCaptureTypeShouldNonNull(ParkingPlaceCameraCaptureType type) {
			ObjectValidator.requireNonNull(type, "captureType must not be null");
		}

		/**
		 * 验证摄像头抓拍记录不能为空
		 *
		 * @param records 摄像头抓拍记录 {@link List}<{@link ParkingPlaceCameraCaptureRecord}>
		 */
		public static void verifyCameraCaptureRecordsShouldNotEmpty(List<ParkingPlaceCameraCaptureRecord> records) {
			ObjectValidator.requireNotEmpty(records, "captureRecords must not be empty");
		}
	}
}

