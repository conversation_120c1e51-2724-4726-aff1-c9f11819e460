package com.leliven.park.domain.basic.scheduling.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 车场值班人员
 *
 * <AUTHOR>
 */
@Getter
@ValueObject
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ParklotWatchkeeper {

    /**
     * 人员ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     *
     * @param id
     * @param name
     */
    private ParklotWatchkeeper(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 创建车场值班人员
     *
     * @param id 人员ID
     * @param name 姓名
     * @return 车场值班人员
     */
    public static ParklotWatchkeeper of(Long id, String name) {
        return new ParklotWatchkeeper(id, name);
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ParklotWatchkeeper)) {
            return false;
        }
        ParklotWatchkeeper that = (ParklotWatchkeeper) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }
}
