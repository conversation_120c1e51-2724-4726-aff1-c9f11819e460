package com.leliven.park.domain.parking.core.model.objectvalue;

import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.ddd.core.annotations.ValueObject;
import com.leliven.vehicle.model.Vehicle;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * 停车特殊出场信息
 *
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class ParkingCompensateExitInfo {

	/** 停车订单ID */
	private Long parkingOrderId;

	/** 车位ID */
	private Long placeId;

	/** 车辆信息 */
	private Vehicle vehicle;

	/** 出场时间 */
	private Date exitTime;

	/** 图片地址 */
	private String imageUrl;

	/** 触发类型 */
	private ParkingTriggerWay triggerType;

	/** 设备类型 */
	private DeviceType deviceType;

	/**
	 * 安全的获取设备类型值
	 */
	public Integer getDeviceTypeValue() {
		if (deviceType == null) {
			return null;
		}
		return deviceType.getValue();
	}
}
