package com.leliven.park.domain.parking.core.support.specialexit;

import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.parking.core.model.objectvalue.ParkingCompensateExitInfo;
import com.leliven.ddd.core.annotations.DomainService;
import org.springframework.core.annotation.Order;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于触发时间优先级的复合补偿出场处理器
 * 选择最新触发时间的处理器执行
 *
 * <AUTHOR>
 */
@Order(0)
@DomainService
public class CompositeTimePriorityCompensateExitHandler extends AbstractParkingCompensateExitHandler
	implements ParkingCompensateExitHandler {

	/**
	 * 补偿出场信息提供者列表
	 */
	private final List<ParkingCompensateExitInfoProvider> providers;


	/**
	 * 构造函数
	 *
	 * @param providers 补偿出场信息提供者列表 {@link List}<{@link ParkingCompensateExitInfoProvider}>
	 */
	public CompositeTimePriorityCompensateExitHandler(List<ParkingCompensateExitInfoProvider> providers) {
		this.providers = new ArrayList<>(providers);
	}

	@Override
	public boolean supports(ParkingOrderAgg parkingOrderAgg) {
		return parkingOrderAgg.isOnRoadOrder();
	}

	/**
	 * 获取补偿出场信息提供者列表
	 *
	 * @return 补偿出场信息提供者列表 {@link List}<{@link ParkingCompensateExitInfoProvider}>
	 */
	public List<ParkingCompensateExitInfoProvider> getProviders() {
		return Collections.unmodifiableList(providers);
	}

	/**
	 * 选择最新触发时间的处理器执行
	 *
	 * @param parkingOrder 停车订单
	 * @return 是否处理成功
	 */
	@Override
	protected boolean doHandle(ParkingOrderAgg parkingOrder) {
		return getLatestExitInfo(parkingOrder).map(this::publishExitInfoSelectedEvent).orElse(false);
	}

	/**
	 * 获取最晚的出场信息
	 *
	 * <ul>
	 * <li>通过获取的候选出场信息列表中，选择最晚的出场信息</li>
	 * <li>当有多个候选信息的出场时间时，默认根据提供者中的 {@code @Order} 排序获取第一个</li>
	 * </ul>
	 *
	 * @param parkingOrder 停车订单 {@link ParkingOrderAgg}
	 * @return 出场信息 {@link ParkingCompensateExitInfo}
	 */
	protected Optional<ParkingCompensateExitInfo> getLatestExitInfo(ParkingOrderAgg parkingOrder) {
		return getCandidateExitInfos(parkingOrder)
			.stream().max(Comparator.comparing(ParkingCompensateExitInfo::getExitTime));
	}

	/**
	 * 获取候选的出场信息列表
	 *
	 * @param parkingOrder 停车订单 {@link ParkingOrderAgg}
	 * @return 出场信息列表
	 */
	protected List<ParkingCompensateExitInfo> getCandidateExitInfos(ParkingOrderAgg parkingOrder) {
		return providers.stream()
			.map(provider -> provider.getExitInfoIfSupported(parkingOrder))
			.filter(Optional::isPresent)
			.map(Optional::get)
			.collect(Collectors.toList());
	}


}
