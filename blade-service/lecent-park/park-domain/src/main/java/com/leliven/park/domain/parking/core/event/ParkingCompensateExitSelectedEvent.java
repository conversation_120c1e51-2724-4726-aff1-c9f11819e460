package com.leliven.park.domain.parking.core.event;

import com.leliven.park.domain.parking.core.model.objectvalue.ParkingCompensateExitInfo;
import com.leliven.ddd.core.event.AbstractPayloadDomainEvent;

import javax.annotation.Nonnull;

/**
 * 特殊出场已选择事件
 *
 * <AUTHOR>
 */
public class ParkingCompensateExitSelectedEvent extends AbstractPayloadDomainEvent<ParkingCompensateExitInfo> {

	/**
	 * Create a new DomainEvent.
	 *
	 * @param payload   the payload object (never {@code null})
	 */
	protected ParkingCompensateExitSelectedEvent(@Nonnull ParkingCompensateExitInfo payload) {
		super(payload);
	}

	/**
	 * 创建事件
	 *
	 * @param payload 事件载荷
	 * @return ParkingCompensateExitSelectedEvent
	 */
	public static ParkingCompensateExitSelectedEvent create(@Nonnull ParkingCompensateExitInfo payload) {
		return new ParkingCompensateExitSelectedEvent(payload);
	}

}
