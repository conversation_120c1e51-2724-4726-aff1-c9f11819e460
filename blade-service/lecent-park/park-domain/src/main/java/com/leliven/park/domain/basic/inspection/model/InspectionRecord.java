package com.leliven.park.domain.basic.inspection.model;

import com.leliven.park.domain.basic.inspection.model.valueobject.*;
import com.leliven.ddd.core.model.TenantDomain;
import com.leliven.ddd.core.valueobject.SimpleLocation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * 巡检记录领域对象
 * 代表一次实际巡检上报的结果
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InspectionRecord extends TenantDomain {
    /**
     * 巡检任务ID
     */
    private Long taskId;

    /**
     * 巡检子任务ID
     */
    private Long subTaskId;

    /**
     * 巡检任务项ID
     */
    private Long taskItemId;

    /**
     * 所属车场ID
     */
    private Long parklotId;

    /**
     * 巡检对象
     */
    private InspectionObject object;

    /**
     * 巡检项id
     */
    private Long itemId;

    /**
     * 巡检人
     */
    private Inspector inspector;

    /**
     * 巡检时间
     */
    private LocalDateTime inspectionTime;

    /**
     * 巡检类型
     */
    private InspectionType inspectionType;

    /**
     * 巡检来源
     */
    private InspectionSource inspectionSource;

    /**
     * 巡检结果状态
     *
     * @see InspectionResultStatus#getValue()
     */
    private InspectionResultStatus resultStatus;

    /**
     * 异常信息
     */
    private InspectionAbnormalInfo abnormalInfo;

    /**
     * 附加信息
     */
    @ApiModelProperty(value = "附加信息")
    private Map<String, String> additionalInfo;

    /**
     * 地理位置信息
     */
    private SimpleLocation location;

    /**
     * 判断该记录是否需要创建工单
     *
     * @return 是否需要创建工单
     */
    public boolean needsWorkOrder() {
        return isAbnormal()
            && this.abnormalInfo != null
            && this.abnormalInfo.isCreateWorkOrderEnable();
    }

    /**
     * 判断该记录是否异常
     *
     * @return 是否异常 {@code true} 异常 {@code false} 正常
     */
    public boolean isAbnormal() {
        return Objects.equals(InspectionResultStatus.ABNORMAL, resultStatus);
    }

    /**
     * 获取异常现象ID
     *
     * @return 异常现象ID {@link Long}
     */
    public Long getAbnormalPhenomenonId() {
        return this.abnormalInfo != null ? this.abnormalInfo.getPhenomenonId() : null;
    }

    /**
     * 获取巡检人员ID
     *
     * @return 巡检人员ID {@link Long}
     */
    public Long getInspectorId() {
        return inspector != null ? inspector.getInspectorId() : null;
    }

    /**
     * 获取巡检人员名称
     *
     * @return 巡检人员名称 {@link String}
     */
    public String getInspectorName() {
        return inspector != null ? inspector.getInspectorName() : "";
    }


}
