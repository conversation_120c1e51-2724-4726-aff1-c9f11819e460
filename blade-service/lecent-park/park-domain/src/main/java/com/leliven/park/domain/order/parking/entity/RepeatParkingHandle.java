package com.leliven.park.domain.order.parking.entity;

import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 进场覆盖处理
 *
 * <AUTHOR>
 * @date 2023/11/02
 */
@Getter
@AllArgsConstructor
public enum RepeatParkingHandle implements DomainEventType {
	/**
	 * 进场
	 */
	ENTERED("进场"),
	/**
	 * 覆盖
	 */
	COVERED("覆盖"),
	/**
	 * 清除
	 */
	CLEARED("清除")
	;

	private final String desc;

}
