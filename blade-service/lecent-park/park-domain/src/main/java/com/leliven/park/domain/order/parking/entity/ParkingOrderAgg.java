package com.leliven.park.domain.order.parking.entity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.entity.TimeNode;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.DateUtil;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 停车订单聚合
 *
 * <AUTHOR>
 */
@Data
public class ParkingOrderAgg extends TenantDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 停车场ID
	 */
	private Long parklotId;
	/**
	 * 车位Id
	 */
	private Long placeId;
	/**
	 * 进场信息
	 */
	private ParkingEventInfo enterInfo;
	/**
	 * 出场信息
	 */
	private ParkingEventInfo exitInfo;
	/**
	 * 停车状态
	 * <p>
	 * {@link com.lecent.park.en.parklot.ParkingStatusEnum}
	 */
	private ParkingStatusEnum parkingStatus;
	/**
	 * 计费开始时间
	 */
	private Date billingStartTime;
	/**
	 * 停车费用
	 */
	private ParkingCost parkingCost;
	/**
	 * 停车时长
	 */
	private Duration duration;
	/**
	 * 停车时长描述
	 * 格式： xx天xx小时xx分钟
	 */
	private String durationTime;
	/**
	 * 停车时间表
	 * <p>
	 * {@link ParkingSchedule}
	 */
	private ParkingSchedule parkingSchedule;
	/**
	 * 授权卡关联信息
	 * <p>
	 * {@link AuthCardRelation}
	 */
	private AuthCardRelation authCardRelation;
	/**
	 * 是否车位
	 */
	private boolean occupied;
	/**
	 * 收费规则ID
	 */
	private Long chargeRuleId;
	/**
	 * 缴费方式，多个id,逗号隔开
	 */
	private String payTypes;
	/**
	 * 优惠券，多个id,逗号隔开
	 */
	private String couponIds;
	/**
	 * 异常
	 * 0 无异常
	 * 1 重复入场 详情请看targetParkingId
	 */
	private String reasonIds;
	/**
	 * 目标ID
	 * 重复进场状态时-指向最新进场记录
	 */
	private Long targetParkingId;
	/**
	 * 目标ID
	 * 重复进场状态时-指向最新进场记录
	 */
	private ParkingOrderAgg targetParkingOrder;
	/**
	 * 临停标识 0-否 1-是
	 */
	private boolean tempCar;
	/**
	 * 备注，描述
	 */
	private String memo;
	/**
	 * 预约订单号
	 */
	private String reserveOrderId;
	/**
	 * 其他标识 0:场内停车,1:路边停车
	 */
	private Integer otherFlag;
	/**
	 * TODO 未使用
	 * 优惠券对应的商户，多个id,逗号隔开
	 */
	private String merchantIds;
	/**
	 * TODO 未使用
	 * <p>
	 * 版本
	 */
	private Integer version;
	/**
	 * TODO 未使用
	 * 付款订单，多个id,逗号隔开
	 */
	private String orderIds;
	/**
	 * TODO 未使用
	 * 进场确认;false:未确认,true:已确认
	 */
	private boolean enterConfirm;
	/**
	 * TODO 未使用
	 * <p>
	 * 0 无异常
	 * 1 有异常未处理
	 * 2 有异常已处理
	 */
	private Integer errorStatus;

	public ParkingOrderAgg() {
		this.duration = Duration.ZERO;
		this.parkingCost = ParkingCost.create();
		this.parkingSchedule = ParkingSchedule.create();
	}

	public ParkingOrderAgg(ParkingEventInfo enterEvent) {
		super(IdWorker.getId());
		this.enterInfo = enterEvent;
		this.duration = Duration.ZERO;
		this.parkingCost = ParkingCost.create();
		this.parkingStatus = ParkingStatusEnum.PARK_IN;
		this.billingStartTime = enterEvent.getDatetime();
		this.parkingSchedule = ParkingSchedule.create();
	}

	/**
	 * 创建停车订单
	 *
	 * @param enterEvent 进场事件
	 * @return 停车订单
	 */
	public ParkingOrderAgg create(ParkingEventInfo enterEvent) {
		this.parkingStatus = ParkingStatusEnum.PARK_IN;
		this.enterInfo = enterEvent;
		this.billingStartTime = enterEvent.getDatetime();
		return this;
	}

	/**
	 * 变更进场信息
	 *
	 * @param enterEvent 进场事件
	 */
	public void changeEnterInfo(ParkingEventInfo enterEvent) {
		if (Objects.nonNull(enterEvent.getDatetime())) {
			this.billingStartTime = enterEvent.getDatetime();
			this.enterInfo.setDatetime(enterEvent.getDatetime());
		}
		if (Objects.nonNull(enterEvent.getTriggerWay())) {
			this.enterInfo.setTriggerWay(enterEvent.getTriggerWay());
		}
		if (Objects.nonNull(enterEvent.getImageUrl())) {
			this.enterInfo.setImageUrl(enterEvent.getImageUrl());
		}
		if (Objects.nonNull(enterEvent.getImageCollection())) {
			this.enterInfo.setImageCollection(enterEvent.getImageCollection());
		}
		if (Objects.nonNull(enterEvent.getVehicle())) {
			this.enterInfo.setVehicle(enterEvent.getVehicle());
		}
		if (Objects.nonNull(enterEvent.getTodoId())) {
			this.enterInfo.setTodoId(enterEvent.getTodoId());
		}
		if (Objects.nonNull(enterEvent.getChannelId())) {
			this.enterInfo.setChannelId(enterEvent.getChannelId());
		}
		if (Objects.nonNull(enterEvent.getOpenGateCost())) {
			this.enterInfo.setOpenGateCost(enterEvent.getOpenGateCost());
		}
		if (Objects.nonNull(enterEvent.getOptUserId())) {
			this.enterInfo.setOptUserId(enterEvent.getOptUserId());
		}
	}

	/**
	 * 出场
	 *
	 * @param exitInfo 出场事件
	 */
	public void exit(ParkingEventInfo exitInfo) {
		this.exitInfo = exitInfo;
		this.parkingStatus = ParkingStatusEnum.PARK_OUT;
	}

	/**
	 * 覆盖出场
	 *
	 * @param latestOrder 最新订单
	 * @return 停车订单
	 */
	public ParkingOrderAgg coverExit(ParkingOrderAgg latestOrder) {
		ParkingEventInfo latestEnterInfo = latestOrder.getEnterInfo();

		ParkingEventInfo coveredExitInfo = new ParkingEventInfo();
		coveredExitInfo.setDatetime(latestEnterInfo.getDatetime());
		coveredExitInfo.setTriggerWay(ParkingTriggerWay.ENTER_COVER);

		this.exitInfo = coveredExitInfo;
		this.targetParkingId = latestOrder.getId();
		this.parkingStatus = ParkingStatusEnum.PARK_REPEAT;
		return this;
	}

	/**
	 * 占用车位
	 *
	 * @param relationId 授权卡关联ID
	 */
	public void occupiedParking(Long relationId) {
		this.occupied = true;
		if (this.authCardRelation == null) {
			this.authCardRelation = new AuthCardRelation();
		}
		this.authCardRelation.changeRelationId(relationId);
	}

	/**
	 * 释放车位
	 *
	 * @param enterInfo 进场信息
	 */
	public void releaseOccupiedParking(ParkingEventInfo enterInfo) {
		this.occupied = false;
		this.billingStartTime = enterInfo.getDatetime();
		this.parkingSchedule.addParkingTimeNode(
			// TODO 后续提出去，这里分前车或后车释放车位
			new TimeNode(
				DateUtil.formatDateTime(enterInfo.getDatetime()),
				"释放车位，开始计费",
				"循环车按前车收费,后车：" + enterInfo.getVehicle().getPlate() + "进来时，释放车位给后车",
				true)
		);
	}

	/**
	 * 是否在场
	 *
	 * @return true-在场 false-不在场
	 */
	public boolean isPresent() {
		return Optional.ofNullable(this.parkingStatus)
			.map(ParkingStatusEnum::isPresent)
			.orElseThrow(() -> new ServiceException("业务异常：停车状态为空"));
	}

	/**
	 * 是否出场
	 *
	 * @return true-在场 false-不在场
	 */
	public boolean isExit() {
		return Optional.ofNullable(this.parkingStatus)
			.map(ParkingStatusEnum::isExited)
			.orElseThrow(() -> new ServiceException("业务异常：停车状态为空"));
	}

	/**
	 * 判断给定的日期是否处于免费时段内
	 *
	 * @param freeDuration 免费时段的分钟数
	 * @param checkDate    要判断的日期
	 * @return true-在免费时段内 false-不在免费时段内
	 */
	public boolean isDateWithinFreeDuration(int freeDuration, Date checkDate) {
		Date freeEndDate = DateUtil.plusMinutes(this.enterInfo.getDatetime(), freeDuration);
		return freeEndDate.getTime() >= checkDate.getTime();
	}

	/**
	 * 是否相同订单ID
	 *
	 * @param otherOrder 其他订单
	 * @return true-相同 false-不相同
	 */
	public boolean hasSameId(ParkingOrderAgg otherOrder) {
		return Optional.ofNullable(otherOrder)
			.map(o -> this.getId().equals(o.getId()))
			.orElse(false);
	}

	/**
	 * 是否路边停车订单
	 *
	 * @return true-是 false-否
	 */
	public boolean isOnRoadOrder() {
		return Objects.nonNull(this.otherFlag) && this.otherFlag == 1;
	}

	/**
	 * 是否支付过
	 *
	 * @return true-支付过 false-未支付
	 */
	public boolean hasBeenPaid() {
		return this.parkingCost.hasBeenPaid();
	}


	@Override
	public int hashCode() {
		return Objects.hash(getId());
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null || getClass() != obj.getClass()) {
			return false;
		}
		ParkingOrderAgg other = (ParkingOrderAgg) obj;
		return Objects.equals(getId(), other.getId());
	}
}
