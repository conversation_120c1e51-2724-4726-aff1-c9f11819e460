package com.leliven.park.domain.parking.core.support.factory;

import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.order.parking.entity.ParkingOrderAgg;
import com.leliven.park.domain.parking.core.specification.EntryFreeDurationSpec;
import com.leliven.park.domain.parking.core.specification.PaidFreeLeaveDurationSpec;
import com.leliven.park.domain.parking.core.specification.ParkingFreeDurationSpec;
import com.leliven.ddd.core.annotations.DomainService;
import com.leliven.ddd.core.specification.Specification;

/**
 * 停车免费时长规约工厂
 *
 * <AUTHOR>
 */
@DomainService
public class ParkingFreeDurationSpecFactory {

	/**
	 * 创建入场免费时长规约
	 *
	 * <p>
	 * 用于校验停车订单是否在入场免费时长内
	 * </p>
	 *
	 * @param chargeRegulation 临时停车收费规则 {@link TempParkingBillingRuleCfg}
	 * @return 入场免费时长规约 {@link Specification}<{@link ParkingOrderAgg}>
	 */
	public ParkingFreeDurationSpec createEntryFreeDurationSpec(TempParkingBillingRuleCfg chargeRegulation) {
		return new EntryFreeDurationSpec(chargeRegulation.getFreeDuration());
	}

	/**
	 * 创建提前支付免费离场时长规约
	 *
	 * @param chargeRegulation 临时停车收费规则 {@link TempParkingBillingRuleCfg}
	 * @return 提前支付免费离场时长规约 {@link Specification}<{@link ParkingOrderAgg}>
	 */
	public ParkingFreeDurationSpec createPaidFreeLeaveDurationSpec(TempParkingBillingRuleCfg chargeRegulation) {
		return new PaidFreeLeaveDurationSpec(chargeRegulation.getFreeLeaveDurationAfterPaid());
	}

}
