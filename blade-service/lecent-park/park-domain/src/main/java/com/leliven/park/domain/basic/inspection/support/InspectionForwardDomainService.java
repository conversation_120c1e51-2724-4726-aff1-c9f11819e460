package com.leliven.park.domain.basic.inspection.support;

import com.leliven.park.domain.basic.inspection.InspectionForwardRecordGateway;
import com.leliven.park.domain.basic.inspection.InspectionPhenomenonGateway;
import com.leliven.park.domain.basic.inspection.event.InspectionRecordBatchCreatedEvent;
import com.leliven.park.domain.basic.inspection.model.InspectionForwardContext;
import com.leliven.park.domain.basic.inspection.model.InspectionForwardRecord;
import com.leliven.park.domain.basic.inspection.model.InspectionPhenomenon;
import com.leliven.park.domain.basic.inspection.model.InspectionRecord;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionPhenomenonForwardRule;
import com.leliven.park.domain.basic.inspection.support.forward.InspectionForwardDispatcher;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 巡检转发领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class InspectionForwardDomainService {

    private final InspectionForwardDispatcher forwardDispatcher;
    private final InspectionPhenomenonGateway phenomenonGateway;
    private final InspectionForwardRecordGateway forwardRecordGateway;

    /**
     * 处理巡检记录转发事件
     *
     * @param event 转发事件
     */
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleForwardEvent(InspectionRecordBatchCreatedEvent event) {
        // 创建转发上下文
        List<InspectionForwardContext> forwardContexts = createForwardContexts(event.getPayload());

        if (!forwardContexts.isEmpty()) {
            // 执行转发
            forwardDispatcher.dispatch(forwardContexts);
        }
    }

    /**
     * 创建转发上下文
     *
     * @param inspectionRecords 巡检记录列表 {@link List}<{@link InspectionRecord}>
     * @return 转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     */
    private List<InspectionForwardContext> createForwardContexts(List<InspectionRecord> inspectionRecords) {
        // 筛选出异常记录
        List<InspectionRecord> abnormalRecords = filterAbnormalRecords(inspectionRecords);

        if (abnormalRecords.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取现象信息分组
        Map<Long, InspectionPhenomenon> phenomenonGroup = getPhenomenonGroup(abnormalRecords);

        // 为每个异常记录创建转发上下文
        return createForwardContextsForAbnormalRecords(abnormalRecords, phenomenonGroup);
    }

    /**
     * 筛选异常记录
     *
     * @param inspectionRecords 巡检记录列表 {@link List}<{@link InspectionRecord}>
     * @return 异常记录列表 {@link List}<{@link InspectionRecord}>
     */
    private List<InspectionRecord> filterAbnormalRecords(List<InspectionRecord> inspectionRecords) {
        return inspectionRecords.stream()
            .filter(InspectionRecord::isAbnormal)
            .collect(Collectors.toList());
    }

    /**
     * 为异常记录创建转发上下文
     *
     * @param abnormalRecords 异常记录列表 {@link List}<{@link InspectionRecord}>
     * @param phenomenonGroup 现象信息分组 {@link Map}<{@link Long}, {@link InspectionPhenomenon}>
     * @return 转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     */
    private List<InspectionForwardContext> createForwardContextsForAbnormalRecords(
            List<InspectionRecord> abnormalRecords,
            Map<Long, InspectionPhenomenon> phenomenonGroup) {

        List<InspectionForwardContext> contexts = new ArrayList<>();
        List<Long> missingPhenomenonIds = new ArrayList<>();

        for (InspectionRecord abnormalRecord : abnormalRecords) {
            InspectionPhenomenon phenomenon = phenomenonGroup.get(abnormalRecord.getAbnormalPhenomenonId());

            if (Objects.isNull(phenomenon)) {
                missingPhenomenonIds.add(abnormalRecord.getAbnormalPhenomenonId());
                continue;
            }

            // 为每个转发规则创建转发上下文
            contexts.addAll(createContextsForPhenomenonRules(abnormalRecord, phenomenon));
        }

        // 批量记录缺失的现象ID
        if (!missingPhenomenonIds.isEmpty()) {
            log.warn("未找到巡检现象，IDs: {}", missingPhenomenonIds);
        }

        return contexts;
    }

    /**
     * 为现象规则创建转发上下文
     *
     * @param abnormalRecord 异常记录 {@link InspectionRecord}
     * @param phenomenon 现象信息 {@link InspectionPhenomenon}
     * @return 转发上下文列表 {@link List}<{@link InspectionForwardContext}>
     */
    private List<InspectionForwardContext> createContextsForPhenomenonRules(
            InspectionRecord abnormalRecord,
            InspectionPhenomenon phenomenon) {

        return phenomenon.getForwardRules().stream()
            .filter(rule -> shouldCreateContext(abnormalRecord, rule))
            .map(rule -> InspectionForwardContext.create(abnormalRecord, rule))
            .collect(Collectors.toList());
    }

    /**
     * 判断是否应该创建转发上下文
     *
     * @param abnormalRecord 异常记录 {@link InspectionRecord}
     * @param rule 转发规则 {@link InspectionPhenomenonForwardRule}
     * @return 是否应该创建转发上下文 {@code true} 是 {@code false} 否
     */
    private boolean shouldCreateContext(InspectionRecord abnormalRecord, InspectionPhenomenonForwardRule rule) {
        // 如果是工单目标类型但不需要工单，则跳过
        return !(rule.isWorkOrderTargetType() && !abnormalRecord.needsWorkOrder());
    }

    /**
     * 获取现象信息分组
     *
     * <p>
     * {@code key} 现象ID
     * {@code value} 现象信息
     * </p>
     *
     * @param inspectionRecords 巡检记录列表
     * @return 现象信息分组 {@link Map}<{@link Long}, {@link InspectionPhenomenon}>
     */
    private Map<Long, InspectionPhenomenon> getPhenomenonGroup(List<InspectionRecord> inspectionRecords) {
        // 获取现象ID列表
        List<Long> phenomenonIds = inspectionRecords.stream()
            .map(InspectionRecord::getAbnormalPhenomenonId)
            .distinct()
            .collect(Collectors.toList());

        // 如果现象ID列表为空，直接返回空Map
        if (phenomenonIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return phenomenonGateway.list(phenomenonIds).stream()
            .collect(Collectors.toMap(InspectionPhenomenon::getId, Function.identity()));
    }


    /**
     * 创建转发记录
     *
     * @param forwardContexts 转发上下文列表
     */
    private void createForwardRecords(List<InspectionForwardContext> forwardContexts) {
        List<InspectionForwardRecord> forwardRecords = forwardContexts.stream()
            .map(InspectionForwardContext::toForwardRecord)
            .collect(Collectors.toList());
        forwardRecordGateway.batchSave(forwardRecords);
    }


}
