package com.leliven.park.domain.basic.place.support;

import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.ddd.core.annotations.DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 停车位领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@DomainService
@RequiredArgsConstructor
public class ParkingPlaceDomainService {

	private final ParkingPlaceGateway parkingPlaceGateway;


	public ParkingSpace get(Long id) {
		return this.parkingPlaceGateway.getById(id);
	}

}
