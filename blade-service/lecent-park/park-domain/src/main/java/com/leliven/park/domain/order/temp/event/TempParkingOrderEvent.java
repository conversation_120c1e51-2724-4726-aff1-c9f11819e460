package com.leliven.park.domain.order.temp.event;

import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import lombok.ToString;

/**
 * 临停订单领域事件
 *
 * <AUTHOR>
 */
@ToString(callSuper = true)
public class TempParkingOrderEvent extends AbstractDomainEvent<TempParkingOrderDomain, TempParkingOrderEventType> {

	private static final long serialVersionUID = 4096507652333220941L;

	public TempParkingOrderEvent(Object source,
								 TempParkingOrderEventType eventType,
								 TempParkingOrderDomain payload) {
		super(source, eventType, payload);
	}
}
