package com.lecent.park.discount.charging.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.lecent.park.discount.charging.controller.xiangqianchong.ReqXqcBody;
import com.lecent.park.discount.charging.controller.xiangqianchong.ResXqcBody;
import com.lecent.park.discount.charging.entity.ParklotChargingDiscountConfig;
import com.lecent.park.discount.charging.service.IParklotChargingDiscountConfigService;
import com.lecent.park.discount.charging.service.IParklotChargingOrderService;
import com.lecent.park.en.coupon.ChargingFactory;
import com.lecent.park.en.coupon.ChargingFactoryDiscountType;
import com.lecent.park.entity.BaseAppConfig;
import com.lecent.park.entity.ParklotChargingOrder;
import com.lecent.park.service.IBaseAppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.exception.VerifySignException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.TreeMap;

/**
 * 向黔冲充电处理
 *
 * <AUTHOR>
 * @date 2022年06月08日 11:57
 */
@Slf4j
@Service
@RefreshScope
public class XiangqichongService {

	private static final String SIGN_PARAM_SIGN = "sign";
	//private static final String SIGN_PARAM_APP_ID = "appId";原本是去除appId（实际为车场id） 为避免歧义，接收参数时更改为parklotId
	private static final String SIGN_PARAM_APP_ID = "parklotId";
	private static final String MD5_ALGORITHM = "MD5";
	private static final int BYTE_MASK = 0xff;
	private static final int HEX_THRESHOLD = 16;

	@Resource
	private IParklotChargingOrderService chargingOrderService;

	@Resource
	private IParklotChargingDiscountConfigService discountConfigService;

	@Resource
	private IBaseAppConfigService appConfigService;

	/**
	 * 接收充电订单通知
	 *
	 * @param req 充电订单请求体
	 * @return 响应结果
	 */
	public ResXqcBody notificationParkOrderInfo(ReqXqcBody req) {
		// 查询充电配置
		ParklotChargingDiscountConfig discountConfig = discountConfigService.queryConfigByOtherParkLotId(req.getParklotId().toString());
		if (discountConfig == null) {
			log.warn("车场充电配置信息不存在，appId: {}", req.getParklotId());
			return ResXqcBody.fail(String.valueOf(ResultCode.FAILURE.getCode()), "车场充电配置信息不存在");
		}

		// 验证请求签名
		verifyReq(req, discountConfig.getBaseAppId());

		// 构建充电订单对象
		ParklotChargingOrder chargingOrder = buildChargingOrder(req,discountConfig.getParklotId());

		// 保存订单
		chargingOrderService.addOrder(chargingOrder);

		// 返回参数
		JSONObject responseJson = new JSONObject()
			.fluentPut("orderNo", req.getOrderNo());

		return ResXqcBody.ok(responseJson.toJSONString());
	}

	/**
	 * 构建充电订单对象

	 */
	private ParklotChargingOrder buildChargingOrder(ReqXqcBody req,Long parklotId) {
		return ParklotChargingOrder.builder()
			.stationId(req.getStationId())
			.stationName(req.getStationName())
			.parklotId(parklotId)
			.thirdParklotId(req.getParklotId().toString())
			.tradeNo(req.getOrderNo())
			.plate(req.getPlateNo())
			.startTime(DateUtil.parse(req.getStartTime(), DatePattern.NORM_DATETIME_PATTERN))
			.endTime(DateUtil.parse(req.getEndTime(), DatePattern.NORM_DATETIME_PATTERN))
			.kwh(BigDecimal.valueOf(req.getPower()))
			.totalMoney(convertAmount(req.getTotalMoney()))
			.freeParkingCost(convertAmount(req.getCouponMoney()))
			.freeParkingTimes(req.getCouponTime())
			.chargingFactoryDiscountType(ChargingFactoryDiscountType.DEFAULT)
			.sourceData(JSONObject.toJSONString(req))
			.chargingFactory(ChargingFactory.XQC)
			.build();
	}


	private BigDecimal convertAmount(Integer amount) {
		if(amount == null){
			return null;
		}
		return BigDecimal.valueOf(amount)
			.divide(BigDecimal.valueOf(100))
			.setScale(2, RoundingMode.HALF_DOWN);
	}

	/**
	 * 验证请求
	 *
	 * @param req 数据
	 * @throws VerifySignException 签名验证失败时抛出
	 * @throws RuntimeException    重复提交时抛出
	 */
	private void verifyReq(ReqXqcBody req, Long appId) {

		String parkId = req.getParklotId().toString();
		log.info("接收充电数据,车场编号={}，车牌={}", parkId, req.getPlateNo());

		BaseAppConfig config = appConfigService.getById(appId);
		if (config == null) {
			throw new ServiceException("运营商信息未在停车平台进行配置");
		}

		// 验证签名
		String sign = paramsSign(JSONObject.parseObject(JSONObject.toJSONString(req)), config.getAppSecret());
		if (!req.getSign().equals(sign)) {
			log.error("签名验证失败 req="+ Func.toJson(req));
			log.error("签名验证失败 sign="+ sign +"appSecret:"+config.getAppSecret());
			//throw new VerifySignException("签名验证失败");
		}
	}


	/**
	 * 参数签名
	 * 示例：
	 * 参数对象：{"mobile":"13700000000","rechargeCardId":"632825472899887104","companyId":"******************","appId":"EED96C219E83450A","ts":"1583744086841"}
	 * url拼接：companyId=******************&mobile=13700000000&rechargeCardId=632825472899887104&ts=1583744086841&85d15350778b11e9bbaa506b4b2f6421
	 * 签名结果：8B3FCE1E1B5A4AE8B0691A0C7D39EDBC
	 *
	 * @param requestBody 参数对象
	 * @param appSecret   秘钥
	 * @return 签名字符串
	 */
	public static String paramsSign(JSONObject requestBody, String appSecret) {
		TreeMap<String, String> params = new TreeMap<>();

		// 过滤掉sign，appId字段，空属性及Map或List等复杂对象
		requestBody.entrySet().stream()
			.filter(entry -> !SIGN_PARAM_SIGN.equals(entry.getKey())
				&& !SIGN_PARAM_APP_ID.equals(entry.getKey())
				&& entry.getValue() != null
				&& !(entry.getValue() instanceof java.util.Map)
				&& !(entry.getValue() instanceof Iterable)
				&& !entry.getValue().toString().isEmpty())
			.forEach(entry -> {
				// 对power字段特殊处理，保留两位小数
				if ("power".equals(entry.getKey()) && entry.getValue() instanceof Number) {
					params.put(entry.getKey(), String.format("%.2f", ((Number) entry.getValue()).doubleValue()));
				} else {
					params.put(entry.getKey(), entry.getValue().toString());
				}
			});

		// 拼接appSecret
		String temp = Joiner.on("&").withKeyValueSeparator("=").join(params).concat("&").concat(appSecret);
		return md5(temp).toUpperCase();
	}

	/**
	 * 对文本执行 md5 摘要加密, 此算法与 mysql,JavaScript生成的md5摘要进行过一致性对比.
	 *
	 * @param plainText 明文
	 * @return 返回值中的字母为小写
	 */
	private static String md5(String plainText) {
		if (plainText == null) {
			plainText = "";
		}

		try {
			MessageDigest md = MessageDigest.getInstance(MD5_ALGORITHM);
			md.update(plainText.getBytes(StandardCharsets.UTF_8));
			byte[] digest = md.digest();

			StringBuilder builder = new StringBuilder(digest.length * 2);
			for (byte b : digest) {
				int value = b & BYTE_MASK;
				if (value < HEX_THRESHOLD) {
					builder.append("0");
				}
				builder.append(Integer.toHexString(value));
			}
			return builder.toString();
		} catch (NoSuchAlgorithmException e) {
			log.error("MD5算法不可用", e);
			throw new RuntimeException("MD5算法不可用", e);
		}
	}
}
