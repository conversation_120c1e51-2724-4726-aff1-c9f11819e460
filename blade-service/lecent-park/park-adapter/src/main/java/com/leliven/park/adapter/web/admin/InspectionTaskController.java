package com.leliven.park.adapter.web.admin;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.park.application.basic.dto.command.InspectionTaskChangeExecutorsCommand;
import com.leliven.park.application.basic.service.InspectionTaskAppService;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.InspectionTaskQuery;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.InspectionTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

/**
 * 巡检任务管理接口
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/inspection/task")
@Api(value = "巡检任务管理", tags = "巡检任务管理")
public class InspectionTaskController {

    private final InspectionTaskAppService inspectionTaskAppService;

    @Slave
    @GetMapping("/page")
    @ApiOperation(value = "分页查询巡检任务", notes = "根据查询条件分页获取巡检任务列表")
    public R<IPage<InspectionTaskVO>> page(InspectionTaskQuery query) {
        return R.data(inspectionTaskAppService.page(query));
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "查询巡检任务详情", notes = "根据ID获取巡检任务详情")
    public R<InspectionTaskVO> detail(@PathVariable("id") @ApiParam(value = "巡检任务ID", required = true) Long id) {
        return R.data(inspectionTaskAppService.detail(id));
    }

    /**
     * 根据任务id生成任务项
     *
     * @param id 任务id
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    @PostMapping("/{id}/generate-task-items")
    @ApiOperation(value = "根据任务id生成任务项", notes = "根据任务id生成任务项")
    public R<Boolean> generateTaskItems(@PathVariable("id") @ApiParam(value = "巡检任务ID", required = true) Long id) {
        return R.data(inspectionTaskAppService.generateTaskItems(id));
    }

    /**
     * 将任务状态变更为进行中
     *
     * @param id 任务ID
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    @PostMapping("/{id}/start")
    @ApiOperation(value = "开始任务", notes = "手动将巡检任务状态从未开始变更为进行中")
    public R<Boolean> startTask(@PathVariable("id") @ApiParam(value = "巡检任务ID", required = true) Long id) {
        return R.data(inspectionTaskAppService.startTask(id));
    }

    /**
     * 取消任务
     *
     * @param id 任务ID
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    @PostMapping("/{id}/cancel")
    @ApiOperation(value = "取消任务", notes = "取消巡检任务，同时取消对应的子任务")
    public R<Boolean> cancelTask(@PathVariable("id") @ApiParam(value = "巡检任务ID", required = true) Long id) {
        return R.data(inspectionTaskAppService.cancelTask(id));
    }

    /**
     * 变更任务执行人
     *
     * @param id 任务ID
     * @param command 变更任务执行人命令
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    @PostMapping("/{id}/inspectors")
    @ApiOperation(value = "变更任务执行人", notes = "变更巡检任务的执行人，只有未完成的任务才能变更")
    public R<Boolean> changeTaskExecutors(
            @PathVariable("id") @ApiParam(value = "巡检任务ID", required = true) Long id,
            @RequestBody @ApiParam(value = "变更任务执行人命令", required = true) InspectionTaskChangeExecutorsCommand command) {
        // 设置任务ID
        command.setTaskId(id);
        return R.data(inspectionTaskAppService.changeTaskExecutors(command));
    }
}
