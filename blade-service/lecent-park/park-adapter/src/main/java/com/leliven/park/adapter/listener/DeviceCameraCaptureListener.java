package com.leliven.park.adapter.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.event.DeviceCameraCaptureEvent;
import com.lecent.park.core.mq.rabbitmq.config.RabbitMQConfig;
import com.lecent.park.core.mq.rabbitmq.utils.MessageConverter;
import com.lecent.park.core.notify.constant.MsgConstant;
import com.lecent.park.core.notify.domain.MsgRequest;
import com.leliven.park.application.basic.assembler.CameraCaptureAssembler;
import com.leliven.park.application.basic.service.ParkingPlaceAppService;
import com.leliven.park.domain.basic.place.event.ParkingPlaceCameraCaptureDomainEvent;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureType;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 摄像头设备抓拍监听器
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@Slf4j
@Component
public class DeviceCameraCaptureListener {

    /**
     * 摄像头设备定时抓拍队列（用于批量保存定时抓拍记录）
     */
    private static final String QUEUE_CAMERA_CAPTURE_TIMED = "queue.leliven-park.parking-space.camera.capture";
	/**
	 * 摄像头设备定时抓拍队列（用于发送秒图领域事件，处理其他依赖抓拍记录的业务）
	 */
	private static final String QUEUE_CAMERA_CAPTURE_TIMED_BIZ = "queue.leliven-park.parking-space.camera.capture.biz";
    /**
     * 停车设备进出场抓拍队列
     */
    private static final String QUEUE_CAMERA_CAPTURE_PARKING = "queue.leliven-park.parking-space.camera.capture.parking";
    /**
     * 停车设备进出场事件路由
     */
    private static final String ROUTING_KEY_PARKING = "leliven.notify.routing.key.v2.lecent-device.event.enter.exit";


	private final CameraCaptureAssembler cameraCaptureAssembler;
    private final ParkingPlaceAppService parkingPlaceAppService;

    public DeviceCameraCaptureListener(CameraCaptureAssembler cameraCaptureAssembler,
									   ParkingPlaceAppService parkingPlaceAppService) {
		this.cameraCaptureAssembler = cameraCaptureAssembler;
		this.parkingPlaceAppService = parkingPlaceAppService;
    }

	/**
	 * 处理摄像头设备定时抓拍事件（专用于批量保存定时抓拍记录，勿处理其他业务）
	 *
	 * @param messages 摄像头设备定时抓拍事件消息
	 */
	@Trace(operationName = "DeviceCameraCaptureListener.onDeviceCameraCaptureTimedEvent")
    @RabbitListener(
        bindings = {@QueueBinding(
            value = @Queue(value = QUEUE_CAMERA_CAPTURE_TIMED, durable = "false"),
            exchange = @Exchange(
                value = DeviceMQConstant.EXCHANGE,
                type = ExchangeTypes.TOPIC),
            key = DeviceMQConstant.RoutingKey.CAMERA_CAPTURE_TIMED)
        }, containerFactory = RabbitMQConfig.BATCH_LISTENER_CONTAINER_FACTORY_NAME)
    public void onDeviceCameraCaptureTimedEvent(List<Message> messages) {
		try {
			List<DeviceCameraCaptureEvent> event = MessageConverter.from(messages, DeviceCameraCaptureEvent.class);
			this.parkingPlaceAppService.onDeviceCameraCaptureTimedEvent(event);
		} catch (Exception e) {
			log.warn("处理摄像头定时抓拍事件失败：", e);
		}
	}

	/**
	 * 处理摄像头设备定时抓拍事件（用于发送定时抓拍领域事件，处理其他依赖抓拍记录的业务）
	 *
	 * @param messages 摄像头设备定时抓拍事件消息
	 */
	@RabbitListener(
		bindings = {@QueueBinding(
			value = @Queue(value = QUEUE_CAMERA_CAPTURE_TIMED_BIZ, durable = "false"),
			exchange = @Exchange(
				value = DeviceMQConstant.EXCHANGE,
				type = ExchangeTypes.TOPIC),
			key = DeviceMQConstant.RoutingKey.CAMERA_CAPTURE_TIMED)
		})
	public void onCaptureTimedEventToProcessBiz(Message messages) {
		try {
			DeviceCameraCaptureEvent event = MessageConverter.from(messages, DeviceCameraCaptureEvent.class);
			cameraCaptureAssembler.fromDeviceCameraCaptureEvent(event).ifPresent(c ->
				SpringDomainEventPublisher.publish(
					ParkingPlaceCameraCaptureDomainEvent.ofCreated(this, ParkingPlaceCameraCaptureType.TIMED, c),
					false
				)
			);
		} catch (Exception e) {
			log.warn("onCaptureTimedEventToProcessBiz：", e);
		}
	}

    /**
	 * 处理设备停车事件
     *
     * <p>
     * 用于保存车位设备停车抓拍记录
     * </p>
	 *
	 * @param message 摄像头设备停车事件消息
	 */
	@Trace(operationName = "DeviceCameraCaptureListener.onDeviceParkingEvent")
    @RabbitListener(
        bindings = {@QueueBinding(
            value = @Queue(value = QUEUE_CAMERA_CAPTURE_PARKING, durable = "false"),
            exchange = @Exchange(
                value = MsgConstant.EXCHANGE,
                type = ExchangeTypes.TOPIC),
            key = ROUTING_KEY_PARKING)
        }, containerFactory = RabbitMQConfig.BATCH_LISTENER_CONTAINER_FACTORY_NAME)
    public void onDeviceParkingEvent(List<Message> message) {
        try {
            List<MsgRequest<DeviceParkingEventDTO>> msgRequests =
                MessageConverter.from(message, new TypeReference<MsgRequest<DeviceParkingEventDTO>>() {});
            this.parkingPlaceAppService.processCameraParkingCaptureEvents(msgRequests);
        } catch (Exception e) {
            log.warn("处理设备停车抓拍事件失败：", e);
        }
    }


}
