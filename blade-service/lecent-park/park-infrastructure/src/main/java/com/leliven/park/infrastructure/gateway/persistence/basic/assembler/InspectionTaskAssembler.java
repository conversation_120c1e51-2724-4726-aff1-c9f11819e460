package com.leliven.park.infrastructure.gateway.persistence.basic.assembler;


import com.leliven.park.domain.basic.inspection.model.InspectionTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskExecutor;
import com.leliven.park.infrastructure.gateway.persistence.basic.InspectionSubTaskGatewayReadable;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.dataobject.InspectionItemDO;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.InspectionItemMapper;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.InspectionTaskExecutorMapper;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.InspectionTaskExecutorVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.InspectionTaskVO;
import com.leliven.ddd.core.annotations.Assembler;

import lombok.RequiredArgsConstructor;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;

/**
 * 巡检任务装配器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class InspectionTaskAssembler {

	private final InspectionItemMapper itemMapper;
	private final InspectionAssembler inspectionAssembler;
	private final InspectionTaskExecutorMapper taskExecutorMapper;
	private final InspectionSubTaskGatewayReadable subTaskGatewayReadable;


    /**
     * 根据执行人ID列表创建执行人对象列表
     *
     * @param executorIds 执行人ID列表
     * @return 执行人对象列表
     */
    public List<InspectionTaskExecutor> toExecutors(List<Long> executorIds) {
        if (Func.isEmpty(executorIds)) {
            return Collections.emptyList();
        }

		return inspectionAssembler.toInspectors(executorIds)
			.stream()
			.map(inspector -> InspectionTaskExecutor.create(inspector.getInspectorId(), inspector.getInspectorName()))
			.collect(Collectors.toList());
    }

	/**
	 * 为任务视图列表列表填充关联信息
	 *
	 * @param taskViewList 任务视图列表
	 */
	public void fillTaskViewList(List<InspectionTaskVO> taskViewList) {
		if (Func.isEmpty(taskViewList)) {
            return;
        }

		// 搜集所有任务ID
		Set<Long> taskIds = new HashSet<>();
		// 收集所有任务中的巡检项ID
		Set<Long> allItemIds = new HashSet<>();
        for (InspectionTaskVO taskVO : taskViewList) {
            List<Long> itemIds = Func.toLongList(taskVO.getItemIds());
            if (!itemIds.isEmpty()) {
                allItemIds.addAll(itemIds);
            }
			taskIds.add(taskVO.getId());
        }

		Map<Long, String> itemIdToNameMap = Collections.emptyMap();

		List<InspectionItemDO> itemList = itemMapper.selectBatchIds(allItemIds);
        if (Func.isNotEmpty(itemList)) {
			itemIdToNameMap = itemList.stream().collect(
				Collectors.toMap(InspectionItemDO::getId, InspectionItemDO::getItemName, (v1, v2) -> v1));
		}

		Map<Long, List<InspectionTaskExecutorVO>> taskIdToTaskExecutorListMap = taskExecutorMapper.listViewObjectByTaskIds(taskIds)
			.stream()
			.collect(Collectors.groupingBy(InspectionTaskExecutorVO::getTaskId));

		LocalDateTime currentDateTime = LocalDateTime.now();
		boolean isAdmin = AuthUtil.isAdmin();
		Long currentUserId = AuthUtil.getUserId();

		for (InspectionTaskVO taskVO : taskViewList) {
            List<Long> itemIds = Func.toLongList(taskVO.getItemIds());
            List<String> itemNamesList = itemIds.stream()
                .map(itemIdToNameMap::get)
                .filter(Func::isNotBlank)
                .collect(Collectors.toList());

			// 填充任务剩余秒数
			fillTaskSecondsUntilEnd(taskVO, currentDateTime);

			// 填充巡检项名称列表
            taskVO.setItemNames(itemNamesList);

			// 填充巡检任务执行人列表
			taskVO.setInspectors(taskIdToTaskExecutorListMap.getOrDefault(taskVO.getId(), Collections.emptyList()));

			// 填充任务操作权限, 放在填充任务执行人列表后
			fillTaskOperationPermission(taskVO, currentUserId, isAdmin);
        }
	}

	/**
	 * 为任务视图填充关联信息
	 *
	 * @param task 任务视图
	 */
	public void fillTaskVO(InspectionTaskVO task) {
        if (Objects.nonNull(task)) {
			// 填充任务剩余秒数
			fillTaskSecondsUntilEnd(task, LocalDateTime.now());
			// 填充巡检子任务汇总信息
            task.setSubTaskSummary(subTaskGatewayReadable.getSummaryByTaskId(task.getId()));
			// 填充巡检任务执行人列表
            task.setInspectors(taskExecutorMapper.listViewObjectByTaskId(task.getId()));
			// 填充任务操作权限, 放在填充任务执行人列表后
			fillTaskOperationPermission(task, AuthUtil.getUserId(), AuthUtil.isAdmin());
		}

	}

	/**
	 * 巡检任务领域对象转视图对象
	 *
	 * @param task 巡检任务领域对象
	 * @return 巡检任务视图对象
	 */
	public static InspectionTaskVO toVO(InspectionTask task) {
		InspectionTaskVO vo = new InspectionTaskVO();
		vo.setId(task.getId());
		vo.setName(task.getName());
		vo.setParklotId(task.getParklotId());
		vo.setItemIds(Func.join(task.getItemIds()));
		vo.setCompletionRate(task.getCompletionRate());
		vo.setStartTime(task.getStartTime());
		vo.setEndTime(task.getEndTime());
		vo.setLatestCompletedTime(task.getLatestCompletedTime());
		vo.setStatus(task.getStatus());
		vo.setTenantId(task.getTenantId());
		// 转换任务完成实际耗时：Duration转换为秒数
		if (task.getActualCompletionDuration() != null) {
			vo.setActualCompletionDurationSeconds(task.getActualCompletionDuration().getSeconds());
		}
		return vo;
	}

	/**
	 * 填充任务剩余秒数
	 *
	 * @param task 任务视图
	 * @param currentDateTime 当前时间
	 */
	private void fillTaskSecondsUntilEnd(InspectionTaskVO task, LocalDateTime currentDateTime) {
		if (Objects.nonNull(task)) {
			// 计算任务剩余秒数
			long secondsUntilEnd = Duration.between(currentDateTime, task.getEndTime()).getSeconds();
			// 设置任务剩余秒数
			task.setSecondsUntilEnd(secondsUntilEnd < 0 ? 0 : secondsUntilEnd);
		}
	}

	/**
     * 填充任务操作权限
     * <p>
     * 权限规则：
     * 1. 当前用户是管理员时
     *   - 如果不包含在任务处理人中，则仅有查看权限
     *   - 如果包含在任务处理人中，则具有操作权限
     * 2. 当前用户非管理员时
     *   - 具有操作权限（因为非管理员只能看到自己的任务）
     * </p>
     *
     * @param task 任务
     * @param isAdmin 是否是管理员
     */
    private void fillTaskOperationPermission(InspectionTaskVO task, Long currentUserId, boolean isAdmin) {
        if (Func.isEmpty(task)) {
            return;
        }

        if (!isAdmin) {
			// 非管理员默认有操作权限
			task.setHasOperationPermission(true);
		} else {
			// 管理员需要判断是否在执行人列表中
			boolean isExecutor = false;
			if (Func.isNotEmpty(task.getInspectors())) {
				isExecutor = task.getInspectors().stream()
					.anyMatch(executor -> Objects.equals(executor.getExecutorId(), currentUserId));
			}
			task.setHasOperationPermission(isExecutor);
		}
    }

}
