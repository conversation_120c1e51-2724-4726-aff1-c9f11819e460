package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.lecent.park.entity.ParkingPlace;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 车位信息表
 *
 * <AUTHOR>
 */
@Mapper
public interface ParkingSpaceConverter extends DomainEntityConverter<ParkingSpace, ParkingPlace> {

	ParkingSpaceConverter INSTANCE = Mappers.getMapper(ParkingSpaceConverter.class);

	@Override
	ParkingSpace fromDO(ParkingPlace dataObject);

	@Mapping(target = "plate", ignore = true)
	@Mapping(target = "location", ignore = true)
	@Mapping(target = "lng", ignore = true)
	@Mapping(target = "lat", ignore = true)
	@Mapping(target = "isFree", ignore = true)
	@Mapping(target = "isAppointment", ignore = true)
	@Override
	ParkingPlace toDO(ParkingSpace domainObject);
}
