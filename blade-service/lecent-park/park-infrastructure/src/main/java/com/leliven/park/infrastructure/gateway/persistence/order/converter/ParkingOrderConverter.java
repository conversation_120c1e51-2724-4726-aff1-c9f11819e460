package com.leliven.park.infrastructure.gateway.persistence.order.converter;

import cn.hutool.core.math.Money;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.en.coupon.CouponCategory;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.entity.ParkingOrder;
import com.leliven.park.domain.common.Discount;
import com.leliven.park.domain.common.Discounts;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.domain.common.UnusualCost;
import com.leliven.park.domain.order.parking.entity.*;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.parking.image.entity.ParkingImageCollection;
import com.leliven.park.domain.vehicle.VehicleRepositoryI;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.vehicle.model.PlateVector;
import com.leliven.vehicle.model.Vehicle;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * 停车订单对象转换器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ParkingOrderConverter extends AbstractDomainEntityConverter<ParkingOrderAgg, ParkingOrder>
	implements DomainEntityConverter<ParkingOrderAgg, ParkingOrder> {

	private final VehicleRepositoryI vehicleRepository;

	@Override
	protected ParkingOrderAgg doFromDO(ParkingOrder parkingOrderDO) {
		if (Objects.isNull(parkingOrderDO)) {
			return null;
		}

		Vehicle vehicle = vehicleRepository.getById(parkingOrderDO.getVehicleId(),
			() -> Vehicle.of(parkingOrderDO.getPlate()));
		vehicle.plateVector(PlateVector.of(parkingOrderDO.getOpenId()));

		ParkingStatusEnum parkingStatus = ParkingStatusEnum.resolve(parkingOrderDO.getParkingStatus());

		ParkingOrderAgg parkingOrderAgg = new ParkingOrderAgg();
		// 设置进场信息
		parkingOrderAgg.setEnterInfo(parkingOrderDataObject2EnterEventInfo(parkingOrderDO, vehicle));
		// 如果是已出场状态，设置出场信息
		if (parkingStatus.isExited()) {
			parkingOrderAgg.setExitInfo(parkingOrderDataObject2ExitEventInfo(parkingOrderDO, vehicle));
			parkingOrderAgg.setDurationTime(parkingOrderDO.getDurationTime());
		}


		parkingOrderAgg.setParklotId(parkingOrderDO.getParklotId());
		parkingOrderAgg.setPlaceId(parkingOrderDO.getPlaceId());
		parkingOrderAgg.setParkingStatus(parkingStatus);
		parkingOrderAgg.setBillingStartTime(parkingOrderDO.getBillingStartTime());
		parkingOrderAgg.getParkingCost()
			.addExpectedReceiveAmount(new Money(parkingOrderDO.getTotalAmount()))
			.addActualReceiveAmount(new Money(parkingOrderDO.getPaidAmount()))
			.addDiscounts(parkingOrderDataObject2Discounts(parkingOrderDO))
			.addUnusualCost(new UnusualCost(
				new Money(parkingOrderDO.getUnusualAmount()), parkingOrderDO.getUnusualAmountType()));
		parkingOrderAgg.getParkingSchedule()
			.addParkingTimeNodes(ParkingTimeNodeConverter.toParkingTimeNodes(parkingOrderDO.getParkingTimeNode()))
			.addChargePeriodDetails(ChargePeriodDetailConverter.toChargePeriodDetails(parkingOrderDO.getTimeNodeDetail()));
		parkingOrderAgg.setAuthCardRelation(parkingOrderDataObject2AuthCardRelation(parkingOrderDO));
		parkingOrderAgg.setOccupied(Func.toBoolean(parkingOrderDO.getOccupied(), Boolean.FALSE));
		parkingOrderAgg.setChargeRuleId(parkingOrderDO.getChargeRuleId());
		parkingOrderAgg.setPayTypes(parkingOrderDO.getPayTypes());
		parkingOrderAgg.setCouponIds(parkingOrderDO.getCouponIds());
		parkingOrderAgg.setReasonIds(parkingOrderDO.getReasonIds());
		parkingOrderAgg.setTargetParkingId(parkingOrderDO.getTargetParkingId());
		parkingOrderAgg.setTempCar(Func.toBoolean(parkingOrderDO.getTempCar(), Boolean.FALSE));
		parkingOrderAgg.setReserveOrderId(parkingOrderDO.getReserveOrderId());
		parkingOrderAgg.setOtherFlag(parkingOrderDO.getOtherFlag());
		parkingOrderAgg.setMerchantIds(parkingOrderDO.getMerchantIds());
		parkingOrderAgg.setVersion(parkingOrderDO.getVersion());
		parkingOrderAgg.setOrderIds(parkingOrderDO.getOrderIds());
		parkingOrderAgg.setEnterConfirm(parkingOrderDO.isEnterConfirm());
		parkingOrderAgg.setErrorStatus(parkingOrderDO.getErrorStatus());
		parkingOrderAgg.setMemo(parkingOrderDO.getMemo());

		return parkingOrderAgg;
	}

	@Override
	protected ParkingOrder doToDO(ParkingOrderAgg domainObject) {
		if (Objects.isNull(domainObject)) {
			return null;
		}

		ParkingOrder parkingOrderDO = new ParkingOrder();
		parkingOrderDO.setParklotId(domainObject.getParklotId());
		parkingOrderDO.setPlaceId(domainObject.getPlaceId());
		parkingOrderDO.setParkingStatus(domainObject.getParkingStatus().getValue());
		parkingOrderDO.setBillingStartTime(domainObject.getBillingStartTime());
		parkingOrderDO.setOccupied(domainObject.isOccupied());
		parkingOrderDO.setChargeRuleId(domainObject.getChargeRuleId());
		parkingOrderDO.setPayTypes(domainObject.getPayTypes());
		parkingOrderDO.setCouponIds(domainObject.getCouponIds());
		parkingOrderDO.setReasonIds(domainObject.getReasonIds());
		parkingOrderDO.setTargetParkingId(domainObject.getTargetParkingId());
		parkingOrderDO.setTempCar(domainObject.isTempCar());
		parkingOrderDO.setReserveOrderId(domainObject.getReserveOrderId());
		parkingOrderDO.setOtherFlag(domainObject.getOtherFlag());
		parkingOrderDO.setMerchantIds(domainObject.getMerchantIds());
		parkingOrderDO.setVersion(domainObject.getVersion());
		parkingOrderDO.setOrderIds(domainObject.getOrderIds());
		parkingOrderDO.setEnterConfirm(domainObject.isEnterConfirm());
		parkingOrderDO.setErrorStatus(domainObject.getErrorStatus());
		parkingOrderDO.setMemo(domainObject.getMemo());
		parkingCost2DO(domainObject.getParkingCost(), parkingOrderDO);
		parkingSchedule2DO(domainObject.getParkingSchedule(), parkingOrderDO);
		// 授权卡关系
		authCardRelation2ParkingOrderDO(domainObject.getAuthCardRelation(), parkingOrderDO);
		// 车辆信息
		parkingOrderDO.setPlate(domainObject.getEnterInfo().getVehicle().getPlate());
		parkingOrderDO.setVehicleId(domainObject.getEnterInfo().getVehicle().getId());
		parkingOrderDO.setOpenId(domainObject.getEnterInfo().getVehicle().getPlateVectorNo());
		// 进场信息
		enterInfo2DO(domainObject.getEnterInfo(), parkingOrderDO);
		// 出场信息
		if (domainObject.isExit()) {
			exitInfo2DO(domainObject.getExitInfo(), parkingOrderDO);
			parkingOrderDO.setDurationTime(domainObject.getDurationTime());
		}

		return parkingOrderDO;
	}

	/**
	 * 停车订单数据对象转换为进场信息
	 *
	 * @param parkingOrderDO 停车订单数据对象
	 * @param vehicle        车辆信息
	 * @return {@link ParkingEventInfo}
	 */
	ParkingEventInfo parkingOrderDataObject2EnterEventInfo(ParkingOrder parkingOrderDO, Vehicle vehicle) {
		if (Objects.isNull(parkingOrderDO)) {
			return null;
		}
		ParkingEventInfo enterInfo = new ParkingEventInfo();
		enterInfo.setChannelId(parkingOrderDO.getEnterChannelId());
		enterInfo.setTodoId(parkingOrderDO.getEnterTodoId());
		enterInfo.setDatetime(parkingOrderDO.getEnterTime());
		enterInfo.setImageUrl(parkingOrderDO.getEnterImageUrl());
		enterInfo.setVehicle(vehicle);
		enterInfo.setTriggerWay(ParkingTriggerWay.resolve(parkingOrderDO.getEnterWay()));
		enterInfo.setOpenGateCost(parkingOrderDO.getEnterOpenGateCost());
		enterInfo.setOptUserId(parkingOrderDO.getEnterOptUserId());
		return enterInfo;
	}

	/**
	 * 停车订单数据对象转换为出场信息
	 *
	 * @param parkingOrderDO 停车订单数据对象
	 * @param vehicle        车辆信息
	 * @return {@link ParkingEventInfo}
	 */
	ParkingEventInfo parkingOrderDataObject2ExitEventInfo(ParkingOrder parkingOrderDO, Vehicle vehicle) {
		if (Objects.isNull(parkingOrderDO)) {
			return null;
		}
		ParkingEventInfo exitInfo = new ParkingEventInfo();
		exitInfo.setChannelId(parkingOrderDO.getExitChannelId());
		exitInfo.setTodoId(parkingOrderDO.getExitTodoId());
		exitInfo.setDatetime(parkingOrderDO.getExitTime());
		exitInfo.setImageUrl(parkingOrderDO.getExitImageUrl());
		exitInfo.setImageCollection(new ParkingImageCollection());
		exitInfo.setVehicle(vehicle);
		exitInfo.setTriggerWay(ParkingTriggerWay.resolve(parkingOrderDO.getExitWay()));
		exitInfo.setOpenGateCost(parkingOrderDO.getExitOpenGateCost());
		exitInfo.setOptUserId(parkingOrderDO.getExitOptUserId());
		return exitInfo;
	}

	Discounts parkingOrderDataObject2Discounts(ParkingOrder parkingOrderDO) {
		Discounts discounts = new Discounts();
		Optional.ofNullable(parkingOrderDO.getDiscountAmount())
			.filter(amount -> amount.compareTo(BigDecimal.ZERO) > 0)
			.ifPresent(totalDiscountAmount -> discounts
				.add(new Discount(CouponCategory.COUPON, new Money(parkingOrderDO.getCouponDiscountAmount())))
				.add(new Discount(CouponCategory.MERCHANT, new Money(parkingOrderDO.getMerchantAmount())))
				// TODO 暂时把优惠券和商户优惠的金额排除后的金额作为其他优惠，后续在对应的优惠逻辑中处理
				.add(new Discount(CouponCategory.OTHER, new Money(totalDiscountAmount.subtract(
					parkingOrderDO.getCouponDiscountAmount()).subtract(parkingOrderDO.getMerchantAmount())))
				)
			);
		return discounts;
	}

	/**
	 * 停车订单数据对象转换为授权卡关系
	 *
	 * @param parkingOrderDO 停车订单数据对象
	 * @return {@link AuthCardRelation}
	 */
	AuthCardRelation parkingOrderDataObject2AuthCardRelation(ParkingOrder parkingOrderDO) {
		if (Objects.isNull(parkingOrderDO)) {
			return null;
		}
		return AuthCardRelation.builder()
			.cardId(parkingOrderDO.getCardId())
			.cardType(parkingOrderDO.getCardType())
			.cardIds(Func.toLongList(parkingOrderDO.getTempCardIds()))
			.relationId(parkingOrderDO.getRelationId())
			.relationType(PlatePropertyType.resolve(parkingOrderDO.getRelationType()))
			.build();
	}

	/**
	 * 进场信息转换为停车订单数据对象
	 *
	 * @param eventInfo      进场事件
	 * @param parkingOrderDO 停车订单数据对象
	 */
	void enterInfo2DO(ParkingEventInfo eventInfo, ParkingOrder parkingOrderDO) {
		parkingOrderDO.setEnterTime(eventInfo.getDatetime());
		parkingOrderDO.setEnterChannelId(eventInfo.getChannelId());
		parkingOrderDO.setEnterTodoId(eventInfo.getTodoId());
		parkingOrderDO.setEnterImageUrl(eventInfo.getImageUrl());
		parkingOrderDO.setEnterWay(eventInfo.getTriggerWay().getValue());
		parkingOrderDO.setEnterOpenGateCost(eventInfo.getOpenGateCost());
		parkingOrderDO.setEnterOptUserId(eventInfo.getOptUserId());
	}

	/**
	 * 出场信息转换为停车订单数据对象
	 *
	 * @param eventInfo      出场事件
	 * @param parkingOrderDO 停车订单数据对象
	 */
	void exitInfo2DO(ParkingEventInfo eventInfo, ParkingOrder parkingOrderDO) {
		parkingOrderDO.setExitTime(eventInfo.getDatetime());
		parkingOrderDO.setExitChannelId(eventInfo.getChannelId());
		parkingOrderDO.setExitTodoId(eventInfo.getTodoId());
		parkingOrderDO.setExitImageUrl(eventInfo.getImageUrl());
		parkingOrderDO.setExitWay(eventInfo.getTriggerWay().getValue());
		parkingOrderDO.setExitOpenGateCost(eventInfo.getOpenGateCost());
		parkingOrderDO.setExitOptUserId(eventInfo.getOptUserId());
	}

	/**
	 * 停车费用转换为停车订单数据对象
	 *
	 * @param parkingCost    停车费用
	 * @param parkingOrderDO 停车订单数据对象
	 */
	void parkingCost2DO(ParkingCost parkingCost, ParkingOrder parkingOrderDO) {
		parkingOrderDO.setTotalAmount(parkingCost.getExpectedReceiveAmount().getAmount());
		parkingOrderDO.setPaidAmount(parkingCost.getActualReceiveAmount().getAmount());
		parkingOrderDO.setUnusualAmount(parkingCost.getUnusualCost().getUnusualAmount().getAmount());
		parkingOrderDO.setUnusualAmountType(parkingCost.getUnusualCost().getUnusualAmountType());
		parkingOrderDO.setDiscountAmount(parkingCost.getDiscountAmount().getAmount());
		parkingOrderDO.setMerchantAmount(parkingCost.getMerchantAmount().getAmount());
		parkingOrderDO.setCouponDiscountAmount(parkingCost.getCouponDiscountAmount().getAmount());
	}

	/**
	 * 停车时段转换为停车订单数据对象
	 *
	 * @param parkingSchedule 停车时段
	 * @param parkingOrderDO  停车订单数据对象
	 */
	void parkingSchedule2DO(ParkingSchedule parkingSchedule, ParkingOrder parkingOrderDO) {
		parkingOrderDO.setParkingTimeNode(
			ParkingTimeNodeConverter.toParkingTimeNodesJsonStr(parkingSchedule.getParkingTimeNodes()));
		parkingOrderDO.setTimeNodeDetail(ChargePeriodDetailConverter
			.toChargePeriodDetailJsonStr(parkingSchedule.getChargePeriodDetails()));
	}

	/**
	 * 授权卡关系转换为停车订单数据对象
	 *
	 * @param authCardRelation 授权卡关系
	 * @param parkingOrderDO   停车订单数据对象
	 */
	void authCardRelation2ParkingOrderDO(AuthCardRelation authCardRelation, ParkingOrder parkingOrderDO) {
		if (Objects.isNull(authCardRelation) || Objects.isNull(parkingOrderDO)) {
			return;
		}

		parkingOrderDO.setCardId(authCardRelation.getCardId());
		parkingOrderDO.setCardType(authCardRelation.getCardType());
		parkingOrderDO.setTempCardIds(Func.join(authCardRelation.getCardIds()));
		parkingOrderDO.setRelationId(authCardRelation.getRelationId());
		parkingOrderDO.setRelationType(authCardRelation.getRelationTypeValue());
	}
}
