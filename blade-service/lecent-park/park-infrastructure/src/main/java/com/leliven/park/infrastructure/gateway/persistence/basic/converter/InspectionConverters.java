package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import cn.hutool.core.lang.Opt;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.inspection.model.*;
import com.leliven.park.domain.basic.inspection.model.valueobject.*;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.dataobject.*;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.ddd.core.valueobject.SimpleLocation;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 巡检领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S6548"})
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionConverters {


	/**
	 * 巡检现象转换器实例
	 */
	public static final InspectionPhenomenonConverter PHENOMENON_CONVERTER = new InspectionPhenomenonConverter();

    /**
     * 巡检项转换器实例
     */
	public static final InspectionItemConverter ITEM_CONVERTER = new InspectionItemConverter();

    /**
     * 巡检任务转换器实例
     */
	public static final InspectionTaskConverter TASK_CONVERTER = new InspectionTaskConverter();

    /**
     * 巡检任务执行人转换器实例
     */
	public static final InspectionTaskExecutorConverter TASK_EXECUTOR_CONVERTER = new InspectionTaskExecutorConverter();

    /**
     * 巡检子任务任务转换器实例
     */
    public static final InspectionSubTaskConverter SUB_TASK_CONVERTER = new InspectionSubTaskConverter();

    /**
     * 巡检任务项转换器实例
     */
	public static final InspectionTaskItemConverter TASK_ITEM_CONVERTER = new InspectionTaskItemConverter();

    /**
     * 巡检记录转换器实例
     */
	public static final InspectionRecordConverter RECORD_CONVERTER = new InspectionRecordConverter();

	/**
	 * 巡检异常原因转换器实例
	 */
	public static final InspectionAbnormalReasonConverter ABNORMAL_REASON_CONVERTER = new InspectionAbnormalReasonConverter();


	/**
	 * 巡检现象转换器
	 *
	 * <AUTHOR>
	 */
	@NoArgsConstructor(access = AccessLevel.PRIVATE)
	public static class InspectionPhenomenonConverter extends AbstractDomainEntityConverter<InspectionPhenomenon, InspectionPhenomenonDO> {

		@Override
		protected InspectionPhenomenon doFromDO(InspectionPhenomenonDO dataObject) {
			InspectionPhenomenon phenomenon = InspectionPhenomenon.create(dataObject.getName(), dataObject.getCode(), dataObject.getDescription());
			convertForwardRule2DomainObject(dataObject.getForwardRuleJson(), phenomenon);
			convertAbnormalReasonIds2DomainObject(dataObject.getAbnormalReasonIds(), phenomenon);
			return phenomenon;
		}

		@Override
		protected InspectionPhenomenonDO doToDO(InspectionPhenomenon domainObject) {
			InspectionPhenomenonDO dataobject = new InspectionPhenomenonDO();
			dataobject.setName(domainObject.getName());
			dataobject.setCode(domainObject.getCode());
			dataobject.setDescription(domainObject.getDescription());
			dataobject.setForwardRuleJson(convertDomainObject2ForwardRuleJson(domainObject.getForwardRules()));
			dataobject.setAbnormalReasonIds(convertDomainObject2AbnormalReasonIdsStr(domainObject.getAbnormalReasonIdList()));
			return dataobject;
		}

        /**
         * 将转发规则JSON字符串转换为领域对象的转发规则集合
         *
         * @param forwardRuleJson 转发规则字符串
         * @param phenomenon 巡检现象领域对象
         */
		private void convertForwardRule2DomainObject(String forwardRuleJson, InspectionPhenomenon phenomenon) {
			if (Func.isNotBlank(forwardRuleJson)) {
				phenomenon.addForwardRules(JsonUtil.readList(forwardRuleJson, InspectionPhenomenonForwardRule.class));
			}
		}

        /**
         * 将领域对象的转发规则集合转换为JSON字符串
         *
         * @param forwardRules 转发规则集合
         * @return 转发规则字符串
         */
		private String convertDomainObject2ForwardRuleJson(List<InspectionPhenomenonForwardRule> forwardRules) {
			if (Func.isEmpty(forwardRules)) {
				return "";
			}

			return Func.toJson(forwardRules);
		}

		/**
		 * 将逗号分隔的异常原因ID字符串转换为领域对象的异常原因ID集合
		 *
		 * @param abnormalReasonIdsStr 逗号分隔的异常原因ID字符串
		 * @param phenomenon 巡检现象领域对象
		 */
		private void convertAbnormalReasonIds2DomainObject(String abnormalReasonIdsStr, InspectionPhenomenon phenomenon) {
			if (Func.isNotBlank(abnormalReasonIdsStr)) {
				List<Long> abnormalReasonIds = Func.toLongList(abnormalReasonIdsStr);
				phenomenon.addAbnormalReasonIds(abnormalReasonIds);
			}
		}

		/**
		 * 将领域对象的异常原因ID列表转换为逗号分隔的字符串
		 *
		 * @param abnormalReasonIds 异常原因ID列表
		 * @return 逗号分隔的异常原因ID字符串
		 */
		private String convertDomainObject2AbnormalReasonIdsStr(List<Long> abnormalReasonIds) {
			return Func.join(abnormalReasonIds);
		}
	}

    /**
     * 巡检项转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionItemConverter extends AbstractDomainEntityConverter<InspectionItem, InspectionItemDO> {

        @Override
        protected InspectionItem doFromDO(InspectionItemDO dataObject) {
           return new InspectionItem()
                .setItemName(dataObject.getItemName())
                .setItemCode(dataObject.getItemCode())
                .setCategoryType(dataObject.getCategoryType())
                .setObjectType(InspectionObjectType.resolve(dataObject.getRelatedObject()))
                .setRelatedDeviceTypes(Func.toIntList(dataObject.getRelatedDeviceTypes()).stream()
                    .map(DeviceType::resolve)
                    .collect(Collectors.toSet()))
                .setDescription(dataObject.getDescription());
        }

        @Override
        protected InspectionItemDO doToDO(InspectionItem domainObject) {
            InspectionItemDO itemDO = new InspectionItemDO();
            itemDO.setItemName(domainObject.getItemName());
            itemDO.setItemCode(domainObject.getItemCode());
            itemDO.setCategoryType(domainObject.getCategoryType());
            itemDO.setRelatedObject(domainObject.getObjectType().getValue());
            itemDO.setRelatedDeviceTypes(Func.join(domainObject.getRelatedDeviceTypes().stream()
                .map(DeviceType::getValue)
                .collect(Collectors.toList())));
            itemDO.setDescription(domainObject.getDescription());
            return itemDO;
        }
    }

    /**
     * 巡检任务转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionTaskConverter extends AbstractDomainEntityConverter<InspectionTask, InspectionTaskDO> {

        @Override
        protected InspectionTask doFromDO(InspectionTaskDO dataObject) {
            return new InspectionTask(dataObject.getId(), dataObject.getPlanId(), dataObject.getParklotId())
				.name(dataObject.getName())
                .timeWindow(InspectionTaskTimeWindow.of(dataObject.getStartTime(), dataObject.getEndTime()))
                .taskStatus(InspectionTaskStatus.resolve(dataObject.getStatus()))
                .itemIds(Func.toLongList(dataObject.getItemIds()))
                .completionRate(dataObject.getCompletionRate())
                .latestCompletedTime(dataObject.getLatestCompletedTime())
                .actualCompletionDuration(dataObject.getActualCompletionDurationSeconds() != null ?
                    Duration.ofSeconds(dataObject.getActualCompletionDurationSeconds()) : null);
        }

        @Override
        protected InspectionTaskDO doToDO(InspectionTask domainObject) {
            InspectionTaskDO taskDO = new InspectionTaskDO();
			taskDO.setName(domainObject.getName());
            taskDO.setPlanId(domainObject.getPlanId());
            taskDO.setParklotId(domainObject.getParklotId());
            taskDO.setStartTime(domainObject.getStartTime());
            taskDO.setEndTime(domainObject.getEndTime());
            taskDO.setStatus(domainObject.getTaskStatus().getValue());
            taskDO.setItemIds(Func.join(domainObject.getItemIds()));
            taskDO.setCompletionRate(domainObject.getCompletionRate());
            taskDO.setLatestCompletedTime(domainObject.getLatestCompletedTime());
            // 转换任务完成实际耗时：Duration转换为秒数
            if (domainObject.getActualCompletionDuration() != null) {
                taskDO.setActualCompletionDurationSeconds(domainObject.getActualCompletionDuration().getSeconds());
            }
            return taskDO;
        }
    }

    /**
     * 巡检任务执行人转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionTaskExecutorConverter implements DomainEntityConverter<InspectionTaskExecutor, InspectionTaskExecutorDO> {

        @Override
        public InspectionTaskExecutor fromDO(InspectionTaskExecutorDO dataObject) {
            if (dataObject == null) {
                return null;
            }

            return new InspectionTaskExecutor(
                dataObject.getId(), dataObject.getTaskId(), dataObject.getExecutorId(), dataObject.getExecutorName());
        }

        @Override
        public InspectionTaskExecutorDO toDO(InspectionTaskExecutor domainObject) {
            if (domainObject == null) {
                return null;
            }

            InspectionTaskExecutorDO executorDO = new InspectionTaskExecutorDO();
            executorDO.setId(domainObject.getId());
            executorDO.setTaskId(domainObject.getTaskId());
            executorDO.setExecutorId(domainObject.getExecutorId());
            executorDO.setExecutorName(domainObject.getExecutorName());
            return executorDO;
        }
    }

    /**
     * 巡检子任务转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionSubTaskConverter extends AbstractDomainEntityConverter<InspectionSubTask, InspectionSubTaskDO> {

        @Override
        protected InspectionSubTask doFromDO(InspectionSubTaskDO dataObject) {
            InspectionSubTask subTask = new InspectionSubTask();
            subTask.setId(dataObject.getId());
            subTask.setTaskId(dataObject.getTaskId());
            subTask.setObject(InspectionObject.of(dataObject.getObjectId(), dataObject.getObjectName(),
                InspectionObjectType.resolve(dataObject.getObjectType())));
            subTask.setInspector(Inspector.of(dataObject.getInspectorId(), dataObject.getInspectorName()));
            subTask.setCompleteTime(dataObject.getCompleteTime());
            subTask.setHasAbnormalItems(dataObject.getHasAbnormalItems());
            // 从DO转换位置信息
            subTask.setCompleteLocation(SimpleLocation.ofUnvalidated(
                dataObject.getCompleteLongitude(),
                dataObject.getCompleteLatitude(),
                dataObject.getCompleteAddress()
            ));

            return subTask;
        }

        @Override
        protected InspectionSubTaskDO doToDO(InspectionSubTask domainObject) {
            InspectionSubTaskDO subTaskDO = new InspectionSubTaskDO();
            subTaskDO.setTaskId(domainObject.getTaskId());
            subTaskDO.setObjectId(domainObject.getObject().getId());
            subTaskDO.setObjectName(domainObject.getObject().getName());
            subTaskDO.setObjectType(domainObject.getObject().getType().getValue());
            subTaskDO.setInspectorId(domainObject.getInspectorId());
            subTaskDO.setInspectorName(domainObject.getInspectorName());
            subTaskDO.setCompleteTime(domainObject.getCompleteTime());
            subTaskDO.setHasAbnormalItems(domainObject.isHasAbnormalItems());

            // 转换位置信息到DO
            SimpleLocation location = domainObject.getCompleteLocation();
            if (location != null) {
                subTaskDO.setCompleteLongitude(location.getLongitude());
                subTaskDO.setCompleteLatitude(location.getLatitude());
                subTaskDO.setCompleteAddress(location.getAddress());
            }
            return subTaskDO;
        }
    }

    /**
     * 巡检任务项转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionTaskItemConverter implements DomainEntityConverter<InspectionTaskItem, InspectionTaskItemDO> {

        @Override
        public InspectionTaskItem fromDO(InspectionTaskItemDO dataObject) {
            if (Objects.isNull(dataObject)) {
                return null;
            }

            InspectionTaskItem item = new InspectionTaskItem();
            item.setId(dataObject.getId());
            item.setTaskId(dataObject.getTaskId());
            item.setSubTaskId(dataObject.getSubTaskId());
            item.setItemId(dataObject.getItemId());
            return item;
        }

        @Override
        public InspectionTaskItemDO toDO(InspectionTaskItem domainObject) {
            if (Objects.isNull(domainObject)) {
                return null;
            }

            InspectionTaskItemDO itemDO = new InspectionTaskItemDO();
            itemDO.setId(domainObject.getId());
            itemDO.setTaskId(domainObject.getTaskId());
            itemDO.setSubTaskId(domainObject.getSubTaskId());
            itemDO.setItemId(domainObject.getItemId());
            return itemDO;
        }
    }

    /**
     * 巡检记录转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionRecordConverter extends AbstractDomainEntityConverter<InspectionRecord, InspectionRecordDO> {


        @Override
        protected InspectionRecord doFromDO(InspectionRecordDO dataObject) {
            InspectionRecord domainObject = new InspectionRecord();
            domainObject.setTaskId(dataObject.getTaskId());
            domainObject.setSubTaskId(dataObject.getSubTaskId());
            domainObject.setTaskItemId(dataObject.getTaskItemId());
            domainObject.setParklotId(dataObject.getParklotId());
            domainObject.setObject(InspectionObject.of(dataObject.getObjectId(), dataObject.getObjectName(),
				InspectionObjectType.resolve(dataObject.getObjectType())));
            domainObject.setInspector(Inspector.of(dataObject.getInspectorId(), dataObject.getInspectorName()));
            domainObject.setInspectionTime(dataObject.getInspectionTime());
            domainObject.setInspectionType(InspectionType.resolve(dataObject.getType()));
            domainObject.setItemId(dataObject.getItemId());
            domainObject.setResultStatus(InspectionResultStatus.resolve(dataObject.getResultStatus()));
            domainObject.setAbnormalInfo(InspectionAbnormalInfo.builder()
                .phenomenonId(dataObject.getAbnormalDescId())
                .phenomenonName(dataObject.getAbnormalTitle())
                .abnormalReasonId(dataObject.getAbnormalReasonId())
                .abnormalReasonName(dataObject.getAbnormalReasonName())
                .abnormalImages(Func.toStrList(dataObject.getAbnormalImages()))
                .createWorkOrderEnable(dataObject.getCreateWorkOrderEnable())
                .workOrderId(dataObject.getWorkOrderId())
                .resolved(Func.toBoolean(dataObject.getResolved(), Boolean.TRUE))
                .build());
            domainObject.setAdditionalInfo(
                Opt.ofBlankAble(dataObject.getAdditionalInfo())
                    .map(t -> Func.readJson(dataObject.getAdditionalInfo(), new TypeReference<Map<String, String>>() {}))
                    .orElseGet(Maps::newHashMap)
            );
            domainObject.setLocation(SimpleLocation.ofUnvalidated(
                dataObject.getLongitude(),
                dataObject.getLatitude(),
                dataObject.getAddress()
            ));
            return domainObject;
        }

        @Override
        protected InspectionRecordDO doToDO(InspectionRecord domainObject) {
            InspectionRecordDO dataObject = new InspectionRecordDO();
            dataObject.setTaskId(domainObject.getTaskId());
            dataObject.setSubTaskId(domainObject.getSubTaskId());
            dataObject.setTaskItemId(domainObject.getTaskItemId());
            dataObject.setParklotId(domainObject.getParklotId());
            dataObject.setItemId(domainObject.getItemId());

            JavaUtils.INSTANCE
                .acceptIfNotNull(domainObject.getObject(), t -> {
                    dataObject.setObjectId(t.getId());
                    dataObject.setObjectType(t.getType().getValue());
                    dataObject.setObjectName(t.getName());
                })
                .acceptIfNotNull(domainObject.getInspector(), t -> {
                    dataObject.setInspectorId(t.getInspectorId());
                    dataObject.setInspectorName(t.getInspectorName());
                })
                .acceptIfNotNull(domainObject.getInspectionTime(), dataObject::setInspectionTime)
                .acceptIfNotNull(domainObject.getInspectionType(), t -> dataObject.setType(t.getValue()))
                .acceptIfNotNull(domainObject.getResultStatus(), t -> dataObject.setResultStatus(t.getValue()))
                .acceptIfNotNull(domainObject.getAbnormalInfo(), t -> {
                    dataObject.setAbnormalDescId(t.getPhenomenonId());
                    dataObject.setAbnormalTitle(t.getPhenomenonName());
                    dataObject.setAbnormalReasonId(t.getAbnormalReasonId());
                    dataObject.setAbnormalReasonName(t.getAbnormalReasonName());
                    dataObject.setAbnormalImages(Func.join(t.getAbnormalImages()));
                    dataObject.setCreateWorkOrderEnable(t.isCreateWorkOrderEnable());
                    dataObject.setWorkOrderId(t.getWorkOrderId());
                    dataObject.setResolved(t.isResolved());
                })
                .acceptIfNotNull(domainObject.getAdditionalInfo(), Func::toJson)
                .acceptIfNotNull(domainObject.getLocation(), t -> {
                    dataObject.setLongitude(t.getLongitude());
                    dataObject.setLatitude(t.getLatitude());
                    dataObject.setAddress(t.getAddress());
                });

            return dataObject;
        }
    }

    /**
     * 巡检异常原因转换器
     *
     * <AUTHOR>
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class InspectionAbnormalReasonConverter extends AbstractDomainEntityConverter<InspectionAbnormalReason, InspectionAbnormalReasonDO> {

        public static final InspectionAbnormalReasonConverter INSTANCE = new InspectionAbnormalReasonConverter();

        @Override
        protected InspectionAbnormalReason doFromDO(InspectionAbnormalReasonDO dataObject) {
            InspectionAbnormalReason domainObject = new InspectionAbnormalReason();
            domainObject.setCode(dataObject.getCode());
            domainObject.setName(dataObject.getName());
            domainObject.setDescription(dataObject.getDescription());
            return domainObject;
        }

        @Override
        protected InspectionAbnormalReasonDO doToDO(InspectionAbnormalReason domainObject) {
            InspectionAbnormalReasonDO dataObject = new InspectionAbnormalReasonDO();
            dataObject.setId(domainObject.getId());
            dataObject.setCode(domainObject.getCode());
            dataObject.setName(domainObject.getName());
            dataObject.setDescription(domainObject.getDescription());
            return dataObject;
        }
    }


}
