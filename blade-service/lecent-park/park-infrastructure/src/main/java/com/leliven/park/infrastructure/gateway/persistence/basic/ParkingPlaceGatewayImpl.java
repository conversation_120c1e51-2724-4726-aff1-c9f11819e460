package com.leliven.park.infrastructure.gateway.persistence.basic;

import com.lecent.park.entity.ParkingPlace;
import com.leliven.ddd.core.annotations.DomainGatewayImpl;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCapture;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCaptureRecord;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.*;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureType;
import com.leliven.park.domain.basic.device.model.BasicParkingDevice;
import com.leliven.park.common.model.valueobject.DeviceIdentifier;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.park.domain.basic.device.model.valueobject.DeviceId;
import com.leliven.park.domain.basic.device.model.valueobject.DeviceSn;
import com.leliven.park.infrastructure.gateway.persistence.basic.converter.ParkingPlaceStatusConverter;
import com.leliven.park.infrastructure.gateway.persistence.basic.converter.ParkingSpaceCameraCaptureRecordConverter;
import com.leliven.park.infrastructure.gateway.persistence.basic.converter.ParkingSpaceConverter;
import com.leliven.park.infrastructure.gateway.persistence.basic.doris.dataobject.ParkingPlaceCameraCaptureRecordDO;
import com.leliven.park.infrastructure.gateway.persistence.basic.doris.mapper.CameraCaptureRecordDorisMapperService;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParkingPlaceMapperService;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParkingPlaceStatusMapperService;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.ParkingPlaceCameraCaptureRecordQuery;
import com.leliven.park.infrastructure.gateway.persistence.basic.redis.ParkingPlaceCacheMapper;
import com.leliven.ddd.core.support.LazyLoader;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.exception.NotFoundException;
import org.springblade.core.tool.utils.Func;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车位领域网关实现
 *
 * <AUTHOR>
 */
@Slf4j
@DomainGatewayImpl
public class ParkingPlaceGatewayImpl implements ParkingPlaceGateway {

    private final ParkingPlaceCacheSupplier cacheSupplier;
    private final ParkingPlaceStatusConverter statusConverter;
    private final ParkingPlaceMapperService placeMapperService;
    private final ParkingPlaceStatusMapperService statusMapperService;
    private final CameraCaptureRecordDorisMapperService cameraCaptureRecordDorisMapperService;

    public ParkingPlaceGatewayImpl(ParkingPlaceStatusConverter statusConverter,
                                   ParkingPlaceMapperService placeMapperService,
                                   ParkingPlaceStatusMapperService statusMapperService,
                                   CameraCaptureRecordDorisMapperService cameraCaptureRecordDorisMapperService) {

        this.cacheSupplier = new RedisParkingPlaceCacheSupplier();
        this.statusConverter = statusConverter;
        this.placeMapperService = placeMapperService;
        this.statusMapperService = statusMapperService;
        this.cameraCaptureRecordDorisMapperService = cameraCaptureRecordDorisMapperService;
    }

    @Override
    public ParkingPlaceCacheSupplier asCacheSupplier() {
        return cacheSupplier;
    }

    @Override
    public ParkingSpace getById(Long id) {
        return aggregate(placeMapperService.getById(id));
    }

    @Override
    public ParkingSpace getOrElseThrow(Long id) {
        return this.getOneOptById(id).orElseThrow(() -> new NotFoundException("未查询到车位信息， id=" + id));
    }

    @Override
    public Optional<ParkingSpace> getOneOptById(Long id) {
        return Optional.ofNullable(aggregate(placeMapperService.getById(id)));
    }

    @Override
    public ParkingSpace getByPayCode(String payCode) {
        return aggregate(placeMapperService.getByPayCode(payCode));
    }

    @Override
    public Optional<ParkingSpace> getByDeviceIdentifier(DeviceIdentifier deviceIdentifier) {
        if (Objects.isNull(deviceIdentifier)) {
            return Optional.empty();
        }

        ParkingPlace dataobject = null;
        if (deviceIdentifier instanceof DeviceId) {
            DeviceId uniqueIdentifier = (DeviceId) deviceIdentifier;
            dataobject = placeMapperService.getByDeviceId(uniqueIdentifier.getValue());
        } else if (deviceIdentifier instanceof DeviceSn) {
            dataobject = placeMapperService.getByDeviceSn(((DeviceSn) deviceIdentifier).getValue());
        }

        return Optional.ofNullable(aggregate(dataobject));
    }

    @Override
    public ParkingSpace getByDevice(ParkingDevice device) {
        if (Objects.isNull(device)) {
            return null;
        }

        ParkingPlace dataobject = null;
        if (device instanceof MobileParkingPlaceDevice) {
            MobileParkingPlaceDevice mobileParkingDevice = (MobileParkingPlaceDevice) device;
            dataobject = placeMapperService.getByPayCode(mobileParkingDevice.getParkingPlacePayCode());
        } else if (device instanceof BasicParkingDevice) {
            BasicParkingDevice basicParkingDevice = (BasicParkingDevice) device;
            if (Objects.nonNull(basicParkingDevice.getId())) {
                dataobject = placeMapperService.getByDeviceId(basicParkingDevice.getId());
            } else if (Func.isNotBlank(basicParkingDevice.getSn())) {
                dataobject = placeMapperService.getByDeviceSn(basicParkingDevice.getSn());
            }
        }

        return aggregate(dataobject);
    }

    @Override
    public List<ParkingSpace> listByParklotId(Long parklotId) {
        List<ParkingPlace> dataObjects = placeMapperService.lambdaQuery().eq(ParkingPlace::getParklotId, parklotId).list();
        return dataObjects.stream().map(this::aggregate).collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Optional<ParkingPlaceCameraCaptureRecord> getLastTimedCaptureWithDateRangeClosed(Long parkingPlaceId,
                                                                                            Date inclusiveStartTime,
                                                                                            Date inclusiveEndTime) {
        ParkingPlaceCameraCaptureRecordDO dataobject = this.cameraCaptureRecordDorisMapperService
            .getLastCapture(ParkingPlaceCameraCaptureRecordQuery.builder()
                .parkingPlaceId(parkingPlaceId)
                .captureTimeStart(inclusiveStartTime).captureTimeEnd(inclusiveEndTime)
                .inclusiveStartTime(true)
                .type(ParkingPlaceCameraCaptureType.TIMED.getValue())
                .build()
            );
        return Optional.ofNullable(ParkingSpaceCameraCaptureRecordConverter.INSTANCE.fromDO(dataobject));
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Optional<ParkingPlaceCameraCaptureRecord> getLastCaptureWithDateRangeClosed(Long parkingPlaceId,
                                                                                       Date inclusiveStartTime,
                                                                                       Date inclusiveEndTime,
                                                                                       @Nonnull String plate) {
        if (PlateValidator.isNoPlate(plate)) {
            return Optional.empty();
        }
        ParkingPlaceCameraCaptureRecordDO dataObject = this.cameraCaptureRecordDorisMapperService
            .getLastCapture(ParkingPlaceCameraCaptureRecordQuery.builder()
                .parkingPlaceId(parkingPlaceId)
                .captureTimeStart(inclusiveStartTime).captureTimeEnd(inclusiveEndTime)
                .inclusiveStartTime(true)
                .plate(plate)
                .build()
            );

        return Optional.ofNullable(ParkingSpaceCameraCaptureRecordConverter.INSTANCE.fromDO(dataObject));
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<ParkingPlaceCameraCaptureRecord> getLastCaptureByTimes(Long parkingPlaceId, Integer times) {
        List<ParkingPlaceCameraCaptureRecordDO> list = cameraCaptureRecordDorisMapperService.lambdaQuery()
            .eq(ParkingPlaceCameraCaptureRecordDO::getParkingPlaceId, parkingPlaceId)
            .orderByDesc(ParkingPlaceCameraCaptureRecordDO::getCaptureTime)
            .last("limit " + times)
            .list();
        return list.stream().map(ParkingSpaceCameraCaptureRecordConverter.INSTANCE::fromDO).collect(Collectors.toList());
    }

    @Override
    public void saveCameraCapture(ParkingPlaceCameraCapture parkingPlaceCameraCapture) {
        saveBatchCameraCaptures(Collections.singletonList(parkingPlaceCameraCapture));
    }

    @Override
    public boolean saveBatchCameraCaptures(List<ParkingPlaceCameraCapture> parkingPlaceCameraCaptures) {
        List<ParkingPlaceCameraCaptureRecordDO> doList = parkingPlaceCameraCaptures.stream()
            .flatMap(t -> ParkingSpaceCameraCaptureRecordConverter.INSTANCE.toDOList(t.getRecords()).stream())
            .collect(Collectors.toList());
        return cameraCaptureRecordDorisMapperService.customSaveBatch(doList);
    }

    /**
     * 获取车位聚合领域对象
     *
     * @param dataobject 数据对象
     * @return 车位聚合领域对象 {@link ParkingSpace}
     */
    public ParkingSpace aggregate(ParkingPlace dataobject) {
        ParkingSpace parkingSpace = ParkingSpaceConverter.INSTANCE.fromDO(dataobject);
        if (Objects.isNull(parkingSpace)) {
            return null;
        }

        parkingSpace.setRealTimeStatusLoader(LazyLoader.of(() ->
			this.statusConverter.fromDO(this.statusMapperService.getByParkingPlaceId(parkingSpace.getId())))
		);

        return parkingSpace;
    }

    public class RedisParkingPlaceCacheSupplier implements ParkingPlaceCacheSupplier {

        @Override
        public ParkingSpace get(Long id) {
            ParkingPlace dataObject = ParkingPlaceCacheMapper.INSTANCE.getById(id);

            if (Objects.isNull(dataObject)) {
                return null;
            }

            return aggregate(dataObject);
        }

        @Override
        public Optional<ParkingSpace> getOptional(Long id) {
            return Optional.ofNullable(get(id));
        }

        @Override
        public Optional<ParkingSpace> getByBoundDeviceId(Long deviceId) {
            ParkingPlace dataObject = ParkingPlaceCacheMapper.INSTANCE.getByBoundDeviceId(deviceId);
            return Optional.ofNullable(dataObject).map(ParkingPlaceGatewayImpl.this::aggregate);
        }
    }


}
