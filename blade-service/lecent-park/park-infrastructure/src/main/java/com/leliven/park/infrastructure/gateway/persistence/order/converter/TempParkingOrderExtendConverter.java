package com.leliven.park.infrastructure.gateway.persistence.order.converter;

import com.lecent.pay.core.enums.PayChannel;
import com.lecent.pay.core.enums.PayScene;
import com.leliven.park.domain.basic.payway.entity.ParklotPayMerchant;
import com.leliven.park.domain.order.temp.model.TempParkingOrderExtend;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.dataobject.TempParkingOrderExtendDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 临停订单转换器
 *
 * <AUTHOR>
 */
@Component
public class TempParkingOrderExtendConverter
    extends AbstractDomainEntityConverter<TempParkingOrderExtend, TempParkingOrderExtendDO> {

    @Override
    protected TempParkingOrderExtend doFromDO(TempParkingOrderExtendDO dataObject) {
        TempParkingOrderExtend domainObject = new TempParkingOrderExtend();
        JavaUtils.INSTANCE
            .acceptIfHasText(dataObject.getDescription(), domainObject::setDescription)
            .acceptIfHasText(dataObject.getAttach(), domainObject::setAttach)
            .acceptIfNotNull(dataObject.getExpireDuration(), domainObject::setExpireDuration)
            .acceptIfNotNull(dataObject.getPayScene(), sceneValue ->
                domainObject.setPayScene(PayScene.resolve(sceneValue)))
        ;

        domainObject.setReceivingMerchant(convertDataObject2Merchant(dataObject));

        return domainObject;
    }

    @Override
    protected TempParkingOrderExtendDO doToDO(TempParkingOrderExtend domainObject) {
        if (Objects.isNull(domainObject)) {
            return null;
        }

        TempParkingOrderExtendDO dataObject = new TempParkingOrderExtendDO();
        JavaUtils.INSTANCE
            .acceptIfHasText(domainObject.getDescription(), dataObject::setDescription)
            .acceptIfHasText(domainObject.getAttach(), dataObject::setAttach)
            .acceptIfNotNull(domainObject.getExpireDuration(), dataObject::setExpireDuration)
            .acceptIfNotNull(domainObject.getPayScene(), sense -> dataObject.setPayScene(sense.getValue()));

        convertMerchant2DataObject(domainObject.getReceivingMerchant(), dataObject);

        return dataObject;
    }

    private ParklotPayMerchant convertDataObject2Merchant(TempParkingOrderExtendDO dataObject) {
        ParklotPayMerchant merchant = new ParklotPayMerchant();
        merchant.setMerchantId(dataObject.getReceivingMchId());
        merchant.setMerchantNo(dataObject.getReceivingMchNo());
        merchant.setMerchantProviderCode(dataObject.getReceivingProviderCode());
        merchant.setPayChannelType(PayChannel.payChannelValue(dataObject.getPayChannelType()));
        return merchant;
    }

    private void convertMerchant2DataObject(ParklotPayMerchant merchant, TempParkingOrderExtendDO dataObject) {
        if (Objects.isNull(merchant)) {
            return;
        }
        dataObject.setReceivingMchId(merchant.getMerchantId());
        dataObject.setReceivingMchNo(merchant.getMerchantNo());
        dataObject.setReceivingProviderCode(merchant.getMerchantProviderCode());
        if (Objects.nonNull(merchant.getPayChannelType())) {
            dataObject.setPayChannelType(merchant.getPayChannelType().getValue());
        }

    }

}
