package com.leliven.park.infrastructure.gateway.persistence.basic.redis;

import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.entity.ParkingPlace;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParkingPlaceMapperService;
import com.leliven.ddd.core.cache.AbstractCacheMapper;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.CacheUtils;
import org.springblade.core.tool.utils.SpringUtil;

import java.util.Objects;
import java.util.Optional;

/**
 * 车位缓存类
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S6548")
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ParkingPlaceCacheMapper extends AbstractCacheMapper<ParkingPlace> {

	/**
	 * 车位缓存前缀
	 */
	private static final String PREFIX = CacheConstant.PARK_CACHE + "place:";
	/**
	 * 车位缓存前缀
	 */
	private static final String KEY_ID = PREFIX + "%s";

	public static final ParkingPlaceCacheMapper INSTANCE = new ParkingPlaceCacheMapper();

	private ParkingPlaceMapperService mapperService;


	/**
	 * 获取实例
	 *
	 * @return {@link ParkingPlaceCacheMapper}
	 */
	public static ParkingPlaceCacheMapper getInstance() {
		return INSTANCE;
	}

	/**
	 * 根据设备ID获取车位信息
	 *
	 * @param deviceId 设备ID
	 * @return {@link ParkingPlace}
	 */
	public ParkingPlace getByBoundDeviceId(Long deviceId) {
		Long parkingPlaceId = ParkingDeviceCacheMapper.INSTANCE.getParkingPlaceIdByDeviceId(deviceId);
		return Optional.ofNullable(parkingPlaceId).map(this::getById).orElse(null);
	}

	@Override
	protected ParkingPlace getByIdFromCache(Long id) {
		return CacheUtils.get(String.format(KEY_ID, id),
			ParkingPlace.class,
			() -> getMapperService().getById(id),
			// 缓存一个月
			CacheUtils.DEFAULT_1_MONTH
		);
	}

	@Override
	protected void delFromCache(Long id) {
		CacheUtils.delKey(String.format(KEY_ID, id));
	}

	private ParkingPlaceMapperService getMapperService() {
		if (Objects.isNull(mapperService)) {
			mapperService = SpringUtil.getBean(ParkingPlaceMapperService.class);
		}

		return mapperService;
	}
}
