package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.Parklot;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.domain.basic.parklot.model.valueobject.*;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotNameVO;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.utils.Func;

/**
 * 车场领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S6548", "DuplicatedCode"})
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ParklotConverter extends AbstractDomainEntityConverter<ParklotDomain, Parklot> {

	public static final ParklotConverter INSTANCE = new ParklotConverter();

	@Override
	protected ParklotDomain doFromDO(final Parklot dataObject) {
		ParklotDomain domainObject = new ParklotDomain();
		domainObject.setType(ParkLotType.resolve(dataObject.getParklotType()));
		domainObject.setCapacity(dataobject2ParklotCapacity(dataObject));
		domainObject.setOperationRules(dataobject2ParklotRules(dataObject));
		domainObject.setTempParkingChargeRuleSetting(dataobject2TempParkingChargeRuleSetting(dataObject));
		domainObject.setUniversalConfig(dataobject2ParklotUniversalConfig(dataObject));
		domainObject.setRentalCardRule(dataobject2ParklotRentalCardRule(dataObject));
		domainObject.setMiniAppsConfig(dataobject2ParklotMiniAppsConfig(dataObject));
		domainObject.setNo(dataObject.getParklotNo());
		if (dataObject.getIsNest() != null) {
			domainObject.setNest(dataObject.getIsNest());
		}

		domainObject.setName(dataObject.getName());
		domainObject.setImgUrl(dataObject.getParklotImgUrl());
		domainObject.setNamePinyinFull(dataObject.getNamePinyinFull());
		domainObject.setNamePinyinInitial(dataObject.getNamePinyinInitial());
		domainObject.setFullName(dataObject.getFullName());
		domainObject.setMemo(dataObject.getMemo());
		domainObject.setLng(dataObject.getLng());
		domainObject.setLat(dataObject.getLat());
		domainObject.setRegionCode(dataObject.getRegionCode());
		domainObject.setRegionAddress(dataObject.getRegionAddress());
		domainObject.setAddress(dataObject.getAddress());
		domainObject.setParentId(dataObject.getParentId());
		domainObject.setMerchantId(dataObject.getMerchantId());
		domainObject.setCreateDeptAncestors(dataObject.getCreateDeptAncestors());
		return domainObject;
	}

	@Override
	protected Parklot doToDO(ParklotDomain domainObject) {
		Parklot parklotDO = new Parklot();
		parklotDO.setName(domainObject.getName());
		parklotDO.setFullName(domainObject.getFullName());
		parklotDO.setNamePinyinFull(domainObject.getNamePinyinFull());
		parklotDO.setNamePinyinInitial(domainObject.getNamePinyinInitial());
		parklotDO.setParklotNo(domainObject.getNo());
		parklotDO.setMemo(domainObject.getMemo());
		parklotDO.setLng(domainObject.getLng());
		parklotDO.setLat(domainObject.getLat());
		parklotDO.setRegionCode(domainObject.getRegionCode());
		parklotDO.setRegionAddress(domainObject.getRegionAddress());
		parklotDO.setAddress(domainObject.getAddress());
		parklotDO.setParentId(domainObject.getParentId());
		parklotDO.setIsNest(domainObject.isNest());
		parklotDO.setMerchantId(domainObject.getMerchantId());
		parklotDO.setCreateDeptAncestors(domainObject.getCreateDeptAncestors());

		JavaUtils.INSTANCE
            .acceptIfNotNull(domainObject.getType(), type -> parklotDO.setParklotType(type.getValue()));

		return parklotDO;
	}

	public static ParklotNameVO domainToNameVO(ParklotDomain domain) {
		ParklotNameVO parklotNameVO = new ParklotNameVO();
		parklotNameVO.setId(domain.getId());
		parklotNameVO.setName(domain.getName());
		return parklotNameVO;
	}

	private ParklotTempParkingChargeRuleSetting dataobject2TempParkingChargeRuleSetting(Parklot parklotDO) {
		ParklotTempParkingChargeRuleSetting tempParkingChargeRuleSetting = new ParklotTempParkingChargeRuleSetting();
		tempParkingChargeRuleSetting.setDefaultRuleId(Func.toLong(parklotDO.getTempParkingChargeRuleId()));
		tempParkingChargeRuleSetting.setMotorcycleRuleId(parklotDO.getChargeRuleMotorcycleId());
		tempParkingChargeRuleSetting.setTricycleRuleId(parklotDO.getChargeRuleTricycleId());
		tempParkingChargeRuleSetting.setMediumSizedCarRuleId(parklotDO.getChargeRuleMediumSizedCarId());
		tempParkingChargeRuleSetting.setLargeVehicleRuleId(parklotDO.getChargeRuleLargeVehicleId());
		return tempParkingChargeRuleSetting;
	}

	private ParklotUniversalConfig dataobject2ParklotUniversalConfig(Parklot parklotDO) {
		ParklotUniversalConfig universalConfig = new ParklotUniversalConfig();
		universalConfig.setReceiptPrintConfigId(parklotDO.getReceiptPrintConfigId());
		return universalConfig;
	}

	protected ParklotMiniAppsConfig dataobject2ParklotMiniAppsConfig(Parklot parklotDO) {
		ParklotMiniAppsConfig parklotMiniAppsConfig = new ParklotMiniAppsConfig();
		parklotMiniAppsConfig.setScanMimiId(parklotDO.getScanMimiId());
		parklotMiniAppsConfig.setScanAliMimiId(parklotDO.getScanAliMimiId());
		return parklotMiniAppsConfig;
	}

	protected ParklotRentalCardRule dataobject2ParklotRentalCardRule(Parklot parklotDO) {
		ParklotRentalCardRule rentalCardRule = new ParklotRentalCardRule();
		rentalCardRule.setCardRules(parklotDO.getCardRules());
		rentalCardRule.setCardTypes(parklotDO.getCardTypes());
		rentalCardRule.setDayChangeRule(parklotDO.getDayChangeRule());
		rentalCardRule.setWeekChangeRule(parklotDO.getWeekChangeRule());
		rentalCardRule.setMonthChangeRule(parklotDO.getMonthChangeRule());
		rentalCardRule.setPlateChangeRules(parklotDO.getPlateChangeRules());
		return rentalCardRule;
	}

	protected ParklotCapacity dataobject2ParklotCapacity(final Parklot parklotDO) {
		ParklotCapacity parklotCapacity = new ParklotCapacity();
		parklotCapacity.setFloorAmount(parklotDO.getFloorAmount());
		parklotCapacity.setTotalAreaAmount(parklotDO.getTotalAreaAmount());
		parklotCapacity.setLotAmount(parklotDO.getLotAmount());
		parklotCapacity.setIndependentOwnershipAmount(parklotDO.getIndependentOwnershipAmount());
		parklotCapacity.setFloatingRatio(parklotDO.getFloatingRatio());
		parklotCapacity.setTempLotAmount(parklotDO.getTempLotAmount());
		parklotCapacity.setVipLotAmount(parklotDO.getVipLotAmount());
		parklotCapacity.setAccessibilityLotAmount(parklotDO.getAccessibilityLotAmount());
		parklotCapacity.setSoldLetterLotAmount(parklotDO.getSoldLetterLotAmount());
		parklotCapacity.setLettingLotAmount(parklotDO.getLettingLotAmount());
		parklotCapacity.setLetterLotAmount(parklotDO.getLetterLotAmount());
		parklotCapacity.setSoldLotAmount(parklotDO.getSoldLotAmount());
		return parklotCapacity;
	}

	protected ParklotOperationRules dataobject2ParklotRules(Parklot parklot) {
		ParklotOperationRules parklotOperationRules = new ParklotOperationRules();
		parklotOperationRules.setAutoEnter(Boolean.TRUE.equals(parklot.getAutoEnter()));
		parklotOperationRules.setAutoExit(Boolean.TRUE.equals(parklot.getAutoExit()));
		parklotOperationRules.setETCSupport(Boolean.TRUE.equals(parklot.getETCSupport()));
		parklotOperationRules.setLimitAutoBeginTime(parklot.getLimitAutoBeginTime());
		parklotOperationRules.setLimitAutoEndTime(parklot.getLimitAutoEndTime());
		parklotOperationRules.setSecondEnterConfirm(Boolean.TRUE.equals(parklot.getIsSecondEnterConfirm()));
		parklotOperationRules.setIsPlateInput(parklot.getIsPlateInput());
		parklotOperationRules.setNoParkingOrderExitSupport(Boolean.TRUE.equals(parklot.getNoParkingOrderExitSupport()));
		parklotOperationRules.setFuzzyMatchingPlateSupport(Boolean.TRUE.equals(parklot.getFuzzyMatchingPlateSupport()));
		parklotOperationRules.setFuzzyDisplay(Boolean.TRUE.equals(parklot.getFuzzyDisplay()));
		parklotOperationRules.setNoFeelSupport(Boolean.TRUE.equals(parklot.getNoFeelSupport()));
		parklotOperationRules.setRealTimeCalculate(Boolean.TRUE.equals(parklot.getRealTimeCalculate()));
		parklotOperationRules.setScanSub(Boolean.TRUE.equals(parklot.getIsScanSub()));
		parklotOperationRules.setScanSubLimitTime(Boolean.TRUE.equals(parklot.getIsScanSubLimitTime()));
		parklotOperationRules.setScanSubStartTime(parklot.getScanSubStartTime());
		parklotOperationRules.setScanSubEndTime(parklot.getScanSubEndTime());
		return parklotOperationRules;
	}
}

