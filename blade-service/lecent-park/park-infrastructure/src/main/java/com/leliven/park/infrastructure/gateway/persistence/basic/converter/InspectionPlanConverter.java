package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.leliven.park.domain.basic.inspection.model.InspectionPlan;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionExecutorAssignConfig;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionInterval;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.dataobject.InspectionPlanDO;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.ddd.core.valueobject.DateTimeRange;
import com.leliven.ddd.core.valueobject.TimeRange;
import com.leliven.ddd.core.valueobject.TimeUnit;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.Func;

import java.time.temporal.ChronoUnit;

/**
 * 巡检领域对象转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S6548"})
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionPlanConverter extends AbstractDomainEntityConverter<InspectionPlan, InspectionPlanDO> {

    public static final InspectionPlanConverter INSTANCE = new InspectionPlanConverter();

    /**
     * 获取实例
     */
    public static InspectionPlanConverter getInstance() {
        return INSTANCE;
    }

    /**
     * 将数据库中的间隔单位转换为TimeUnit
     */
    private static TimeUnit convertToTimeUnit(Integer intervalUnit) {
        if (intervalUnit == null) {
            return null;
        }
        switch (intervalUnit) {
            case 1:
                return TimeUnit.HOUR;
            case 2:
                return TimeUnit.DAY;
            case 3:
                return TimeUnit.WEEK;
            default:
                throw new IllegalArgumentException("无效的间隔单位：" + intervalUnit);
        }
    }


    @Override
    protected InspectionPlan doFromDO(InspectionPlanDO dataObject) {
        InspectionPlan plan = new InspectionPlan(dataObject.getId())
            .setName(dataObject.getName())
            .setValidityPeriod(DateTimeRange.of(dataObject.getStartDate(), dataObject.getEndDate()))
            .setWorkTimeRange(TimeRange.of(dataObject.getWorkTimeStart(), dataObject.getWorkTimeEnd()))
            .setInterval(InspectionInterval.of(dataObject.getIntervalUnit(), dataObject.getIntervalValue()))
            .setItemIds(Func.toLongList(dataObject.getItemIds()))
            .setParklotIds(Func.toLongList(dataObject.getParklotIds()))
            .setTaskPriority(dataObject.getTaskPriority())
            .setRemark(dataObject.getRemark());

        // 转换巡检间隔
        TimeUnit timeUnit = convertToTimeUnit(dataObject.getIntervalUnit());
        if (timeUnit != null && dataObject.getIntervalValue() != null) {
            plan.setInterval(InspectionInterval.of(timeUnit, dataObject.getIntervalValue()));
        }

        // 转换执行人配置
        if (Func.isNotBlank(dataObject.getExecutorConfig())) {
            plan.configureExecutorAssignConfig(
                JsonUtil.parse(dataObject.getExecutorConfig(), InspectionExecutorAssignConfig.class));
        }
        return plan;
    }

    @Override
    protected InspectionPlanDO doToDO(InspectionPlan domainObject) {
        InspectionPlanDO dataobject = new InspectionPlanDO();
        dataobject.setName(domainObject.getName());
        dataobject.setStartDate(domainObject.getValidityPeriod().getStartDateTime().truncatedTo(ChronoUnit.SECONDS));
        dataobject.setEndDate(domainObject.getValidityPeriod().getEndDateTime().truncatedTo(ChronoUnit.SECONDS));
        dataobject.setWorkTimeStart(domainObject.getWorkTimeRange().getStartTime());
        dataobject.setWorkTimeEnd(domainObject.getWorkTimeRange().getEndTime());
        dataobject.setIntervalUnit(domainObject.getIntervalUnit());
        dataobject.setIntervalValue(domainObject.getIntervalValue());
        dataobject.setItemIds(Func.join(domainObject.getItemIds()));
        dataobject.setParklotIds(Func.join(domainObject.getParklotIds()));
        dataobject.setExecutorConfig(Func.toJson(domainObject.getExecutorAssignConfig()));
        dataobject.setTaskPriority(domainObject.getTaskPriority());
        dataobject.setRemark(domainObject.getRemark());

        return dataobject;
    }
}
