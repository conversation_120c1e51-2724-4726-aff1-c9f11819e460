package com.leliven.park.infrastructure.gateway.persistence.order.converter;

import cn.hutool.core.math.Money;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.TempParkingOrder;
import com.lecent.pay.core.enums.PayWay;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.domain.common.PayStatus;
import com.leliven.park.domain.order.temp.model.TempParkingOrderDomain;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.vehicle.model.Vehicle;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 临停订单转换器
 *
 * <AUTHOR>
 */
@Component
public class TempParkingOrderConverter extends AbstractDomainEntityConverter<TempParkingOrderDomain, TempParkingOrder>
    implements DomainEntityConverter<TempParkingOrderDomain, TempParkingOrder> {

    @Override
    protected TempParkingOrderDomain doFromDO(TempParkingOrder dataObject) {
        if (dataObject == null) {
            return null;
        }

        TempParkingOrderDomain domainObject = new TempParkingOrderDomain();

        domainObject.setParkingId(dataObject.getParkingId());
        domainObject.setChannelId(dataObject.getChannelId());
        domainObject.setParklotId(dataObject.getParklotId());
        domainObject.setTodoId(dataObject.getTodoId());
        domainObject.setUnusualAmountType(dataObject.getUnusualAmountType());
        domainObject.setChargeData(ProjectCostConverter.fromJson(dataObject.getChargeData()));
        domainObject.setOpenId(dataObject.getOpenId());
        domainObject.setChargeUserId(dataObject.getChargeUserId());
        domainObject.setChargeUserDutyId(dataObject.getChargeUserDutyId());
        domainObject.setPayTime(dataObject.getPayTime());
        domainObject.setDeleteRemark(dataObject.getDeleteRemark());
        domainObject.setTradeNo(dataObject.getTradeNo());
        domainObject.setThirdTradeNo(dataObject.getThirdTradeNo());
        domainObject.setStatementDate(dataObject.getStatementDate());
        domainObject.setOrderIds(dataObject.getOrderIds());
        domainObject.setCouponIds(dataObject.getCouponIds());
        domainObject.setMerchantIds(dataObject.getMerchantIds());
        domainObject.setMemo(dataObject.getMemo());
        domainObject.setRemark(dataObject.getRemark());
        domainObject.setOrderStyle(dataObject.getOrderStyle());
        domainObject.setRefundStatus(dataObject.getRefundStatus());
        domainObject.setTargetId(dataObject.getTargetId());
        domainObject.setTargetTradeNo(dataObject.getTargetTradeNo());
        domainObject.setChargeUserLoginName(dataObject.getChargeUserLoginName());
        domainObject.setChargeUserRealName(dataObject.getChargeUserRealName());
        if (dataObject.getBeforePaid() != null) {
            domainObject.setBeforePaid(dataObject.getBeforePaid());
        }
        domainObject.setOrderUserId(dataObject.getOrderUserId());
        domainObject.setMasterOrderId(dataObject.getMasterOrderId());

        domainObject.setVehicle(Vehicle.of(dataObject.getPlate()));
        domainObject.setParkingCost(TempParkingOrderCostConverter.fromDO(dataObject));
        domainObject.setPayStatus(PayStatus.resolve(dataObject.getPayStatus()));
        domainObject.setPayType(PayWay.resolve(dataObject.getPayType()));
        domainObject.setCreateWay(CreateWay.getPayChannel(dataObject.getCreateWay()));

        return domainObject;
    }

    @Override
    protected TempParkingOrder doToDO(TempParkingOrderDomain domainObject) {
        TempParkingOrder dataObject = new TempParkingOrder();

        dataObject.setVehicleId(domainVehicleId(domainObject));
        dataObject.setPlate(domainVehiclePlate(domainObject));
        dataObject.setTotalAmount(domainParkingCostTotalAmount(domainObject));
        dataObject.setUnusualAmount(domainParkingCostUnusualAmount(domainObject));
        dataObject.setReceiveAmount(domainParkingCostReceiveAmount(domainObject));
        dataObject.setPaidAmount(domainParkingCostPaidAmount(domainObject));
        dataObject.setMerchantAmount(domainParkingCostMerchantAmount(domainObject));
        dataObject.setDiscountAmount(domainParkingCostDiscountAmount(domainObject));
        dataObject.setCouponDiscountAmount(domainParkingCostCouponDiscountAmount(domainObject));
        dataObject.setPayStatus(domainPayStatusValue(domainObject));
        dataObject.setCreateWay(domainCreateWayValue(domainObject));
        dataObject.setParkingId(domainObject.getParkingId());
        dataObject.setChannelId(domainObject.getChannelId());
        dataObject.setParklotId(domainObject.getParklotId());
        dataObject.setTradeNo(domainObject.getTradeNo());
        dataObject.setPayTime(domainObject.getPayTime());
        dataObject.setStatementDate(domainObject.getStatementDate());
        dataObject.setOrderIds(domainObject.getOrderIds());
        dataObject.setCouponIds(domainObject.getCouponIds());
        dataObject.setMerchantIds(domainObject.getMerchantIds());
        dataObject.setMemo(domainObject.getMemo());
        dataObject.setThirdTradeNo(domainObject.getThirdTradeNo());
        dataObject.setOrderStyle(domainObject.getOrderStyle());
        dataObject.setRefundStatus(domainObject.getRefundStatus());
        dataObject.setTargetId(domainObject.getTargetId());
        dataObject.setTargetTradeNo(domainObject.getTargetTradeNo());
        dataObject.setChargeUserDutyId(domainObject.getChargeUserDutyId());
        dataObject.setChargeUserId(domainObject.getChargeUserId());
        dataObject.setChargeUserLoginName(domainObject.getChargeUserLoginName());
        dataObject.setChargeUserRealName(domainObject.getChargeUserRealName());
        dataObject.setTodoId(domainObject.getTodoId());
        dataObject.setUnusualAmountType(domainObject.getUnusualAmountType());
        dataObject.setOpenId(domainObject.getOpenId());
        dataObject.setChargeData(ProjectCostConverter.toJson(domainObject.getChargeData()));
        dataObject.setBeforePaid(domainObject.isBeforePaid());
        dataObject.setOrderUserId(domainObject.getOrderUserId());
        dataObject.setDeleteRemark(domainObject.getDeleteRemark());
        dataObject.setMasterOrderId(domainObject.getMasterOrderId());
        dataObject.setPayType(domainObject.getPayType().getKey());

        return dataObject;
    }

    private Long domainVehicleId(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        Vehicle vehicle = tempParkingOrderDomain.getVehicle();
        if (vehicle == null) {
            return null;
        }
        return vehicle.getId();
    }

    private String domainVehiclePlate(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        Vehicle vehicle = tempParkingOrderDomain.getVehicle();
        if (vehicle == null) {
            return null;
        }
        return vehicle.getPlate();
    }

    private BigDecimal domainParkingCostTotalAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money totalAmount = parkingCost.getExpectedReceiveAmount();
        if (totalAmount == null) {
            return null;
        }
        return totalAmount.getAmount();
    }

    private BigDecimal domainParkingCostUnusualAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        if (!parkingCost.hasUnusualAmount()) {
            return null;
        }
        Money unusualAmount = parkingCost.getUnusualAmount();
        return unusualAmount.getAmount();
    }

    private BigDecimal domainParkingCostReceiveAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money receiveAmount = parkingCost.getPayableAmount();
        if (receiveAmount == null) {
            return null;
        }
        return receiveAmount.getAmount();
    }

    private BigDecimal domainParkingCostPaidAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money paidAmount = parkingCost.getActualReceiveAmount();
        if (paidAmount == null) {
            return null;
        }
        return paidAmount.getAmount();
    }

    private BigDecimal domainParkingCostMerchantAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money merchantAmount = parkingCost.getMerchantAmount();
        if (merchantAmount == null) {
            return null;
        }
        return merchantAmount.getAmount();
    }

    private BigDecimal domainParkingCostDiscountAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money discountAmount = parkingCost.getDiscountAmount();
        if (discountAmount == null) {
            return null;
        }
        return discountAmount.getAmount();
    }

    private BigDecimal domainParkingCostCouponDiscountAmount(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        ParkingCost parkingCost = tempParkingOrderDomain.getParkingCost();
        if (parkingCost == null) {
            return null;
        }
        Money couponDiscountAmount = parkingCost.getCouponDiscountAmount();
        if (couponDiscountAmount == null) {
            return null;
        }
        return couponDiscountAmount.getAmount();
    }

    private Integer domainPayStatusValue(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        PayStatus payStatus = tempParkingOrderDomain.getPayStatus();
        if (payStatus == null) {
            return null;
        }
        return payStatus.getValue();
    }

    private Integer domainCreateWayValue(TempParkingOrderDomain tempParkingOrderDomain) {
        if (tempParkingOrderDomain == null) {
            return null;
        }
        CreateWay createWay = tempParkingOrderDomain.getCreateWay();
        if (createWay == null) {
            return null;
        }
        return createWay.getValue();
    }
}
