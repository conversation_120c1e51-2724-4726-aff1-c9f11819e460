package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import cn.hutool.core.math.Money;
import com.lecent.park.entity.TempParkingChargeRule;
import com.lecent.park.entity.TempParkingSegmentCfg;
import com.leliven.ddd.core.valueobject.TimeRange;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingSegmentCfg;
import com.leliven.park.domain.basic.billingrule.model.valueobject.TempParkingChargeType;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import org.springblade.common.utils.JavaUtils;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * 临停计费规则领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/9/20
 */
@Component
public class TempParkingBillingRuleCfgConverter
    extends AbstractDomainEntityConverter<TempParkingBillingRuleCfg, TempParkingChargeRule> {

    /**
     * 临停分段配置表领域对象转换器
     */
    private final SegmentConfigConverter segmentConfigConverter;


    /**
     * 构造函数
     */
    public TempParkingBillingRuleCfgConverter() {
        segmentConfigConverter = new SegmentConfigConverter();
    }

    /**
     * 获取临停分段配置表领域对象转换器
     *
     * @return 临停分段配置表领域对象转换器
     */
    public SegmentConfigConverter segmentConfigConverter() {
        return segmentConfigConverter;
    }

    @Override
    protected TempParkingBillingRuleCfg doFromDO(TempParkingChargeRule dataObject) {
        TempParkingBillingRuleCfg rule = new TempParkingBillingRuleCfg();
        rule.setName(dataObject.getName());
        rule.setMemo(dataObject.getMemo());
        rule.setChargeType(TempParkingChargeType.resolve(Func.toInt(dataObject.getRuleType())));
        rule.setIncludeFreeTime(Func.toBoolean(dataObject.getIncludeFreeTime(), true));
        rule.setFreeDuration(Duration.ofMinutes(dataObject.getFreeTime()));
        rule.setFreeLeaveDurationAfterPaid(Duration.ofMinutes(dataObject.getPayLeaveTime()));
        rule.setParklotId(dataObject.getParklotId());
        rule.setChargePreTime(dataObject.getChargePreTime());
        rule.setChargePreFee(dataObject.getChargePreFee());
        rule.setChargePreMinute(dataObject.getChargePreMinute());
        rule.setPreTopTime(dataObject.getPreTopTime());
        rule.setPreTopFee(dataObject.getPreTopFee());
        rule.setPerNHour(dataObject.getPerNHour());
        rule.setPerNHourCappingFee(dataObject.getPerNHourCappingFee());
        rule.setPerFirstTime(dataObject.getPerFirstTime());
        rule.setPerFixedFree(dataObject.getPerFixedFree());

        return rule;
    }

    @Override
    protected TempParkingChargeRule doToDO(TempParkingBillingRuleCfg domainObject) {
        return null;
    }


    /**
     * 临停分段配置表实体转换器
     *
     * <p>
     * 用于将 {@link TempParkingSegmentCfg} 数据对象转换为领域对象 {@link TempParkingBillingSegmentCfg}
     * </p>
     * <AUTHOR>
     */
    public static class SegmentConfigConverter extends AbstractDomainEntityConverter<TempParkingBillingSegmentCfg, TempParkingSegmentCfg> {

        @Override
        protected TempParkingBillingSegmentCfg doFromDO(TempParkingSegmentCfg dataObject) {
            // beginTime和endTime为从0点开始的分钟数
            TimeRange billingTimeRange = TimeRange.ofMinutesWithCrossDay(dataObject.getBeginTime(), dataObject.getEndTime());

            Integer billingCycleMinutes = dataObject.getChargePeriodTime();
            Money billingCycleAmount = new Money(dataObject.getChargePeriodFee() == null ? BigDecimal.ZERO : dataObject.getChargePeriodFee());
            Money maxBillingAmount = new Money(dataObject.getMaxMoney() == null ? BigDecimal.ZERO : dataObject.getMaxMoney());
            Integer crossSegmentFreeMinutes = dataObject.getNextStageTime();

            return TempParkingBillingSegmentCfg.of(
                    billingTimeRange,
                    billingCycleMinutes,
                    billingCycleAmount,
                    maxBillingAmount,
                    crossSegmentFreeMinutes
            );
        }

        @Override
        protected TempParkingSegmentCfg doToDO(TempParkingBillingSegmentCfg domainObject) {
            TempParkingSegmentCfg dataObject = new TempParkingSegmentCfg();

            JavaUtils.INSTANCE
                .acceptIfNotNull(domainObject.getBillingTimeRange(), billingTimeRange -> {
                    dataObject.setBeginTime(billingTimeRange.getStartMinutes());
                    dataObject.setEndTime(billingTimeRange.getEndMinutes());
                })
                .acceptIfNotNull(domainObject.getBillingCycleMinutes(), dataObject::setChargePeriodTime)
                .acceptIfNotNull(domainObject.getBillingCycleAmount(), t -> dataObject.setChargePeriodFee(t.getAmount()))
                .acceptIfNotNull(domainObject.getMaxBillingAmount(), t -> dataObject.setMaxMoney(t.getAmount()))
                .acceptIfNotNull(domainObject.getCrossSegmentFreeMinutes(), dataObject::setNextStageTime);

            return dataObject;
        }
    }
}
