package com.leliven.park.infrastructure.gateway.persistence.basic.redis;

import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.entity.Parklot;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParklotMapperService;
import com.leliven.ddd.core.cache.AbstractCacheMapper;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.CacheUtils;
import org.springblade.core.tool.utils.SpringUtil;

import java.util.List;
import java.util.Objects;

/**
 * 车场缓存Mapper
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S6548")
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ParklotCacheMapper extends AbstractCacheMapper<Parklot> {

	private static final String PARK_LOT_ID = CacheConstant.PARK_CACHE + "parklot:";

	public static final ParklotCacheMapper INSTANCE = new ParklotCacheMapper();

	private ParklotMapperService mapperService;

	public static ParklotCacheMapper getInstance() {
		return INSTANCE;
	}

	/**
	 * 获取所有车场缓存对象
	 *
	 * @return 车场缓存对象列表
	 */
	public List<Parklot> listAll() {
		return CacheUtils.keys(PARK_LOT_ID + "*", Parklot.class);
	}

	@Override
	protected Parklot getByIdFromCache(Long id) {
		return CacheUtils.get(PARK_LOT_ID + id,
			Parklot.class,
			() -> getMapperService().getById(id),
			// 缓存一个月
			CacheUtils.DEFAULT_1_MONTH
		);
	}

	@Override
	protected void delFromCache(Long id) {
		CacheUtils.delKey(PARK_LOT_ID + id);
	}

	private ParklotMapperService getMapperService() {
		if (Objects.isNull(mapperService)) {
			mapperService = SpringUtil.getBean(ParklotMapperService.class);
		}

		return mapperService;
	}

}
