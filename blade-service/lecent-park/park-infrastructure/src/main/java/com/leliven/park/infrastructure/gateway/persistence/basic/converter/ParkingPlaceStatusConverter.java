package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.lecent.park.entity.ParkingPlaceStatusDO;
import com.leliven.park.domain.basic.place.model.ParkingPlaceStatus;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.domain.vehicle.VehicleRepositoryI;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.vehicle.model.Vehicle;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 停车位状态明细领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Component
public class ParkingPlaceStatusConverter extends AbstractDomainEntityConverter<ParkingPlaceStatus, ParkingPlaceStatusDO>
    implements DomainEntityConverter<ParkingPlaceStatus, ParkingPlaceStatusDO> {

	private final VehicleRepositoryI vehicleRepository;

	public ParkingPlaceStatusConverter(VehicleRepositoryI vehicleRepository) {
		this.vehicleRepository = vehicleRepository;
	}

	@Override
	protected ParkingPlaceStatus doFromDO(ParkingPlaceStatusDO dataObject) {
		if ( dataObject == null ) {
			return null;
		}

		ParkingPlaceStatus parkingPlaceStatus = new ParkingPlaceStatus();
		parkingPlaceStatus.setParkingPlaceId( dataObject.getPlaceId() );
		parkingPlaceStatus.setVehicle(Optional.ofNullable(vehicleRepository.getById(dataObject.getVehicleId()))
			.orElseGet(() -> Vehicle.of(dataObject.getPlate())));
		parkingPlaceStatus.setParkingOrderId( dataObject.getParkingOrderId() );
		parkingPlaceStatus.setParklotId( dataObject.getParklotId() );
		parkingPlaceStatus.setEnterTime( dataObject.getEnterTime() );
		parkingPlaceStatus.setLastParkingOrderId( dataObject.getLastParkingOrderId() );
		parkingPlaceStatus.setLastExitTime( dataObject.getLastExitTime() );
		parkingPlaceStatus.setIdleState( ParkingPlaceIdleState.resolve(dataObject.getIdeState()) );

		return parkingPlaceStatus;
	}

	@Override
	protected ParkingPlaceStatusDO doToDO(ParkingPlaceStatus domainObject) {
		ParkingPlaceStatusDO parkingPlaceStatusDO = new ParkingPlaceStatusDO();
		parkingPlaceStatusDO.setVehicleId( domainObject.getVehicle().getId() );
		parkingPlaceStatusDO.setPlate( domainObject.getVehicle().getPlate() );
		parkingPlaceStatusDO.setLastParkingOrderId( domainObject.getLastParkingOrderId() );
		parkingPlaceStatusDO.setParkingOrderId( domainObject.getParkingOrderId() );
		parkingPlaceStatusDO.setParklotId( domainObject.getParklotId() );
		parkingPlaceStatusDO.setPlaceId( domainObject.getParkingPlaceId() );
		parkingPlaceStatusDO.setEnterTime( domainObject.getEnterTime() );
		parkingPlaceStatusDO.setLastExitTime( domainObject.getLastExitTime() );
		parkingPlaceStatusDO.setIdeState( domainObjectIdleStateValue( domainObject ) );

		return parkingPlaceStatusDO;
	}

	private Integer domainObjectIdleStateValue(ParkingPlaceStatus parkingPlaceStatus) {
		if ( parkingPlaceStatus == null ) {
			return null;
		}
		ParkingPlaceIdleState idleState = parkingPlaceStatus.getIdleState();
		if ( idleState == null ) {
			return null;
		}
		return idleState.getValue();
	}

}
