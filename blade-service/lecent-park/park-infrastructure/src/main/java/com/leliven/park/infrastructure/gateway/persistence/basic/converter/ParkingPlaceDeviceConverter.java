package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.lecent.park.entity.ParklotDeviceRet;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.place.model.ParkingPlaceDeviceManager;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.device.model.BasicParkingDevice;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 车位状态设备领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ParkingPlaceDeviceConverter {

    public static ParkingPlaceDeviceManager fromDO(ParkingSpace parkingSpace, List<ParklotDeviceRet> dataObjects) {
        List<ParkingDevice> devices = Optional.ofNullable(dataObjects)
            .map(objs -> objs.stream().map(ParkingPlaceDeviceConverter::convertParklotDeviceRet2ParkingDevice).collect(Collectors.toList()))
            .orElseGet(ArrayList::new);
        return new ParkingPlaceDeviceManager(parkingSpace, devices);
    }

    public static List<ParklotDeviceRet> toDOList(ParkingPlaceDeviceManager domainObject) {
        return domainObject.getDevices().stream()
            .map(device -> convertParkingDevice2ParklotDeviceRet(domainObject.getParkingPlace(), device))
            .collect(Collectors.toList());
    }

    public static ParkingDevice convertParklotDeviceRet2ParkingDevice(ParklotDeviceRet dataObject) {
        if (dataObject == null) {
            return null;
        }
        BasicParkingDevice device = new BasicParkingDevice();
        device.setId(dataObject.getDeviceId());
        device.setSn(dataObject.getDeviceSn());
        device.setName(dataObject.getDeviceName());
        device.setType(DeviceType.resolve(dataObject.getDeviceType()));
        return device;
    }

    private static ParklotDeviceRet convertParkingDevice2ParklotDeviceRet(ParkingSpace parkingSpace, ParkingDevice device) {
        ParklotDeviceRet parklotDeviceRet = new ParklotDeviceRet();
        parklotDeviceRet.setDeviceId(device.getId());
        parklotDeviceRet.setDeviceSn(device.getSn());
        parklotDeviceRet.setDeviceName(device.getName());
        parklotDeviceRet.setDeviceType(device.getType().getValue());
        parklotDeviceRet.setParkPlaceId(parkingSpace.getId());
        parklotDeviceRet.setParklotId(parkingSpace.getParklotId());
        parklotDeviceRet.setRelationType(2);
        return parklotDeviceRet;
    }

    public static class ParkingDeviceConverter implements DomainEntityConverter<ParkingDevice, ParklotDeviceRet> {

        public static final ParkingDeviceConverter INSTANCE = new ParkingDeviceConverter();

        @Override
        public ParkingDevice fromDO(ParklotDeviceRet dataObject) {
            if (dataObject == null) {
                return null;
            }
            BasicParkingDevice device = new BasicParkingDevice();
            device.setId(dataObject.getDeviceId());
            device.setSn(dataObject.getDeviceSn());
            device.setName(dataObject.getDeviceName());
            device.setType(DeviceType.resolve(dataObject.getDeviceType()));
            return device;
        }

        @Override
        public ParklotDeviceRet toDO(ParkingDevice domainObject) {
            ParklotDeviceRet parklotDeviceRet = new ParklotDeviceRet();
            parklotDeviceRet.setDeviceId(domainObject.getId());
            parklotDeviceRet.setDeviceSn(domainObject.getSn());
            parklotDeviceRet.setDeviceName(domainObject.getName());
            parklotDeviceRet.setDeviceType(domainObject.getType().getValue());
            // TODO: parkPlaceId and parklotId are not part of the domain object
//            parklotDeviceRet.setParkPlaceId(domainObject.getId());
//            parklotDeviceRet.setParklotId(domainObject.getParklotId());
            parklotDeviceRet.setRelationType(2);
            return parklotDeviceRet;
        }
    }
}
