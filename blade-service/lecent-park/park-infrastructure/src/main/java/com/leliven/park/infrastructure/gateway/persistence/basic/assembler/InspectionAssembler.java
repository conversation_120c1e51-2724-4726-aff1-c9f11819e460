package com.leliven.park.infrastructure.gateway.persistence.basic.assembler;


import com.leliven.ddd.core.annotations.Assembler;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springblade.common.exception.NotFoundException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;

import com.leliven.park.domain.basic.inspection.model.valueobject.Inspector;
import com.leliven.park.domain.common.user.ParklotUserGateway;

import lombok.RequiredArgsConstructor;

/**
 * 巡检通用装配器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class InspectionAssembler {

    private final ParklotUserGateway parklotUserGateway;

    /**
     * 将当前用户转换为巡检人（当前用户为巡检员时使用）
     *
     * @return 巡检人 {@link Inspector}
     */
    public Inspector toInspectorByCurrentUser() {
        return this.toInspector(AuthUtil.getUserId());
    }

    /**
     * 将用户ID转换为巡检人
     *
     * @param userId 用户ID {@link Long}
     * @return 巡检人 {@link Inspector}
     */
    public Inspector toInspector(Long userId) {
        if (Objects.isNull(userId) || userId <= 0) {
            throw new ServiceException("巡检人ID不能为空");
        }

        // 获取巡检人
        return this.parklotUserGateway.getById(userId)
            .map(user -> Inspector.of(user.getId(), user.getRealName()))
            .orElseThrow(() -> new NotFoundException("未查询到巡检人，id:" + userId));
    }

    /**
     * 将用户ID列表转换为巡检人列表
     *
     * @param userIds 用户ID列表 {@link List}<{@link Long}>
     * @return 巡检人列表 {@link List}<{@link Inspector}>
     */
    public List<Inspector> toInspectors(List<Long> userIds) {
        return this.parklotUserGateway.listByIds(userIds)
            .stream()
            .map(user -> Inspector.of(user.getId(), user.getRealName()))
            .collect(Collectors.toList());
    }
}
