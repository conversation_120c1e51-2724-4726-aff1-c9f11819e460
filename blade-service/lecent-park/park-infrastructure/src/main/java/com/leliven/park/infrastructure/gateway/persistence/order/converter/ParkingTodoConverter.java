package com.leliven.park.infrastructure.gateway.persistence.order.converter;

import cn.hutool.core.math.Money;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.en.PassTemplate;
import com.lecent.park.en.channeltodo.ChargeEnum;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.entity.ChannelTodo;
import com.leliven.park.domain.common.ParkingCost;
import com.leliven.park.common.model.valueobject.MultipleChargeType;
import com.leliven.park.domain.order.parking.entity.AuthCardRelation;
import com.leliven.park.domain.order.parking.entity.ParkingEventInfo;
import com.leliven.park.domain.order.parking.entity.ParkingSchedule;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.order.todo.entity.ParkingTodo;
import com.leliven.park.domain.order.todo.entity.ParkingTodoEdition;
import com.leliven.park.domain.vehicle.VehicleRepositoryI;
import com.leliven.ddd.core.converter.AbstractDomainEntityConverter;
import com.leliven.vehicle.model.PlateVector;
import com.leliven.vehicle.model.Vehicle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 停车代办转换器
 *
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Slf4j
@Component
@RequiredArgsConstructor
public class ParkingTodoConverter extends AbstractDomainEntityConverter<ParkingTodo, ChannelTodo> {

	private final VehicleRepositoryI vehicleRepository;

	@Override
	protected ParkingTodo doFromDO(ChannelTodo dataObject) {
		ParkingTodo domainObject = new ParkingTodo();
		domainObject.setParklotId(dataObject.getParklotId());
		domainObject.setChannelNo(dataObject.getChannelNo());
		domainObject.setChannelType(dataObject.getChannelType());
		domainObject.setPlaceId(dataObject.getPlaceId());
		domainObject.setParkingOrderId(dataObject.getParkingId());
		domainObject.setCouponId(dataObject.getCouponId());
		domainObject.setPassType(PassTemplate.resolve(dataObject.getPassType()));
		domainObject.setType(dataObject.getType());
		domainObject.setNo(dataObject.getNo());
		domainObject.setChargeRuleId(dataObject.getChargeRuleId());
		domainObject.setChargeType(ChargeEnum.resolve(dataObject.getChargeType()));
		domainObject.setReasonId(dataObject.getReasonId());
		domainObject.setOriginalData(dataObject.getOriginalData());
		domainObject.setAfterCarParkingId(dataObject.getAfterCarParkingId());
		domainObject.setMultipleChargeType(MultipleChargeType.resolve(dataObject.getMultipleChargeType()));
		domainObject.setEnterTime(dataObject.getEnterTime());
		domainObject.setEnterImageUrl(dataObject.getEnterImageUrl());
		domainObject.setDuration(dataObject.getDuration());
		domainObject.setTargetParkingId(dataObject.getTargetParkingId());
		domainObject.setTypeDes(dataObject.getTypeDes());
		domainObject.setMerchantId(dataObject.getMerchantId());
		domainObject.setStatus(dataObject.getStatus());
		domainObject.setCardTakeEffect(Func.toBoolean(dataObject.getCardTakeEffect(), false));
		domainObject.setOccupied(Func.toBoolean(dataObject.getOccupied(), false));
		domainObject.setTempCar(Func.toBoolean(dataObject.getTempCar(), true));
		domainObject.setBeforePaid(Func.toBoolean(dataObject.getBeforePaid(), false));
		domainObject.setVersion(dataObject.getVersion());
		domainObject.setOverTimeAmount(new Money(dataObject.getOverTimeAmount()));
		domainObject.setCardInfo(CardInfoConverter.toCardInfo(dataObject.getCardInfo()));
		domainObject.setCreateWay(CreateWay.getPayChannel(dataObject.getCreateWay()));
		domainObject.setEdition(
			ParkingTodoEdition.builder()
				.edit(Func.toBoolean(dataObject.getEdit(), false))
				.editPlate(Func.toBoolean(dataObject.getEditPlate(), false))
				.editParkingId(Func.toBoolean(dataObject.getEditParkingId(), false))
				.editEnterTime(Func.toBoolean(dataObject.getEditEnterTime(), false))
				.build()
		);
		domainObject.setParkingInfo(toParkingEventInfo(dataObject));
		domainObject.setAuthCardRelation(toAuthCardRelation(dataObject));
		domainObject.setData(ProjectCostConverter.fromJson(dataObject.getData()));
		domainObject.getParkingCost().add(ParkingTodoCostConverter.fromTodo(dataObject));
		domainObject.setParkingSchedule(
			ParkingScheduleConverter.from(dataObject.getParkingTimeNode(), dataObject.getTimeNodeDetail())
		);

		domainObject.setDays(dataObject.getDays());
		domainObject.setExtendVoice(dataObject.getExtendVoice());
		domainObject.setRoomNum(dataObject.getRoomNum());
		domainObject.setName(dataObject.getName());
		domainObject.setPhone(dataObject.getPhone());
		domainObject.setPlaceNum(dataObject.getPlaceNum());
		domainObject.setRemark(dataObject.getRemark());
		domainObject.setPayType(dataObject.getPayType());
		domainObject.setOrderUserId(dataObject.getOrderUserId());
		domainObject.setMqOpenGate(Func.toBoolean(dataObject.getIsMqOpenGate(), true));
		domainObject.setSecondExit(Func.toBoolean(dataObject.getIsSecondExit(), false));
		domainObject.setCycleCar(Func.toBoolean(dataObject.getIsCycleCar(), false));
		domainObject.setTempParkingOrderId(dataObject.getTempParkingOrderId());
		domainObject.setLastPayTime(dataObject.getLastPayTime());

		return domainObject;
	}

	@Override
	protected ChannelTodo doToDO(ParkingTodo domainObject) {
		ChannelTodo dataObject = new ChannelTodo();
		dataObject.setParklotId(domainObject.getParklotId());
		dataObject.setChannelNo(domainObject.getChannelNo());
		dataObject.setChannelType(domainObject.getChannelType());
		dataObject.setPlaceId(domainObject.getPlaceId());
		dataObject.setParkingId(domainObject.getParkingOrderId());
		dataObject.setCouponId(domainObject.getCouponId());
		if (Objects.nonNull(domainObject.getPassType())) {
			dataObject.setPassType(domainObject.getPassType().name());
		}
		dataObject.setType(domainObject.getType());
		dataObject.setNo(domainObject.getNo());
		dataObject.setChargeRuleId(domainObject.getChargeRuleId());
		dataObject.setChargeType(domainObject.getChargeType().name());
		dataObject.setReasonId(domainObject.getReasonId());
		dataObject.setOriginalData(domainObject.getOriginalData());
		dataObject.setAfterCarParkingId(domainObject.getAfterCarParkingId());
		dataObject.setMultipleChargeType(domainObject.getMultipleChargeType().getValue());
		dataObject.setEnterTime(domainObject.getEnterTime());
		dataObject.setEnterImageUrl(domainObject.getEnterImageUrl());
		dataObject.setDuration(domainObject.getDuration());
		dataObject.setTargetParkingId(domainObject.getTargetParkingId());
		dataObject.setTypeDes(domainObject.getTypeDes());
		dataObject.setMerchantId(domainObject.getMerchantId());
		dataObject.setStatus(domainObject.getStatus());
		dataObject.setCardTakeEffect(domainObject.isCardTakeEffect());
		dataObject.setOccupied(domainObject.isOccupied());
		dataObject.setTempCar(domainObject.isTempCar());
		dataObject.setBeforePaid(domainObject.isBeforePaid());
		dataObject.setVersion(domainObject.getVersion());
		dataObject.setOverTimeAmount(domainObject.getOverTimeAmount().getAmount());
		dataObject.setCardInfo(CardInfoConverter.toCardInfoJsonStr(domainObject.getCardInfo()));
		dataObject.setCreateWay(domainObject.getCreateWay().getValue());
		parkingEventInfo2DO(domainObject.getParkingInfo(), dataObject);
		authCardRelation2DO(domainObject.getAuthCardRelation(), dataObject);
		parkingSchedule2DO(domainObject.getParkingSchedule(), dataObject);
		parkingTodoEdition2DO(domainObject.getEdition(), dataObject);
		parkingCost2DO(domainObject.getParkingCost(), dataObject);
		dataObject.setData(ProjectCostConverter.toJson(domainObject.getData()));

		dataObject.setDays(domainObject.getDays());
		dataObject.setExtendVoice(domainObject.getExtendVoice());
		dataObject.setRoomNum(domainObject.getRoomNum());
		dataObject.setName(domainObject.getName());
		dataObject.setPhone(domainObject.getPhone());
		dataObject.setPlaceNum(domainObject.getPlaceNum());
		dataObject.setRemark(domainObject.getRemark());
		dataObject.setPayType(domainObject.getPayType());
		dataObject.setOrderUserId(domainObject.getOrderUserId());
		dataObject.setIsMqOpenGate(domainObject.isMqOpenGate());
		dataObject.setIsSecondExit(domainObject.isSecondExit());
		dataObject.setIsCycleCar(domainObject.isCycleCar());
		dataObject.setTempParkingOrderId(dataObject.getTempParkingOrderId());
		dataObject.setLastPayTime(domainObject.getLastPayTime());

		return dataObject;
	}

	AuthCardRelation toAuthCardRelation(ChannelTodo channelTodoDO) {
		if (channelTodoDO == null) {
			return null;
		}

		return AuthCardRelation.builder()
			.relationId(channelTodoDO.getRelationId())
			.cardId(channelTodoDO.getCardId())
			.cardType(channelTodoDO.getCardType())
			.cardIds(Func.toLongList(channelTodoDO.getTempCardIds()))
			.relationType(PlatePropertyType.resolve(channelTodoDO.getRelationType()))
			.build();
	}

	ParkingEventInfo toParkingEventInfo(ChannelTodo channelTodoDO) {
		if (channelTodoDO == null) {
			return null;
		}

		Vehicle vehicle = this.vehicleRepository.getById(channelTodoDO.getVehicleId(),
			() -> Vehicle.of(channelTodoDO.getPlate()));
		vehicle.plateVector(PlateVector.of(channelTodoDO.getOpenId()));

		ParkingEventInfo parkingEventInfo = new ParkingEventInfo();
		parkingEventInfo.setTodoId(channelTodoDO.getId());
		parkingEventInfo.setDatetime(channelTodoDO.getDate());
		parkingEventInfo.setChannelId(channelTodoDO.getChannelId());
		parkingEventInfo.setImageUrl(channelTodoDO.getImageUrl());
		parkingEventInfo.setOptUserId(channelTodoDO.getHandUserId());
		parkingEventInfo.setOpenGateCost(0);
		parkingEventInfo.setTriggerWay(ParkingTriggerWay.resolve(channelTodoDO.getTriggerType()));
		parkingEventInfo.setVehicle(vehicle);

		return parkingEventInfo;
	}

	void parkingEventInfo2DO(ParkingEventInfo parkingEventInfo, ChannelTodo channelTodoDO) {
		Objects.requireNonNull(parkingEventInfo);
		Objects.requireNonNull(channelTodoDO);

		channelTodoDO.setDate(parkingEventInfo.getDatetime());
		channelTodoDO.setChannelId(parkingEventInfo.getChannelId());
		channelTodoDO.setImageUrl(parkingEventInfo.getImageUrl());
		channelTodoDO.setHandUserId(parkingEventInfo.getOptUserId());
		channelTodoDO.setTriggerType(parkingEventInfo.getTriggerWay().getValue());
		channelTodoDO.setVehicleId(parkingEventInfo.getVehicle().getId());
		channelTodoDO.setPlate(parkingEventInfo.getVehicle().getPlate());
		channelTodoDO.setOpenId(parkingEventInfo.getVehicle().getPlateVectorNo());
	}

	void authCardRelation2DO(AuthCardRelation authCardRelation, ChannelTodo channelTodoDO) {
		Objects.requireNonNull(authCardRelation);
		Objects.requireNonNull(channelTodoDO);

		channelTodoDO.setCardId(authCardRelation.getCardId());
		channelTodoDO.setCardType(authCardRelation.getCardType());
		channelTodoDO.setTempCardIds(Func.join(authCardRelation.getCardIds()));
		channelTodoDO.setRelationId(authCardRelation.getRelationId());
		channelTodoDO.setRelationType(authCardRelation.getRelationTypeValue());
	}

	void parkingSchedule2DO(ParkingSchedule parkingSchedule, ChannelTodo channelTodoDO) {
		Objects.requireNonNull(parkingSchedule);
		Objects.requireNonNull(channelTodoDO);

		channelTodoDO.setParkingTimeNode(
			ParkingTimeNodeConverter.toParkingTimeNodesJsonStr(parkingSchedule.getParkingTimeNodes()));
		channelTodoDO.setTimeNodeDetail(
			ChargePeriodDetailConverter.toChargePeriodDetailJsonStr(parkingSchedule.getChargePeriodDetails())
		);
	}

	void parkingTodoEdition2DO(ParkingTodoEdition parkingTodoEdition, ChannelTodo channelTodoDO) {
		Objects.requireNonNull(parkingTodoEdition);
		Objects.requireNonNull(channelTodoDO);

		channelTodoDO.setEdit(parkingTodoEdition.isEdit());
		channelTodoDO.setEditPlate(parkingTodoEdition.isEditPlate());
		channelTodoDO.setEditParkingId(parkingTodoEdition.isEditParkingId());
		channelTodoDO.setEditEnterTime(parkingTodoEdition.isEditEnterTime());
	}

	void parkingCost2DO(ParkingCost parkingCost, ChannelTodo channelTodoDO) {
		Objects.requireNonNull(parkingCost);
		Objects.requireNonNull(channelTodoDO);

		channelTodoDO.setTotalAmount(parkingCost.getExpectedReceiveAmount().getAmount());
		channelTodoDO.setReceiveAmount(parkingCost.getPayableAmount().getAmount());
		channelTodoDO.setPaidAmount(parkingCost.getActualReceiveAmount().getAmount());
		channelTodoDO.setDiscountAmount(parkingCost.getDiscountAmount().getAmount());
		channelTodoDO.setMerchantAmount(parkingCost.getMerchantAmount().getAmount());
		channelTodoDO.setCouponDiscountAmount(parkingCost.getCouponDiscountAmount().getAmount());
		channelTodoDO.setErrorAmount(parkingCost.getUnusualAmount().getAmount());
		channelTodoDO.setErrorAmountType(parkingCost.getUnusualAmountType());
	}
}
