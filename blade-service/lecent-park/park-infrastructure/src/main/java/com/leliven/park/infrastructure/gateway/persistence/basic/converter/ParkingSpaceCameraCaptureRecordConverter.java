package com.leliven.park.infrastructure.gateway.persistence.basic.converter;

import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCaptureRecord;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureType;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.infrastructure.gateway.persistence.basic.doris.dataobject.ParkingPlaceCameraCaptureRecordDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import com.leliven.vehicle.enums.PlateColor;
import com.leliven.vehicle.model.Vehicle;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * 车位摄像头抓拍记录
 *
 * <AUTHOR>
 */
@Mapper(imports = {Vehicle.class, ParkingPlaceCameraCaptureType.class, ParkingPlaceIdleState.class, DeviceType.class})
public interface ParkingSpaceCameraCaptureRecordConverter
	extends DomainEntityConverter<ParkingPlaceCameraCaptureRecord, ParkingPlaceCameraCaptureRecordDO> {

	ParkingSpaceCameraCaptureRecordConverter INSTANCE = Mappers.getMapper(ParkingSpaceCameraCaptureRecordConverter.class);


	@Mapping(target = "deviceType", expression = "java(DeviceType.resolve(dataObject.getDeviceType()))")
	@Mapping(target = "vehicle", expression = "java(Vehicle.of(dataObject.getPlate(), dataObject.getPlateColorCode()))")
	@Mapping(target = "type", expression = "java(ParkingPlaceCameraCaptureType.resolve(dataObject.getType()))")
	@Mapping(target = "parkingState", expression = "java(ParkingPlaceIdleState.resolve(dataObject.getParkingState()))")
	@Mapping(target = "belongToParkingSpace", ignore = true)
	@Override
	ParkingPlaceCameraCaptureRecord fromDO(ParkingPlaceCameraCaptureRecordDO dataObject);

	@Mapping(target = "plateColorCnName", source = "domainObject.vehicle", qualifiedByName = "resolveVehiclePlateColorCnName")
	@Mapping(target = "plateColorCode", source = "domainObject.vehicle", qualifiedByName = "resolveVehiclePlateColor")
	@Mapping(target = "plate", source = "domainObject.vehicle", qualifiedByName = "resolveVehiclePlate")
	@Mapping(target = "type", source = "domainObject.type.value")
	@Mapping(target = "parkingState", source = "domainObject.parkingState", qualifiedByName = "resolveParkingState")
	@Mapping(target = "deviceType", source = "domainObject.deviceType", qualifiedByName = "resolveDeviceType")
	@Override
	ParkingPlaceCameraCaptureRecordDO toDO(ParkingPlaceCameraCaptureRecord domainObject);


	@Named("resolveVehiclePlate")
    default String resolveVehiclePlate(Vehicle vehicle) {
        return vehicle != null ? vehicle.getPlate() : Vehicle.NO_PLATE;
    }

    @Named("resolveVehiclePlateColor")
    default String resolveVehiclePlateColor(Vehicle vehicle) {
        return vehicle != null ? vehicle.getPlateColor().code() : PlateColor.UNKNOWN.code();
    }

    @Named("resolveVehiclePlateColorCnName")
    default String resolveVehiclePlateColorCnName(Vehicle vehicle) {
        return vehicle != null ? vehicle.getPlateColor().chineseName() : PlateColor.UNKNOWN.chineseName();
    }

	@Named("resolveParkingState")
    default Integer resolveParkingState(ParkingPlaceIdleState parkingState) {
        return parkingState != null ? parkingState.getValue() : ParkingPlaceIdleState.UNKNOWN.getValue();
    }

	@Named("resolveDeviceType")
	default Integer resolveDeviceType(DeviceType deviceType) {
		return deviceType != null ? deviceType.getValue() : DeviceType.UNKNOWN.getValue();
	}
}
