package com.leliven.park.infrastructure.gateway.persistence.basic.redis;

import com.lecent.park.common.constant.CacheConstant;
import com.lecent.park.vo.TempParkingChargeRuleVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.TempParkingBillingRuleCfgEnhancedMapper;
import com.leliven.ddd.core.cache.AbstractCacheMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.CacheUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import static org.springblade.common.utils.CacheUtils.DEFAULT_1_WEEK;

/**
 * 临停计费规则缓存 Mapper
 *
 * <AUTHOR>
 * @since 2024/9/20
 */
@SuppressWarnings("squid:S6548")
@Component
@RequiredArgsConstructor
public class TempParkingBillingRuleCfgCacheMapper extends AbstractCacheMapper<TempParkingChargeRuleVO> {

	private static final String TEMP_PARKING_CHARGE_RULE = CacheConstant.PARK_CACHE + "tempParkingChargeRule:";

	@Getter
	private static TempParkingBillingRuleCfgCacheMapper instance;
	private final TempParkingBillingRuleCfgEnhancedMapper enhancedMapper;

	@PostConstruct
	public void init() {
		setInstance(this);
	}

	@Override
	protected TempParkingChargeRuleVO getByIdFromCache(Long id) {
		return CacheUtils.get(
			TEMP_PARKING_CHARGE_RULE + id,
			TempParkingChargeRuleVO.class,
			() -> enhancedMapper.selectOneById(id),
			DEFAULT_1_WEEK
		);
	}

	@Override
	protected void delFromCache(Long id) {
		CacheUtils.delKeyAfterTxCommitted(TEMP_PARKING_CHARGE_RULE + id);
	}

	public static TempParkingBillingRuleCfgCacheMapper instance() {
		return instance;
	}

	private static void setInstance(TempParkingBillingRuleCfgCacheMapper cacheMapper) {
		instance = cacheMapper;
	}
}
