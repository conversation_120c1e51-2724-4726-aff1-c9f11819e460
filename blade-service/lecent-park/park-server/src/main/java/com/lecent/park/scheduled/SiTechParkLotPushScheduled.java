package com.lecent.park.scheduled;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lecent.park.core.thirdparty.dto.sitech.UpParkingSpaceDTO;
import com.lecent.park.core.thirdparty.manager.ISiTechService;
import com.lecent.park.core.thirdparty.properties.SiTechPushProperties;
import com.lecent.park.entity.Parklot;
import com.lecent.park.service.IParklotService;
import com.lecent.park.service.IParklotSpaceCalculateService;
import com.lecent.park.service.ITempParkingChargeRuleService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 独山县 - 车场信息推送
 * 第三方：北京思特奇信息技术股份有限公司接口
 * 5分钟推送一次
 * <AUTHOR>
 * @date 2024-08-14
 */
@Slf4j
@Component
@AllArgsConstructor
public class SiTechParkLotPushScheduled {

	private final IParklotService parkLotService;
	private final IParklotSpaceCalculateService parklotSpaceCalculateService;

	private final ITempParkingChargeRuleService tempParkingChargeRuleService;
	private final ISiTechService siTechService;

	private final SiTechPushProperties siTechPushProperties;

	@XxlJob("parkingJobHandler-5m")
	public void siTechParkLotPush() {
		if(!siTechPushProperties.isEnable()){
			return;
		}
		log.info("SiTechParkLotPushScheduled 开始执行定时推送车场....");

		List<String> listTenantId = siTechPushProperties.getTenantIds();

		for(String tenantId : listTenantId){
			List<Parklot> parklotList = parkLotService.list(new QueryWrapper<Parklot>().lambda()
				.eq(Parklot::getTenantId, tenantId));
			for (Parklot parklot : parklotList) {

				UpParkingSpaceDTO upParkingSpaceDTO = new UpParkingSpaceDTO();
				upParkingSpaceDTO.setTenantId(parklot.getTenantId());
				upParkingSpaceDTO.setParkId(parklot.getId().toString());
				upParkingSpaceDTO.setParkName(parklot.getName());
				upParkingSpaceDTO.setTotalParkingSpace(parklot.getTempLotAmount());
				int remainNumber = parklotSpaceCalculateService.getRemainNumber(parklot);
				upParkingSpaceDTO.setEmptyParkingSpace(remainNumber);

				upParkingSpaceDTO.setUsedParkingSpace(parklotSpaceCalculateService.getUsedNumber(parklot));

				if(Func.isNotBlank(parklot.getRegionAddress())){
					List<String> listRegionAddress = Func.toStrList(parklot.getRegionAddress());
					if(listRegionAddress.size() == 4){
						upParkingSpaceDTO.setProvince(listRegionAddress.get(1));
						upParkingSpaceDTO.setCity(listRegionAddress.get(2));
						upParkingSpaceDTO.setCountry(listRegionAddress.get(3));
					}else{
						upParkingSpaceDTO.setProvince("null");
						upParkingSpaceDTO.setCity("null");
						upParkingSpaceDTO.setCountry("null");
					}
				}else{
					upParkingSpaceDTO.setProvince("null");
					upParkingSpaceDTO.setCity("null");
					upParkingSpaceDTO.setCountry("null");
				}

				if(Func.isNotBlank(parklot.getAddress())){
					upParkingSpaceDTO.setAddress(parklot.getAddress());
				}else{
					upParkingSpaceDTO.setAddress("null");
				}

				upParkingSpaceDTO.setOpenStatus("1");
				if(Func.isNotBlank(parklot.getTempParkingChargeRuleId())){
					upParkingSpaceDTO.setChargeRule(tempParkingChargeRuleService.chargeRuleDesc(parklot.getTempParkingChargeRuleId()));
				}
				if(Func.isBlank(upParkingSpaceDTO.getChargeRule())){
					upParkingSpaceDTO.setChargeRule("null");
				}

				upParkingSpaceDTO.setLongitude(parklot.getLng().doubleValue());
				upParkingSpaceDTO.setLatitude(parklot.getLat().doubleValue());
				upParkingSpaceDTO.setTrade("贵州乐云科技有限公司");
				log.info("SiTechParkLotPushScheduled：{}", JSONObject.toJSONString(upParkingSpaceDTO));
				siTechService.upParkingSpace(upParkingSpaceDTO);
			}
		}


	}
}
