package com.lecent.park.listener;

import com.lecent.device.constant.DeviceMQConstant;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.lecent.park.core.notify.base.BaseMsgReceiver;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.service.IParklotDeviceRetService;
import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.park.application.common.event.AlarmDomainEvent;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingPlaceStatus;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.LecentAppConstant;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.Optional;


/**
 * 设备告警监听
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceAlarmListener extends BaseMsgReceiver<AlarmEvent> {

	public static final String ROUTING_KEY = "leliven.alarm.up";
	private final MqSender mqSender;
    private final ParkingPlaceGateway parkingPlaceGateway;
	private final IParklotDeviceRetService parklotDeviceRetService;

	protected DeviceAlarmListener(MqSender mqSender,
                                  ParkingPlaceGateway parkingPlaceGateway,
                                  IParklotDeviceRetService parklotDeviceRetService) {
		super(LecentAppConstant.APPLICATION_DEVICE_NAME, DeviceMQConstant.DEVICE_ALARM_EVENT_CODE);
		this.mqSender = mqSender;
        this.parkingPlaceGateway = parkingPlaceGateway;
		this.parklotDeviceRetService = parklotDeviceRetService;
    }

	@Override
	public void accept(AlarmEvent alarmEvent) {
        // 其他告警先不处理
		if ("other".equals(alarmEvent.getAlarmClass())) {
			return;
		}

        try {

            Optional<ParklotDeviceRet> parklotDeviceRetOpt = parklotDeviceRetService.getOneOptByDeviceId(alarmEvent.getDeviceId());
		    if (!parklotDeviceRetOpt.isPresent()) {
			    return;
	    	}

            ParklotDeviceRet parklotDeviceRet = parklotDeviceRetOpt.get();

            // 车位设备
            if (2 == parklotDeviceRet.getRelationType()) {
                alarmEvent.setDeviceSn(parklotDeviceRet.getDeviceSn());
                alarmEvent.setPlaceId(parklotDeviceRet.getParkPlaceId());
                parkingPlaceGateway.asCacheSupplier().getOptional(parklotDeviceRet.getParkPlaceId())
                    .ifPresent(t -> {
                        ParkingPlaceStatus realTimeStatus = t.getRealTimeStatus();
                        alarmEvent.setPlate(realTimeStatus.getVehicle().getPlate());
                        alarmEvent.setParkingId(realTimeStatus.getParkingOrderId());
                    });
            }

            Parklot parklot = ParkLotCaches.existParkLot(parklotDeviceRet.getParklotId());
            alarmEvent.setParklotId(parklot.getId());


            SpringDomainEventPublisher.publish(new AlarmDomainEvent(this, alarmEvent));

            mqSender.sendMessage(Func.toJson(alarmEvent), ROUTING_KEY, Exchanges.AMQ_TOPIC);
        } catch (Exception e) {
            log.error("设备告警监听处理失败：", e);
        }
    }
}
