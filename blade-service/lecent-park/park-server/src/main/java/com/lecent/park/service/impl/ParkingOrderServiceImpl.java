package com.lecent.park.service.impl;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.access.ParkAccessClient;
import com.lecent.park.access.ParkTodoContext;
import com.lecent.park.access.exit.ExitNestParkLotService;
import com.lecent.park.biz.impl.RoadSideParkingBiz;
import com.lecent.park.charge.CostDetail;
import com.lecent.park.charge.ProjectCost;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.charge.ProjectCalculate;
import com.lecent.park.common.constant.*;
import com.lecent.park.common.enums.parkinglot.PayQrCodeType;
import com.lecent.park.common.enums.plate.PlatePhoneSourceEnum;
import com.lecent.park.common.enums.plate.PlatePropertyType;
import com.lecent.park.common.utils.DateUtils;
import com.lecent.park.common.utils.*;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.controller.ebo.mini.req.QueryType;
import com.lecent.park.core.log.utils.AuditLogger;
import com.lecent.park.core.notify.api.IMsgSender;
import com.lecent.park.core.notify.domain.MsgRequest;
import com.lecent.park.core.tool.wx.WxUtils;
import com.lecent.park.dto.*;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.en.channeltodo.ChargeEnum;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.en.temprule.CarType;
import com.lecent.park.en.unpaidorder.UnpaidOrderType;
import com.lecent.park.entity.*;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.event.channeltodo.ReturnParkPlaceEvent;
import com.lecent.park.excel.ParkingOrderExcel;
import com.lecent.park.excel.ParkingPresentOrderExcel;
import com.lecent.park.service.*;
import com.lecent.park.utils.excel.ExcelUtils;
import com.lecent.park.utils.excel.F;
import com.lecent.park.utils.excel.handle.ImportHandler;
import com.lecent.park.vo.*;
import com.lecent.park.vo.parkingOrderVO.PayCodeByOrderVO;
import com.lecent.pay.core.enums.PayWay;
import com.lecent.pay.third.wxpay.MiniApi;
import com.leliven.park.application.order.ParkingOrderAppServiceI;
import com.leliven.park.application.parking.RoadsideParkingAppServiceI;
import com.leliven.park.application.parking.cmd.ManualCompensatoryRegisterCommand;
import com.leliven.park.application.parking.dto.ParkingEventInfoDTO;
import com.leliven.park.application.vehicle.assembler.VehicleAssembler;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.domain.order.parking.event.ParkingOrderDomainEvent;
import com.leliven.park.domain.order.parking.event.ParkingOrderEventType;
import com.leliven.park.domain.parking.core.support.publisher.ParkingSuccessfulPassagePublisherI;
import com.leliven.park.domain.parking.core.support.specialexit.ParkingPaidCompensateExitHandler;
import com.leliven.park.domain.parking.image.ParkingImageRepositoryI;
import com.leliven.park.domain.parking.image.entity.ParkingImage;
import com.leliven.park.domain.parking.image.entity.ParkingImageType;
import com.leliven.park.domain.parking.image.entity.ParkingImages;
import com.leliven.park.domain.vehicle.support.VehicleDomainService;
import com.leliven.park.infrastructure.gateway.persistence.basic.redis.ParkingPlaceCacheMapper;
import com.leliven.park.infrastructure.gateway.persistence.basic.redis.ParklotCacheMapper;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotDeviceRetVO;
import com.leliven.park.infrastructure.gateway.persistence.order.converter.ParkingOrderConverter;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.OrderMapper;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderAbnormalMapper;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderMapper;
import com.leliven.park.infrastructure.gateway.persistence.vehicle.converter.VehicleConverter;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import com.leliven.vehicle.model.Vehicle;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.en.payment.PayStatus;
import org.springblade.common.utils.*;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.secure.utils.SecureUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.resource.feign.IOssClient;
import org.springblade.resource.vo.PicVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 车场订单服务实现类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Slf4j
@Service
public class ParkingOrderServiceImpl
    extends BaseServiceImpl<ParkingOrderMapper, ParkingOrder> implements IParkingOrderService {

    @Resource
    @Lazy
    private IParklotService parklotService;
    @Resource
    private IChannelService channelService;
    @Resource
    @Lazy
    private ITempParkingOrderService tempParkingOrderService;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private ParkingOrderAbnormalMapper parkingOrderAbnormalMapper;
    @Resource
    @Lazy
    private ITempParkingChargeRuleService chargeRuleService;
    @Resource
    @Lazy
    private ITempParkingUnpaidOrderService unpaidOrderService;
    @Resource
    @Lazy
    private ICardService iCardService;
    @Resource
    private IReserveParkService reserveParkService;
    @Resource
    private ApplicationEventPublisher publisher;
    @Resource
    private IUserParklotService userParklotService;
    @Resource
    private ParkingOrderAppServiceI parkingOrderAppService;
    @Lazy
    @Resource
    EnterService enterService;
    @Resource
    IBUserPlateService userPlateService;
    @Resource
    IParkingPlaceService parkingPlaceService;
    @Resource
    @Lazy
    IChannelTodoService todoService;
    @Lazy
    @Resource
    private AsyncService asyncService;
    @Resource
    private IParkMerchantParklotService parkMerchantParklotService;
    @Lazy
    @Resource
    private IParkMerchantParklotPlateService merchantParklotPlateService;
    @Lazy
    @Resource
    private ParkAccessClient parkAccessClient;
    @Lazy
    @Resource
    private VehicleDomainService vehicleDomainService;
    @Lazy
    @Resource
    private VehicleAssembler vehicleAssembler;
    @Lazy
    @Resource
    private IReceiptPrintLogService receiptPrintLogService;

    @Resource
    private IParkingOrderAbnormalService parkingOrderAbnormalService;
    @Value("${lecent.park.enterSeconds}")
    private Long enterSeconds;
    @Value(value = "${lecent.park.match-plate-query-limit:6000}")
    private int matchPlateQueryLimit;
    /**
     * 岗亭端无进场记录时自动匹配最大车牌数
     */
    @Value("${lecent.park.guard.match-max-num:20}")
    private int guardMatchMaxNum;
    /**
     * 岗亭端无进场记录时自动匹配相差位数
     */
    @Value("${lecent.park.guard.match-mis-num:2}")
    private int guardMatchMisNum;
    @Resource
    private ICardOrderService orderService;
    @Resource
    private IMsgSender msgSender;
    @Resource
    private IBlackListService blackListService;
    @Resource
    private ExitNestParkLotService exitNestParkLotService;
    @Resource
    private IParkingImageService parkingImageService;
    @Lazy
    @Resource
    private RoadSideParkingBiz roadSideParkingBiz;
    @Resource
    private RoadsideParkingAppServiceI roadsideParkingAppService;
    @Resource
    private IBaseAppConfigService iBaseAppConfigService;
    @Resource
    private IPlatePropertyService platePropertyService;
    @Resource
    private IOssClient iOssClient;
    @Resource
    private IBVehiclePhoneRelationService vehiclePhoneRelationService;
    @Resource
    private ParkingImageRepositoryI parkingImageRepositoryI;

    @Value("${lecent.park.scan-code.domain}")
    private String scanCodeDomain;

    @Value("${lecent.park.scan-code.jump-mini.order-qr-code-path}")
    private String orderQrCodePathTemplate;
    @Resource
    private ParkingOrderConverter parkingOrderConverter;
    @Lazy
    @Resource
    private ParkingPaidCompensateExitHandler parkingPaidCompensateExitHandler;

    @Resource
    private ParkingSuccessfulPassagePublisherI parkingSuccessfulPassagePublisherI;

	@Override
	public ParkingOrder selectParkingOrderById(Long id){
		return baseMapper.selectParkingOrderById(id);
	}
    @Override
    public IPage<ParkingOrderVO> userCustomPage(ParkingOrderDTO parkingOrder, Query query) {
        IPage<ParkingOrderVO> page = Condition.getPage(query);
        List<ParkingOrderVO> list = this.selectParkingOrderPage(page, query, parkingOrder);
        return page.setRecords(list);
    }

    @Slave
    @Override
    public IPage<ParkingOrderVO> customPage(ParkingOrderDTO parkingOrder, Query query) {
        IPage<ParkingOrderVO> page = Condition.getPage(query);
        List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds(parkingOrder.getParklotId());
        if (Func.isEmpty(parkLotIds)) {
            return page.setRecords(Collections.emptyList());
        }

        //当传入carId时不用默认时间和车场
        if (Func.isEmpty(parkingOrder.getCardId())) {
            // 必须默认选择一个车场，无选中车场则选中第一个
            if (null == parkingOrder.getParklotId()) {
                parkLotIds = Collections.singletonList(parkLotIds.get(0));
            }

            // 必须有一个开始时间，如果为空则为最近一周
            if (null == parkingOrder.getEnterStartTime() && null == parkingOrder.getExitStartTime()) {

                // 查询在场车辆时  取消时间限制
                if (null == parkingOrder.getParkingStatus()
                    || ParkingStatusEnum.PARK_IN.getValue() != parkingOrder.getParkingStatus()) {
                    parkingOrder.setEnterStartTime(DateUtil.minusDays(new Date(), CommonConstant.WEEK_DAY));
                }
            }
            parkingOrder.setParkLotIds(parkLotIds);
        }
        return page.setRecords(selectParkingOrderPage(page, query, parkingOrder));
    }

    @Override
    public IPage<ParkingOrderVO> newCustomPage(IPage<ParkingOrderVO> page, ParkingOrderDTO parking) {
        List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIdsBylist(parking.mergeParkLotId());
        if (Func.isEmpty(parkLotIds)) {
            return page.setRecords(Collections.emptyList());
        }

        parking.setParkLotIds(parkLotIds);
        parking.setParkingStatuss(parking.mergeparkingStatus());
        parking.setPlaceIdList(parking.mergePlaceIdList());
        List<ParkingOrderVO> voList = null;
        if (ActiveProfilesUtil.isTest()) {
            voList = baseMapper.newCustomPage(page, parking);
        } else {
            voList = baseMapper.newCustomBYDorisPage(page, parking);
        }
        addName(voList);
        return page.setRecords(voList);
    }

    @Override
    public Integer pageTotal(ParkingOrderDTO parking) {
        List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIdsBylist(parking.mergeParkLotId());
        if (Func.isEmpty(parkLotIds)) {
            return 0;
        }
        parking.setParkLotIds(parkLotIds);
        parking.setParkingStatuss(parking.mergeparkingStatus());
        parking.setPlaceIdList(parking.mergePlaceIdList());
        Integer count = null;
        if (ActiveProfilesUtil.isTest()) {
            count = baseMapper.pageTotal(parking);
        } else {
            count = baseMapper.pageTotalByDoris(parking);
        }
        return count;
    }

    /**
     * 根据订单id 查询订单状态 车牌 支付号
     *
     * @param parkingOrderId 订单id
     * @return PayCodeByOrderVO
     */
    @Deprecated
    @Override
    public PayCodeByOrderVO getOrderStatusAndPayCodeByOrderId(Long parkingOrderId) {
        //订单
        ParkingOrder parkingOrder = this.getById(parkingOrderId);
        LecentAssert.notNull(parkingOrder, "订单为空");
        LecentAssert.notNull(parkingOrder.getPlaceId(), "车位id为空");
        ParkingPlace place = parkingPlaceService.getOne(new LambdaQueryWrapper<ParkingPlace>().eq(ParkingPlace::getId, parkingOrder.getPlaceId()));
        LecentAssert.notNull(place, "车位信息为空");
        PayCodeByOrderVO payCodeByOrderVO = new PayCodeByOrderVO();
        //ParkingStatus  2 在场  4出场
        payCodeByOrderVO.setParkingStatus(parkingOrder.getParkingStatus());
        payCodeByOrderVO.setPlate(parkingOrder.getPlate());
        payCodeByOrderVO.setPayCode(place.getPayCode());
        return payCodeByOrderVO;

    }

    @Override
    public PayCodeByOrderVO getOrderStatus(Long parkingOrderId, String payCode) {
        if (parkingOrderId == null && Func.isBlank(payCode)) {
            throw new ServiceException("订单id和支付码不能同时为空");
        }

        PayCodeByOrderVO orderStatus = baseMapper.getOrderStatus(parkingOrderId, payCode);

        if (orderStatus == null || (Func.isNotBlank(payCode) && ParkingStatusEnum.isExited(orderStatus.getParkingStatus()))) {
            orderStatus = new PayCodeByOrderVO();
            orderStatus.setStatus(0);
            orderStatus.setPayCode(payCode);
            orderStatus.setParkingStatus(ParkingStatusEnum.PARK_OUT.getValue());
            return orderStatus;
        }

        if (BigDecimal.ZERO.compareTo(orderStatus.getReceiveAmount()) >= 0) {
            orderStatus.setStatus(2);
        } else {
            orderStatus.setStatus(1);
        }

        return orderStatus;
    }

    @Override
    public String getUnlimitedQRCodeByParkingOrder(ParkingOrder parkingOrder) {
        ObjectValidator.requireNonNull(parkingOrder, "订单为空");
        return getUnlimitedQRCodeByParkingOrderId(parkingOrder.getId());
    }

    /**
     * @param parkingOrderId parkingOrderId
     * @return 二维码base64 字符串
     */
    @Override
    public String getUnlimitedQRCodeByParkingOrderId(Long parkingOrderId) {
        LecentAssert.notNull(parkingOrderId, "订单id不能为空");
        //id  parkingOrderId 订单id
        String cacheName = CacheNames.CACHE_NAME + "orderPayQRCodeBase64:" + parkingOrderId;
        String sceneKey = "pOrderId:" + parkingOrderId;
        //查询二维码是否在缓存
        return CacheUtils.get(
            cacheName,
            String.class,
            () -> getImageBuffString(parkingOrderId, sceneKey)
            , Duration.ofHours(24));
    }

    @Override
    public String getPayCommonQrCodeByParkingOrder(ParkingOrder parkingOrder) {
        QrConfig qrConfig = new QrConfig();
        qrConfig.setWidth(430);
        qrConfig.setHeight(430);
        qrConfig.setMargin(1);
        String orderQrCodePath = this.orderQrCodePathTemplate.replace("{tenantId}", parkingOrder.getTenantId());
        String jumpPath = scanCodeDomain + orderQrCodePath + "?pOrderId=" + parkingOrder.getId();
        return QrCodeUtil.generateAsBase64(jumpPath, qrConfig, ImgUtil.IMAGE_TYPE_PNG);
    }

    @Override
    public PayQrCode getPayQrCodeByParkingOrderId(Long parkingOrderId) {
        ParkingOrder parkingOrder = getById(parkingOrderId);
        Parklot parklot = this.parklotService.getById(parkingOrder.getParklotId());
        PayQrCodeType codeType = PayQrCodeType.getByValue(parklot.getPayCodeType());
        // 缓存 key 存储二维码类型，避免切换类型后走缓存
        String cacheName = CacheNames.CACHE_NAME + "getPayQrCode:" + codeType.name() + "_" + parkingOrderId;
        String url = CacheUtils.get(
            cacheName,
            String.class,
            () -> {
                // 获取二维码 base64
                String qrcodeBase64 = codeType.getHandle().apply(parkingOrder);
                // 上传到 OSS
                R<BladeFile> r = iOssClient.putBase64Pic(new PicVO(qrcodeBase64, "." + ImgUtil.IMAGE_TYPE_PNG));
                if (!r.isSuccess()) {
                    throw new ServiceException("上传支付二维码到OSS失败");
                }
                return r.getData().getLink();
            }, Duration.ofDays(7));
        return new PayQrCode(parklot.getPayCodeType(), url);
    }

    /**
     * @param parkingOrderId 订单id
     * @param sceneKey       微信二维码sceneKey
     * @return 二维码base64 字符串
     */
    private String getImageBuffString(Long parkingOrderId, String sceneKey) {
        ParkingOrder parkingOrder = this.getById(parkingOrderId);
        LecentAssert.notNull(parkingOrder, "请输入正确的订单id");
        //获取 accessToken
        String accessToken = getMiniAccessToken(parkingOrder.getParklotId());
        //获取二维码 imageBuff
        return generateMiniQRCode(accessToken, sceneKey);
    }

    /**
     * 获取AccessToken
     *
     * @param parklotId 车场id
     * @return accessToken
     */
    private String getMiniAccessToken(Long parklotId) {

        Parklot parklot = ParkLotCaches.existParkLot(parklotId);
        BaseAppConfig baseAppConfig = iBaseAppConfigService.getConfigById(parklot.getScanMimiId());
        LecentAssert.notNull(baseAppConfig, "第三方应用配置为空");
        try {
            return WxUtils.getAccessionToken(baseAppConfig.getAppId(), baseAppConfig.getAppSecret());
        } catch (Exception e) {
            throw new ServiceException("获取accessToken失败" + e.getMessage());
        }

    }

    /**
     * @param accessToken token
     * @param sceneKey    sceneKey
     * @return 二维码base64 字符串
     */
    private String generateMiniQRCode(String accessToken, String sceneKey) {
        String payQrcodePath = "scanPay/middleware";
        Map<String, Object> map = new HashMap<>();
        map.put("scene", sceneKey);
        map.put("page", payQrcodePath);
        String code = MiniApi.miniQRCode(accessToken, map);
        //获取二维码失败
        try {
            byte[] imageBuffByte = Base64.getDecoder().decode(code);
            String errmsg = (String) JSON.parseObject(new String(imageBuffByte)).get("errmsg");
            throw new ServiceException("获取二维码失败:" + errmsg);
        } catch (Exception e) {
            return code;
        }
    }

    private void addName(List<ParkingOrderVO> voList) {
        voList.forEach(vo -> {
            //停车时长
            if (Func.isBlank(vo.getDurationTime())) {
                Date startTime = vo.getEnterTime();
                startTime = startTime == null ? new Date() : startTime;
                Date stopTime = vo.getExitTime();
                stopTime = stopTime == null ? new Date() : stopTime;
                vo.setDurationTime(DateUtils.getDuration(startTime, stopTime));
            }

            Parklot parkLot = ParkLotCaches.getParkLot(vo.getParklotId());
            vo.setParklotName(parkLot != null ? parkLot.getName() : "");
            if (parkLot != null && parkLot.isRoadSide()) {
                ParkingPlace place = parkingPlaceService.getById(vo.getPlaceId());
                if (place != null) {
                    vo.setEnterChannelName("车位编号:" + place.getPlaceCode());
                    vo.setExitChannelName("支付码:" + place.getPayCode());
                }
            } else {
                Channel enterChannel = ParkLotCaches.getChannel(vo.getEnterChannelId());
                vo.setEnterChannelName(enterChannel != null ? enterChannel.getName() : "");
                Channel exitChannel = ParkLotCaches.getChannel(vo.getExitChannelId());
                vo.setExitChannelName(exitChannel != null ? exitChannel.getName() : "");
            }
        });
    }


    private List<ParkingOrderVO> selectParkingOrderPage(IPage<ParkingOrderVO> page, Query query, ParkingOrderDTO parkingOrder) {
        if (StringUtil.isNoneBlank(parkingOrder.getParkingStatusStr())) {
            parkingOrder.setParkingStatuss(Func.toLongList(parkingOrder.getParkingStatusStr()));
        }
        int totalRow = baseMapper.countTotal(parkingOrder);
        if (totalRow <= 0) {
            return null;
        }
        int current = Func.toInt(query.getCurrent()) <= 0 ? 1 : Func.toInt(query.getCurrent());
        int size = Func.toInt(query.getSize()) <= 0 ? 10 : Func.toInt(query.getSize());
        int pages = (totalRow / size) + (totalRow % size > 0 ? 1 : 0);
        query.setCurrent(((current - 1) * size));
        page.setTotal(totalRow);
        page.setPages(pages);

        List<ParkingOrderVO> list = baseMapper.selectParkingOrderPage(query, parkingOrder);
        if (Func.isNotEmpty(list)) {
            for (ParkingOrderVO p : list) {
                BlackList blackPlate = blackListService.getByPlate(p.getPlate(), p.getParklotId());
                if (Func.notNull(blackPlate)) {
                    p.setPlate(p.getPlate() + "(黑名单)");
                }
            }
        }
        return list;
    }


    @Override
    public List<ParkingOrderVO> selectRoadsideParkingList(IPage<ParkingOrderVO> page, List<WechatPlate> plates, ParkingOrderDTO parkingOrderDTO) {

        List<String> plateList = new ArrayList<>();
        plates.forEach(p -> plateList.add(p.getPlate()));

        return baseMapper.selectRoadsideParkingList(page, plateList, parkingOrderDTO);
    }

    @Override
    public List<ParkingOrderExcel> getListByFreeCardId(Long freeCardId) {
        return baseMapper.getListByFreeCardId(freeCardId);
    }

    @Override
    public List<ParkingOrderExcel> getExportData(Long cardId) {
        return baseMapper.getExportData(cardId);
    }

    @Override
    public List<ParkingOrderVO> getSelectLocus(Long targetParkingId) {
        List<ParkingOrderVO> parkingOrderVOList = baseMapper.selectLocus(targetParkingId);
        if (Func.isNotEmpty(parkingOrderVOList) && parkingOrderVOList.size() > 1) {
            for (int i = 0; i < parkingOrderVOList.size(); i++) {
                if (i == 0) {
                    continue;
                }
                if (parkingOrderVOList.get(i).getEnterTime().getTime() == parkingOrderVOList.get(i - 1).getEnterTime().getTime()) {
                    if (DateUtils.notNull(parkingOrderVOList.get(i - 1).getExitTime(), parkingOrderVOList.get(i).getExitTime())) {
                        if (parkingOrderVOList.get(i).getExitTime().getTime() > parkingOrderVOList.get(i - 1).getExitTime().getTime()) {
                            parkingOrderVOList.remove(i);
                        } else {
                            parkingOrderVOList.remove(i - 1);
                        }
                    }
                }
            }
        }
        return parkingOrderVOList;
    }

    @Override
    public int countPresentCarByCardId(String tenantId, Long parklotId, Long cardId, String plate, String openId) {
        return baseMapper.countPresentCarByCardId(tenantId, parklotId, cardId, plate, openId);
    }

    @Override
    public ParkingOrder selectPresentParking(String tenantId, Long parklotId, String plate, String openId) {
        List<ParkingOrder> parkingOrderList = this.selectPresentParkingList(tenantId, parklotId, plate, openId);
        if (Func.isNotEmpty(parkingOrderList)) {
            return parkingOrderList.get(0);
        }
        return null;
    }

    @Override
    public ParkingOrder selectPresentParking(Long parklotId, String plate) {
        return this.selectPresentParking(StringPool.EMPTY, parklotId, plate, StringPool.EMPTY);
    }

    @Override
    public ParkingOrder selectPresentParking(Long parklotId, String plate, String openId) {
        return selectPresentParking(StringPool.EMPTY, parklotId, plate, openId);
    }

    @Override
    public List<ParkingOrder> selectPresentParkingList(String tenantId, Long parklotId, String plate, String openId) {
        List<ParkingOrder> parkingOrderList = null;
        if (!TodoContext.isNoPlate(plate)) {
            parkingOrderList = baseMapper.selectPresentParkingByPlate(tenantId, parklotId, plate);
        } else if (StringUtils.isNotBlank(openId)) {
            parkingOrderList = baseMapper.selectPresentParkingByOpenId(tenantId, parklotId, openId);
        }
        return parkingOrderList;
    }

    @Override
    public List<ParkingOrder> selectListByPresentTargetParkingId(String tenantId, List<Long> parkLotIds,
                                                                 Long targetParkingId) {

        List<ParkingOrder> parkingOrderList = baseMapper.selectListByPresentTargetParkingId(tenantId, parkLotIds,
            targetParkingId);

        if (Func.isNotEmpty(parkingOrderList)) {
            List<ParkingOrder> newList = new ArrayList<>();
            No:
            for (ParkingOrder p : parkingOrderList) {
                for (ParkingOrder np : newList) {
                    if (p.getParklotId().equals(np.getParklotId()) && p.getEnterChannelId().equals(np.getEnterChannelId())) {
                        continue No;
                    }
                }
                newList.add(p);
            }

            return newList.stream().sorted(Comparator.comparing(ParkingOrder::getBillingStartTime)).collect(Collectors.toList());
        }

        return parkingOrderList;
    }

    @Override
    public int countParkingSpaceOccupied(Long parkLotId, Long merchantParkLotId, Date startDate, Date endDate) {
        return baseMapper.countParkingSpaceOccupied(parkLotId, merchantParkLotId, startDate, endDate);
    }

    @Override
    public void updatePresentParkingByPlate(String tenantId, Long parklotId, String plate, String openId, Long newParkingOrderId, Date enterTime) {

        List<ParkingOrder> parkingOrderList;
        if (TodoContext.isNoPlate(plate) && StringUtils.isNotBlank(openId)) {
            parkingOrderList = baseMapper.selectPresentParkingByOpenId(tenantId, parklotId, openId);
        } else if (Func.isNotBlank(plate)) {
            parkingOrderList = baseMapper.selectPresentParkingByPlate(tenantId, parklotId, plate);
        } else {
            parkingOrderList = new ArrayList<>();
        }
        parkingOrderList.forEach(p -> {
            if (!p.getId().equals(newParkingOrderId)) {
                p.setParkingStatus(ParkingStatusEnum.PARK_REPEAT.getValue());
                p.setTargetParkingId(newParkingOrderId);
                p.setExitTime(enterTime);
                p.setExitWay(ChannelWay.WAY_10.getValue());
                this.updateById(p);
                parkingSuccessfulPassagePublisherI.publishExitMessage(parkingOrderConverter.fromDO(p));
                // 进场覆盖后释放车位
                parkingPlaceService.updateCurStatus(p.getPlaceId(), ParkingPlaceIdleState.IDLE, "");
            }

        });
    }

    @Override
    public ParkingOrder selectOnebyCardIdsDesc(String tenantId, Long parklotId, List<Long> cardIds) {
        return baseMapper.selectOnebyCardIdsDesc(tenantId, parklotId, cardIds);
    }

    @Override
    public String selectOtherPresentCar(String tenantId, Long parklotId, Long cardId, String plate) {
        return baseMapper.selectOtherPresentCar(tenantId, parklotId, cardId, plate);
    }


    @Override
    public void carPassing(ChannelTodo todo, Parklot parklot) {
        ParkingOrder parking = this.getParkingById(todo.getParkingId());
        if (parking == null) {
            return;
        }
        if (!todoService.isPass(todo.getStatus())) {
            return;
        }

        ParkMerchantParklotPlateVO merchantCard = merchantParklotPlateService.selectByPlateOrOpenId(todo.getPlate(), parking.getOpenId(), todo.getParklotId(), parking.getEnterTime());
        this.addMerchantUseTimeLength(merchantCard, parking, todo);

        this.handleParkingRecord(todo, parking, parklot);
    }


    @Override
    public void successPass(Parklot parklot, ChannelTodo todo, ParkingOrder parking, ParkMerchantParklotPlateVO merchantCard) {
        if (parking == null && null != todo.getParkingId()) {
            parking = getById(todo.getParkingId());
        }

        if (!todoService.isPass(todo.getStatus())) {
            return;
        }

        this.addMerchantUseTimeLength(merchantCard, parking, todo);

        this.handleParkingRecord(todo, parking, parklot);
    }


    private void addMerchantUseTimeLength(ParkMerchantParklotPlateVO merchantCard, ParkingOrder parking, ChannelTodo todo) {
        if (parking == null) {
            return;
        }
        if (merchantCard != null) {
            //将停车时长累计到缓存中
            Date startDate = parking.getEnterTime().before(merchantCard.getAuthStartTime()) ? merchantCard.getAuthStartTime() : parking.getEnterTime();
            Date endDate = todo.getDate().after(merchantCard.getAuthEndTime()) ? merchantCard.getAuthEndTime() : todo.getDate();
            long parkingTimeLength = DateUtil.between(startDate, endDate).getSeconds();
            ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(merchantCard.getMerchantParklotId());
            this.merchantUseTimeLength(merchantParkLot, parkingTimeLength);
        }
    }

    @Override
    public ParkingOrder existById(Long parkingId) {
        ParkingOrder parkingOrder = getById(parkingId);
        LecentAssert.notNull(parkingOrder, ParkingConstant.TIPS_NO_PARKING_ORDER);
        return parkingOrder;
    }

    /**
     * 停车相关信息更新
     *
     * @param channelTodo 代办信息
     * @param parking     停车订单
     * @param parklot     车场
     * @return ParkingOrder
     */
    private ParkingOrder handleParkingRecord(ChannelTodo channelTodo, ParkingOrder parking, Parklot parklot) {
        if (!todoService.isPass(channelTodo.getStatus())) {
            return parking;
        }
        if (parking != null && parking.getParkingStatus() == 4) {
            boolean needUpdateImageUrl = Func.isNotBlank(channelTodo.getImageUrl());
            boolean needUpdatePlate = !PlateValidator.isPlate(parking.getPlate()) &&
                PlateValidator.isPlate(channelTodo.getPlate());
            if (needUpdateImageUrl) {
                parking.setExitImageUrl(channelTodo.getImageUrl());
            }
            if (needUpdatePlate) {
                // 停车订单车牌为非正常车牌，且代办记录中车牌为正常车牌时，则替换停车订单车牌为正常车牌
                parking.setPlate(channelTodo.getPlate());
            }
            if (needUpdateImageUrl || needUpdatePlate) {
                updateById(parking);
            }
            return parking;
        }

        channelTodo.setIsSecondExit(true);
        exitNestParkLotService.entryOutParkLot(channelTodo);

        if (parking == null) {
            return null;
        }
        //出场更新信息
        updateParkingInfoWithExitedEvent(parking, channelTodo, parklot);
        createTempStopOrder(channelTodo);
        updateMerchantUsedAmount(parking);


        String msgCode = ParkMsgConstant.PAY_EXIT;
        msgSender.send(MsgRequest.builder()
            .code(msgCode)
            .body(parking)
            .build());
        asyncService.pushParkingWuDang(parking);
        if (!channelTodo.isChildParkLotPass()) {
            //更新循环车产生的未缴订单
            unpaidOrderService.updateLoopCarUnPaidOrder(parking.getId());
        }

        //若有异常金额则创建异常订单
        createUnpaidOrder(channelTodo, parking);

        /**
         * 车位处理
         */
        handleParkingPlace(channelTodo, parklot);
        return parking;
    }

    @Override
    public void handleParkingPlace(ChannelTodo channelTodo, Parklot parklot) {
        try {
            //循环车-前车缴费（多车多位循环车收费）
            if (channelTodo.getMultipleChargeType() != null && channelTodo.getMultipleChargeType() == MultipleChargeType.FRONT_CAR_CHARGE_TYPE) {
                this.frontCarCost(channelTodo, parklot);

                //循环车-后车缴费（多车多位循环车收费）
            } else if (channelTodo.getMultipleChargeType() != null && channelTodo.getMultipleChargeType() == MultipleChargeType.AFTER_CAR_CHARGE_TYPE) {

                this.afterCarCost(channelTodo, parklot);
            }
            //临停车出场归还车位
            if (!parklot.isRoadSide()) {
                if (channelTodo.getCardId() == 0 || !Boolean.TRUE.equals(channelTodo.getOccupied())) {
                    if (Boolean.TRUE.equals(parklot.getRealTimeCalculate())) {
                        asyncService.pushParkRemainNumber(parklot);
                    } else {
                        this.releasePlace(parklot);
                    }
                }
            }


        } catch (Exception e) {
            log.error("", e);
        }

    }

    /**
     * 更新商户使用金额
     *
     * @param parking 停车订单
     */
    private void updateMerchantUsedAmount(ParkingOrder parking) {
        if (parking.getMerchantAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        ParkMerchantParklot merchantParkLot = parkMerchantParklotService.getById(parking.getCardId());
        if (merchantParkLot == null) {
            return;
        }
        BigDecimal usedAmount = merchantParkLot.getUsedAmount().add(parking.getMerchantAmount());
        merchantParkLot.setUsedAmount(usedAmount);
        parkMerchantParklotService.updateById(merchantParkLot);
    }


    /**
     * 循环后车收费
     *
     * @param channelTodo 代办信息
     * @param parklot     车场信息
     */
    @Override
    public void afterCarCost(ChannelTodo channelTodo, Parklot parklot) {

        // 月卡过期直接返回
        String cardInfoStr = channelTodo.getCardInfo();
        if (Func.isNotBlank(cardInfoStr)) {
            CardInfo cardInfo = JSON.parseObject(cardInfoStr, CardInfo.class);
            if (cardInfo != null && cardInfo.getEndDate() != null &&
                channelTodo.getDate().compareTo(cardInfo.getEndDate()) >= 0) {
                return;
            }
        }
        //有车位的车
        if (Boolean.TRUE.equals(channelTodo.getOccupied())) {
            //查询后车
            ParkingOrder afterCar = this.selectAfterCarByCardId(channelTodo.getTenantId(), channelTodo.getParklotId(),
                channelTodo.getCardId());
            if (afterCar == null) {
                return;
            }
            if (afterCar.getPlate().equals(channelTodo.getPlate())) {
                return;
            }

            log.info("生成后车（{}）未缴订单，后车出场时缴费", afterCar.getPlate());

            ProjectCost cost = new ProjectCost();
            TempParkingChargeRuleVO chargeRule = this.getChargeRule(afterCar);
            if (chargeRule != null) {
                ProjectCalculate.calculate(cost, "循环车(后车缴费)临停计费时段", chargeRule,
                    afterCar.getBillingStartTime(), channelTodo.getDate());
                TempParkingUnpaidOrder unpaidOrder = TempParkingUnpaidOrder.createUnpaidOrder(afterCar);
                unpaidOrder.setType(UnpaidOrderType.TYPE03.getValue());
                unpaidOrder.setMemo("循环车-后车缴费,前车【" + channelTodo.getPlate() + "】出场生成");
                unpaidOrder.setBillingStartTime(afterCar.getBillingStartTime());
                unpaidOrder.setBillingEndTime(channelTodo.getDate());
                unpaidOrderService.saveUnpaidOrder(unpaidOrder, cost);
            }

            //将位置释放给最早入场且没有车位的车
            updateCycleCar(afterCar, channelTodo);
        }
    }


    private void updateCycleCar(ParkingOrder parkingOrder, ChannelTodo todo) {
        parkingOrder.setOccupied(true);
        parkingOrder.setCardId(todo.getCardId());
        List<TimeNode> timeNodeList = getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
        timeNodeList.add(new TimeNode(DateUtils.format(todo.getDate()), "获得车位，停止计费", null, false));
        parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));
        parkingOrder.setBillingStartTime(todo.getDate());

        String plateOrOpenId = ComUtil.getPlateOrOpenId(parkingOrder.getPlate(), parkingOrder.getOpenId());
        ParkLotCaches.delParkingOrderByPlateOrOpenId(parkingOrder.getParklotId(), plateOrOpenId);
        this.updateById(parkingOrder);
    }


    /**
     * 循环前车收费
     *
     * @param channelTodo 代办信息
     * @param parklot     车场
     */
    @Override
    public void frontCarCost(ChannelTodo channelTodo, Parklot parklot) {
        //有车位的车
        if (Boolean.TRUE.equals(channelTodo.getOccupied())) {
            //查询前车
            ParkingOrder frontCar = baseMapper.selectFrontCarByCardId(channelTodo.getTenantId(),
                channelTodo.getParklotId(), channelTodo.getCardId());
            if (frontCar == null) {
                return;
            }
            if (frontCar.getPlate().equals(channelTodo.getPlate())) {
                return;
            }

            log.info("生成前车（{}）未缴订单，前车出场时缴费", frontCar.getPlate());
            ProjectCost cost = new ProjectCost();
            TempParkingChargeRuleVO chargeRule = this.getChargeRule(frontCar);
            if (chargeRule != null) {
                cost.setIsFirstParking(ParkLotCaches.isFirstParking(chargeRule.getFirstParkingConfigId(), frontCar.getPlate(), frontCar.getBillingStartTime()));
                ProjectCalculate.calculate(cost, "循环车(前车缴费)临停计费时段", chargeRule,
                    frontCar.getBillingStartTime(), channelTodo.getDate());
                TempParkingUnpaidOrder unpaidOrder = TempParkingUnpaidOrder.createUnpaidOrder(frontCar);
                unpaidOrder.setType(UnpaidOrderType.TYPE06.getValue());
                unpaidOrder.setMemo("循环车-前车缴费,后车【" + channelTodo.getPlate() + "】出场生成");
                unpaidOrder.setBillingStartTime(frontCar.getBillingStartTime());
                unpaidOrder.setBillingEndTime(channelTodo.getDate());
                unpaidOrderService.saveUnpaidOrder(unpaidOrder, cost);
            }

            //将位置释放给最早入场且没有车位的车
            updateCycleCar(frontCar, channelTodo);
        }
    }

    private TempParkingChargeRuleVO getChargeRule(ParkingOrder frontCar) {
        if (Func.isNull(frontCar)) {
            return null;
        }
        return chargeRuleService.selectStandardOneByParkLotId(frontCar.getParklotId());
    }


    public void createTempStopOrder(ChannelTodo todo) {
        if (todo.getTotalAmount().compareTo(BigDecimal.ZERO) > 0
            && todo.getDiscountAmount().compareTo(todo.getTotalAmount()) == 0) {
            TempParkingOrder order = TempParkingOrder.create(todo);
            if (Func.isNotBlank(todo.getData())) {
                ProjectCost projectCost = Func.readJson(todo.getData(), ProjectCost.class);
                if (!projectCost.isLatestVersion()) {
                    tempParkingOrderTotalAmount(order);
                }
            }
            order.setTradeNo(SequenceNoUtils.generateNo());
            order.setPayType(PayWay.CASH.getKey());
            order.setPayTime(new Date());
            order.setPayStatus(PayStatus.SUCCESS.getKey());
            tempParkingOrderService.save(order);
            todo.setTempParkingOrderId(order.getId());
        }
    }

    /**
     * 订单总金额处理
     *
     * @param order
     */
    private void tempParkingOrderTotalAmount(TempParkingOrder order) {
        BigDecimal discountAmount = order.getDiscountAmount();
        if (discountAmount == null) {
            discountAmount = BigDecimal.ZERO;
        }
        BigDecimal totalAmount = order.getTotalAmount();
        BigDecimal receiveAmount = order.getReceiveAmount();
        if (totalAmount.compareTo(receiveAmount) == 0
            && totalAmount.compareTo(discountAmount.add(receiveAmount)) != 0) {
            totalAmount = discountAmount.add(receiveAmount);
            order.setTotalAmount(totalAmount);
        }
    }

    /**
     * 释放车位
     *
     * @param parklot 车场
     */
    @Override
    public void releasePlace(Parklot parklot) {
        ReturnParkPlaceEvent event = new ReturnParkPlaceEvent();
        event.setParkLotId(parklot.getId());
        event.setFuzzyDisplay(parklot.getFuzzyDisplay());
        event.setParkPlaceNumber(1);
        publisher.publishEvent(event);
    }

    /**
     * 创建异常订单
     *
     * @param channelTodo 代办信息
     * @param parking     停车订单
     */
    @Override
    public void createUnpaidOrder(ChannelTodo channelTodo, ParkingOrder parking) {
        log.info("createUnpaidOrder todo={}", Func.toJson(channelTodo));
        if (channelTodo.getErrorAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        List<TempParkingUnpaidOrder> tempParkingUnpaidOrders = unpaidOrderService.listByParkingId(parking.getId());
        if (ObjectUtil.isNotEmpty(tempParkingUnpaidOrders)) {
            return;
        }

        List<TempParkingOrder> orders = tempParkingOrderService.selectPaidListByParkingId(parking.getId());
        if (Func.isNotEmpty(orders)) {
            // 上次缴费时间
            Date lastPayTime = orders.get(0).getPayTime();
            // 缴费金额
            BigDecimal paidAmount = Optional.ofNullable(orders.get(0).getPaidAmount()).orElse(BigDecimal.ZERO);
            // 校验出场时是否在提前缴费有效期时间内
            if (lastPayTime != null && paidAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 免费离场时间
                int payLeaveTime = Optional.ofNullable(ParkLotCaches.getParkLot(parking.getParklotId()))
                    .map(Parklot::getTempParkingChargeRuleId)
                    .map(Long::valueOf)
                    .map(ParkLotCaches::getTempParkingChargeRule)
                    .map(TempParkingChargeRuleVO::getPayLeaveTime)
                    .orElse(-1);

                long diffMinute = DateUtil.between(lastPayTime, channelTodo.getDate()).toMinutes();
                // 提前付款时间在免费时长内
                if (payLeaveTime >= diffMinute) {
                    return;
                }
            }
        }

        if (channelTodo.getErrorAmount().compareTo(BigDecimal.ZERO) != 0 && channelTodo.getErrorAmountType() == 2) {
            TempParkingUnpaidOrder unpaidOrder = TempParkingUnpaidOrder.create(channelTodo);
            unpaidOrder.setBillingStartTime(parking.getBillingStartTime());
            unpaidOrder.setChargeUserId(AuthUtil.getUserId());

            Parklot parklot = ParkLotCaches.getParkLot(parking.getParklotId());
            if (parklot != null && parklot.isRoadSide()) {
                unpaidOrder.setType(UnpaidOrderType.TYPE100.getValue());
            }

            String data = channelTodo.getData();
            if (ObjectUtil.isNotEmpty(data)) {
                ProjectCost cost = JSON.parseObject(data, ProjectCost.class);
                List<CostDetail> costDetailList = cost.getCostDetailList();
                if (ObjectUtil.isNotEmpty(costDetailList) && !costDetailList.isEmpty()) {
                    Date pStartDate = costDetailList.get(0).getPStartDate();
                    Date pEndDate = costDetailList.get(costDetailList.size() - 1).getPEndDate();
                    unpaidOrder.setBillingStartTime(pStartDate);
                    unpaidOrder.setBillingEndTime(pEndDate);
                }
            }
            unpaidOrderService.save(unpaidOrder);
        }

    }

    @Override
    public IPage<ParkingOrderVO> insidePage(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO) {
        String plate = parkingOrderDTO.getPlate();

        //如果车牌为空或者车牌是无牌车或者车牌格式不正确，走默认逻辑
        if (!PlateCheckUtils.isPlate(plate)) {
            return page.setRecords(baseMapper.insidePage(page, parkingOrderDTO));
        }

        parkingOrderDTO.setPlate(null);

        //查询在场车牌
        Set<String> plateSet = this.presentParking(parkingOrderDTO.getParklotId());

        List<String> matchedPlates = PlateMatchUtils.match(plate, plateSet, guardMatchMaxNum, guardMatchMisNum);

        //没有匹配到车牌，返回所有停车记录
        if (CollectionUtil.isEmpty(matchedPlates)) {
            return page.setRecords(baseMapper.insidePage(page, parkingOrderDTO));
        }

        //返回匹配到的车牌
        parkingOrderDTO.setPlates(matchedPlates);
        return page.setRecords(baseMapper.insidePage(page, parkingOrderDTO));
    }


    @Override
    public void updateParkingInfoWithExitedEvent(ParkingOrder parking, ChannelTodo todo) {
        Parklot parklot = ParkLotCaches.getParkLot(parking.getParklotId());
        updateParkingInfoWithExitedEvent(parking, todo, parklot);
    }

    /**
     * 更新停车记录
     *
     * @param parking     停车记录
     * @param channelTodo 代办信息
     */
    @Override
    public void updateParkingInfoWithExitedEvent(ParkingOrder parking, ChannelTodo channelTodo, Parklot parklot) {
        updateParkingInfoWithEventType(parking, channelTodo, parklot, ParkingOrderEventType.EXITED);
    }


    /**
     * 更新停车记录
     *
     * @param parking       停车记录
     * @param channelTodo   代办信息
     * @param eventTypeName 事件类型名称
     */
    @Override
    public void updateParkingInfoWithEventType(ParkingOrder parking, ChannelTodo channelTodo, String eventTypeName) {
        Parklot parklot = ParkLotCaches.getParkLot(parking.getParklotId());
        updateParkingInfoWithEventType(parking, channelTodo, parklot, ParkingOrderEventType.resolve(eventTypeName));
    }

    /**
     * 更新停车记录
     *
     * @param parking     停车记录
     * @param channelTodo 代办信息
     * @param parklot     车场
     * @param eventType   事件类型
     */
    public void updateParkingInfoWithEventType(ParkingOrder parking,
                                               ChannelTodo channelTodo,
                                               Parklot parklot,
                                               ParkingOrderEventType eventType) {
        parking.setTotalAmount(BigDecimal.ZERO);
        parking.setPaidAmount(BigDecimal.ZERO);
        log.info("updateParkingInfo todo={}", Func.toJson(channelTodo));

        ProjectCost projectCost = tempParkingOrderService.selectPaidByParkingId(parking.getId());
        if (ObjectUtil.isNotEmpty(projectCost)) {
            if (parklot != null && parklot.isRoadSide() && channelTodo.getTotalAmount() != null
                && channelTodo.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                //路内停车
                parking.setTotalAmount(channelTodo.getTotalAmount());
                parking.setPaidAmount(channelTodo.getPaidAmount());
                parking.setUnusualAmount(channelTodo.getErrorAmount());
                parking.setDiscountAmount(channelTodo.getDiscountAmount());
                parking.setMerchantAmount(channelTodo.getMerchantAmount());
                parking.setCouponDiscountAmount(channelTodo.getCouponDiscountAmount());
            } else {
                parking.setTotalAmount(projectCost.getTotalAmount());
                parking.setPaidAmount(projectCost.getPaidAmount());
                parking.setDiscountAmount(projectCost.getDiscountAmount());
                parking.setCouponDiscountAmount(projectCost.getCouponDiscountAmount());
                parking.setMerchantAmount(projectCost.getMerchantAmount());
                parking.setUnusualAmount(projectCost.getUnusualAmount());
            }
        } else {
            parking.setTotalAmount(channelTodo.getTotalAmount());
            parking.setPaidAmount(channelTodo.getPaidAmount());
            parking.setUnusualAmount(channelTodo.getErrorAmount());
            parking.setDiscountAmount(channelTodo.getDiscountAmount());
            parking.setMerchantAmount(channelTodo.getMerchantAmount());
            parking.setCouponDiscountAmount(channelTodo.getCouponDiscountAmount());
        }

        if (parklot != null && parklot.isRoadSide()) {
            // 停车订单车牌为非正常车牌，且代办记录中车牌为正常车牌时，则替换停车订单车牌为正常车牌
            if (!PlateValidator.isPlate(parking.getPlate()) && PlateValidator.isPlate(channelTodo.getPlate())) {
                parking.setPlate(channelTodo.getPlate());
            }
        }
        parking.setRelationId(channelTodo.getRelationId());
        parking.setRelationType(channelTodo.getRelationType());
        parking.setCardId(channelTodo.getCardId());
        parking.setUnusualAmountType(channelTodo.getErrorAmountType());
        parking.setExitChannelId(channelTodo.getChannelId());
        parking.setExitWay(channelTodo.getTriggerType());
        parking.setExitTime(channelTodo.getDate());
        parking.setExitImageUrl(channelTodo.getImageUrl());
        parking.setExitTodoId(channelTodo.getId());
        parking.setChargeRuleId(channelTodo.getChargeRuleId());
        parking.setPayTypes(channelTodo.getChargeType());
        parking.setDurationTime(DateUtils.getDuration(parking.getEnterTime(), channelTodo.getDate()));
        parking.setReasonIds(String.valueOf(channelTodo.getReasonId()));
        parking.setTimeNodeDetail(channelTodo.getTimeNodeDetail());
        parking.setParkingStatus(ParkingStatus.SPRKING_APPEARED);

        List<TimeNode> timeNodeList = this.getParkingTimeNodeList(parking.getParkingTimeNode());

        timeNodeList.add(new TimeNode(DateUtils.format(channelTodo.getDate()), "车辆出场",
            null, false));

        timeNodeList = timeNodeList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
            () -> new TreeSet<>(Comparator.comparing(TimeNode::getStartDate))), ArrayList::new));

        parking.setParkingTimeNode(JSON.toJSONString(timeNodeList));

        this.parkingOrderAppService.finishOrder(parking, eventType);
        //更新订单中的车牌信息
        updateTempParkingOrderByPlate(parking);

        String plateOrOpenId = ComUtil.getPlateOrOpenId(parking.getPlate(), parking.getOpenId());
        ParkLotCaches.delParkingOrderByPlateOrOpenId(parking.getParklotId(), plateOrOpenId);

        // 预约车处理
        if (StringUtils.isNotBlank(parking.getReserveOrderId())) {
            // 异常处理，防止修改状态出错影响正常流程
            try {
                reserveParkService.outPartAfter(parking);
            } catch (Exception e) {
                log.error("----------------------------------------------------------------------------------------");
                log.error("预约车出场后数据处理失败，订单为{}，车牌为{}", parking.getReserveOrderId(), parking.getPlate());
                log.error("----------------------------------------------------------------------------------------");
            }
        }
    }

    /**
     * 停车记录中的车牌和订单中的车牌不一致则修改成为停车记录中的车牌
     *
     * @param parking 停车记录
     */
    private void updateTempParkingOrderByPlate(ParkingOrder parking) {
        List<TempParkingOrder> tempParkingOrders = tempParkingOrderService.getByParkingId(parking.getId());
        if (Func.isNotEmpty(tempParkingOrders)) {
            List<TempParkingOrder> updates = new ArrayList<>();
            for (TempParkingOrder tempParkingOrder : tempParkingOrders) {
                if (Func.isNotBlank(parking.getPlate()) && !parking.getPlate().equals(tempParkingOrder.getPlate())) {
                    tempParkingOrder.setPlate(parking.getPlate());
                    updates.add(tempParkingOrder);
                }
            }
            if (!updates.isEmpty()) {
                tempParkingOrderService.updateBatchById(updates);
            }
        }
    }

    @Override
    public MiniScanParkingOrderDetailVO getParkingOrderByTodoID(Long todoId) {
        if (null == todoId) {
            throw new ServiceException("代办ID参数不能为空");
        }
        ChannelTodo todo = todoService.getById(todoId);
        if (null == todo) {
            throw new ServiceException("代办信息不存在");
        }
        ParkingOrder parkingOrder = this.getById(todo.getParkingId());
        if (null == parkingOrder) {
            throw new ServiceException("停车记录不存在");
        }
        MiniScanParkingOrderDetailVO result = new MiniScanParkingOrderDetailVO();
        result.setTodoId(todoId);
        result.setParkingId(parkingOrder.getId());
        result.setEnterTime(parkingOrder.getEnterTime());
        result.setExitTime(parkingOrder.getExitTime());
        result.setParkingTimeDesc(DateUtils.getDuration(parkingOrder.getEnterTime(), parkingOrder.getExitTime()));
        result.setPlate(parkingOrder.getPlate());
        result.setParklotName(parklotService.getNameById(parkingOrder.getParklotId()));
        result.setTimeNodes(this.getParkingTimeNodeList(parkingOrder.getParkingTimeNode()));
        return result;
    }

    @Override
    public void insertParkingInfo(ChannelTodo todo) {
        log.info("{}进场开始创建停车记录信息=================================", todo.getPlate());

        ParkingOrder parkingOrder = createParking(todo);
        if (todo.getMultipleChargeType() == MultipleChargeType.FRONT_CAR_CHARGE_TYPE) {
            //循环车前车收费
            this.chargeBeforeCycle(todo, parkingOrder);
        }

        // 如果是车位代办，则查询当前车位是否有在场订单
        List<ParkingOrder> overOrdersOfPlace = Optional.ofNullable(todo.getPlaceId())
            .map(t ->
                list(Wrappers.<ParkingOrder>lambdaQuery()
                    .eq(ParkingOrder::getParklotId, todo.getParklotId())
                    .eq(ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_IN.getValue())
                    .eq(ParkingOrder::getPlaceId, todo.getPlaceId()))
            ).orElse(new ArrayList<>());
        // 保存停车订单
        this.save(parkingOrder);
        //推送入场消息
        parkingSuccessfulPassagePublisherI.publishEnterMessage(parkingOrderConverter.fromDO(parkingOrder));
        // 该车位如果有在场订单记录，则更新订单停车状态为进场覆盖
        overOrdersOfPlace.forEach(p -> {
            p.setParkingStatus(ParkingStatusEnum.PARK_REPEAT.getValue());
            p.setTargetParkingId(parkingOrder.getId());
            p.setExitTime(parkingOrder.getEnterTime());
            p.setExitWay(ChannelWay.WAY_10.getValue());
            updateById(p);
            //推送出场消息
            parkingSuccessfulPassagePublisherI.publishExitMessage(parkingOrderConverter.fromDO(p));
        });
        // 该车在当前车场如果有在场订单记录，则更新停车状态为进场覆盖
        this.updatePresentParkingByPlate(todo.getTenantId(), todo.getParklotId(), todo.getPlate(), todo.getOpenId(),
            parkingOrder.getId(), parkingOrder.getEnterTime());
        // 设置代办处理状态
        todo.setParkingId(parkingOrder.getId());
        todo.setHandStatus(ChannelConstant.HANDSTATUS_CONFIRM);
        log.info("{}进场创建停车记录信息结束=================================", todo.getPlate());
    }

    /**
     * 循环车前车收费处理
     *
     * @param channelTodo  代办信息
     * @param parkingOrder 停车订单
     */
    private void chargeBeforeCycle(ChannelTodo channelTodo, ParkingOrder parkingOrder) {
        Card card = iCardService.getById(channelTodo.getCardId());
        if (card != null) {
            int count = baseMapper.countPresentCarByCardId(channelTodo.getTenantId(), channelTodo.getParklotId(),
                channelTodo.getCardId(), channelTodo.getPlate(), channelTodo.getOpenId());
            if (card.getPlaceNum() <= count) {
                String[] cardIdsArr = StringUtils.isNotBlank(channelTodo.getTempCardIds()) ?
                    (channelTodo.getTempCardIds().substring(1, channelTodo.getTempCardIds().length() - 1)).split(",") :
                    (String.valueOf(channelTodo.getCardId())).split(",");
                List<Long> cardIds =
                    Arrays.stream(cardIdsArr).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                ParkingOrder parking = baseMapper.selectOnebyCardIdsDesc(channelTodo.getTenantId(),
                    channelTodo.getParklotId(), cardIds);
                if (parking != null) {
                    //将前车位释放（更改计费时间）
                    parking.setOccupied(false);
                    parking.setBillingStartTime(parkingOrder.getEnterTime());

                    List<TimeNode> timeNodeList = getParkingTimeNodeList(parking.getParkingTimeNode());
                    timeNodeList.add(new TimeNode(DateUtils.format(parkingOrder.getEnterTime()), "释放车位，开始计费",
                        "循环车按前车收费,后车：" + channelTodo.getPlate() + "进来时，释放车位给后车", true));
                    parking.setParkingTimeNode(JSON.toJSONString(timeNodeList));

                    updateById(parking);

                    //更新初始化的数据
                    parkingOrder.setCardId(parking.getCardId());
                    parkingOrder.setOccupied(true);

                    //更新原来的待办
                    channelTodo.setCardId(parking.getCardId());
                }
            }
        }
    }

    @Override
    public Integer getCurrentAmount(Long parklotId) {
        return baseMapper.getCurrentAmount(parklotId);
    }

    @Override
    public Integer getTodayInAndOutAmount(Long parklotId) {
        return baseMapper.getTodayInAndOutAmount(parklotId);
    }

    @Override
    public List<TotalInAndOutCarVO> totalInAndOutCar(Long parklotId) {
        return baseMapper.totalInAndOutCar(parklotId);
    }

    @Override
    public ParkingCarDuringVO parkingCarDuring(Long parklotId) {

        return ParkingCarDuringVO.builder()
            .one(baseMapper.countLessThanHour(parklotId))
            .six(baseMapper.betweenOneSixHour(parklotId))
            .twelve(baseMapper.betweenSixTwelve(parklotId))
            .eighteen(baseMapper.betweenTwelveEighteen(parklotId))
            .twentyFour(baseMapper.betweenEighteenTwentyFour(parklotId))
            .build();
    }

    @Override
    public List<Map> parkingEnterTimeDistribute(Long parklotId) {
        return baseMapper.parkingEnterTimeDistribute(parklotId);
    }

    @Override
    public boolean bindingCardUpdateParking(Card card, Date oldEndDate) {

        String plates = card.getPlates();
        if (StringUtils.isBlank(plates)) {
            return false;
        }

        List<String> plateList = Func.toStrList(plates);
        List<Long> parklotIds = Func.toLongList(card.getParklotIds());
        for (String plate : plateList) {
            for (Long parklotId : parklotIds) {
                ParkingOrder parkingOrder = this.selectPresentParking(card.getTenantId(), parklotId, plate, plate);
                if (ObjectUtil.isEmpty(parkingOrder)) {
                    continue;
                }
                List<CardVO> cardList = iCardService.selectAvailableCardListByPlate(parkingOrder.getTenantId(), parkingOrder.getParklotId(), parkingOrder.getPlate(), parkingOrder.getOpenId(), parkingOrder.getBillingStartTime(), new Date());
                if (CollectionUtil.isEmpty(cardList)) {
                    continue;
                }
                //月卡依旧有效
                if (card.getEndDate().compareTo(new Date()) >= 0) {
                    bindingOrTransferCard(parkingOrder, cardList, 3);
                } else {
                    bindingOrTransferCard(parkingOrder, cardList, 0);
                }
                super.saveOrUpdate(parkingOrder);

                this.processChannelCarPass(card.getId(), oldEndDate, parkingOrder);
            }
        }

        return true;
    }


    /**
     * 放行通道车辆
     *
     * @param cardId       授权卡ID
     * @param parkingOrder 停车记录
     */
    private void processChannelCarPass(Long cardId, Date oldEndDate, ParkingOrder parkingOrder) {

        try {
            CardOrder cardOrder = orderService.getOne(Wrappers.<CardOrder>lambdaQuery()
                .eq(CardOrder::getCardId, cardId)
                .eq(CardOrder::getPayStatus, 1)
                .lt(CardOrder::getStartDate, parkingOrder.getBillingStartTime())
                .gt(CardOrder::getEndDate, new Date()).last("limit 1"));
            if (cardOrder == null) {
                if (oldEndDate == null) {
                    return;
                }
                if (parkingOrder.getBillingStartTime().after(oldEndDate)) {
                    return;
                }
            }

            ChannelTodoVO todo = todoService.selectLatestOneByPlate(parkingOrder.getParklotId(), parkingOrder.getPlate());
            if (null == todo) {
                return;
            }
            ChannelTodoVO lastChannelTodo = todoService.latestInfoByChannelId(todo.getChannelId());
            // 判断当前通道最新待办是否是当前车辆
            if (null != lastChannelTodo && lastChannelTodo.getPlate().equals(todo.getPlate())) {
                parkingOrder.setParkingStatus(4);
                parkingOrder.setExitImageUrl(todo.getImageUrl());
                parkingOrder.setExitTime(todo.getDate());
                parkingOrder.setExitChannelId(todo.getChannelId());
                parkingOrder.setExitWay(todo.getTriggerType());
                parkingOrder.setExitTodoId(todo.getId());
                parkingOrder.setDurationTime(DateUtils.getDuration(parkingOrder.getEnterTime(), todo.getDate()));
                parkingOrder.setTimeNodeDetail(todo.getTimeNodeDetail());

                List<TimeNode> timeNodeList = this.getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
                timeNodeList.add(new TimeNode(DateUtils.format(todo.getDate()), "月卡缴费出场", null, false));
                parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));
                todo.setStatus(ChannelConstant.TODO_STATUS_SELF_HELP);
                todoService.updateById(todo);
                todoService.pushSentryBoxTodo(todo);

                // 推送出场通知
                msgSender.send(MsgRequest.builder()
                    .code(ParkMsgConstant.CAR_EXIT)
                    .body(parkingOrder)
                    .build());

                this.saveOrUpdate(parkingOrder);
            }
        } catch (Exception e) {
            log.warn("放行通道车辆异常", e);
        }
    }


    @Override
    public boolean updateUnboundCard(List<String> removePlateList, Card card) {

        Long parkLotId = Long.valueOf(card.getParklotIds().split(",")[0]);

        for (String removePlate : removePlateList) {
            String plate = CommonUtil.isChinese(removePlate) ? removePlate : "无牌车";
            ParkingOrder parkingOrder = this.selectPresentParking(card.getTenantId(), parkLotId, plate, removePlate);
            if (Objects.isNull(parkingOrder)) {
                continue;
            }

            List<CardVO> cardList = iCardService.selectAvailableCardListByPlate(card.getTenantId(), parkLotId, plate, removePlate, parkingOrder.getBillingStartTime(), new Date());
            if (Func.isEmpty(cardList)) {
                parkingOrder.setCardId(0L);
                parkingOrder.setTempCardIds("");
                if (Boolean.TRUE.equals(parkingOrder.getOccupied())) {
                    parkingOrder.setBillingStartTime(new Date());
                }
                parkingOrder.setOccupied(false);
                this.saveOrUpdate(parkingOrder);

                continue;
            }

            bindingOrTransferCard(parkingOrder, cardList, 2);

            this.saveOrUpdate(parkingOrder);

        }

        return true;

    }


    @Override
    public boolean updateBindingCard(List<String> addPlateList, Card card) {

        Long parkLotId = Long.valueOf(card.getParklotIds().split(",")[0]);

        for (String plateNo : addPlateList) {
            String plate = plateNo;
            if (!CommonUtil.isChinese(plateNo)) {
                plate = "无牌车";
            }

            ParkingOrder parkingOrder = this.selectPresentParking(card.getTenantId(), parkLotId, plate, plateNo);
            if (ObjectUtils.isEmpty(parkingOrder)) {
                continue;
            }

            List<CardVO> cardList = iCardService.selectAvailableCardListByPlate(card.getTenantId(), parkLotId, plate, plateNo, parkingOrder.getBillingStartTime(), new Date());
            if (ObjectUtils.isEmpty(cardList)) {
                continue;
            }

            bindingOrTransferCard(parkingOrder, cardList, 1);

            this.saveOrUpdate(parkingOrder);

        }
        return true;
    }


    /**
     * 移除车牌或增加车牌
     *
     * @param parkingOrder 停车订单
     * @param cardList     卡列表
     * @param flag         0新开卡，1添加车牌，2移除车牌
     */
    private void bindingOrTransferCard(ParkingOrder parkingOrder, List<CardVO> cardList, int flag) {
        String cardIds = iCardService.selectAvailableCardIds(parkingOrder.getTenantId(), parkingOrder.getParklotId(), parkingOrder.getPlate());
        if (flag == 0 || flag == 3) {
            parkingOrder.setCardId(cardList.get(0).getId());
            parkingOrder.setRelationId(cardList.get(0).getId());
            parkingOrder.setRelationType(PlatePropertyType.MONTH_CARD.getValue());
            parkingOrder.setTempCardIds(cardIds);
            //都设置成无车位， 出场时会处理
            if (flag == 0) {
                parkingOrder.setOccupied(false);
            }
            parkingOrder.setTempCar(false);
        } else {

            List<CardVO> cardVOList = enterService.sortList(cardList);
            for (int i = 0; i < cardVOList.size(); i++) {
                int count = this.countPresentCarByCardId(cardVOList.get(i).getTenantId(), parkingOrder.getParklotId(), cardVOList.get(i).getId(), parkingOrder.getPlate(), parkingOrder.getOpenId());

                if (cardVOList.get(i).getPlaceNum() > count) {
                    parkingOrder.setCardId(cardVOList.get(i).getId());
                    parkingOrder.setRelationId(cardVOList.get(i).getId());
                    parkingOrder.setRelationType(PlatePropertyType.MONTH_CARD.getValue());
                    parkingOrder.setTempCardIds(cardIds);
                    parkingOrder.setBillingStartTime(new Date());
                    parkingOrder.setOccupied(true);
                    parkingOrder.setTempCar(false);
                    break;
                }

                // 所有卡的车位都被占用
                if (i == cardList.size() - 1) {
                    if (Boolean.TRUE.equals(parkingOrder.getOccupied())) {
                        parkingOrder.setOccupied(false);
                        parkingOrder.setBillingStartTime(new Date());
                    }
                    parkingOrder.setCardId(cardVOList.get(i).getId());
                    parkingOrder.setRelationId(cardVOList.get(i).getId());
                    parkingOrder.setRelationType(PlatePropertyType.MONTH_CARD.getValue());
                    parkingOrder.setTempCardIds(cardIds);
                    parkingOrder.setTempCar(false);
                }
            }

        }


    }

    /**
     * 创建停车记录信息
     *
     * @param channelTodo 通道代办信息
     * @return ParkingOrder
     */
    private ParkingOrder createParking(ChannelTodo channelTodo) {
        ParkingOrder park = new ParkingOrder();
        park.setParklotId(channelTodo.getParklotId());
        park.setPlate(channelTodo.getPlate());
        park.setCardType(channelTodo.getCardType());
        park.setCardId(channelTodo.getCardId());
        park.setOccupied(channelTodo.getOccupied());
        park.setEnterChannelId(channelTodo.getChannelId());
        park.setEnterWay(channelTodo.getTriggerType());
        park.setParkingStatus(ParkingStatus.SPRKING_PRESENT);
        park.setEnterTime(channelTodo.getDate());
        park.setEnterImageUrl(channelTodo.getEnterImageUrl());
        park.setChargeRuleId(channelTodo.getChargeRuleId());
        park.setPayTypes("");
        park.setBillingStartTime(channelTodo.getDate());
        park.setEnterTodoId(channelTodo.getId());
        park.setOpenId(channelTodo.getOpenId());
        park.setTempCardIds(channelTodo.getTempCardIds());
        park.setTenantId(channelTodo.getTenantId());
        park.setReserveOrderId(channelTodo.getReserveOrderId());
        park.setTargetParkingId(channelTodo.getTargetParkingId());
        park.setParkingTimeNode(channelTodo.getParkingTimeNode());
        park.setRelationId(channelTodo.getRelationId());
        park.setRelationType(channelTodo.getRelationType());
        park.setPlaceId(channelTodo.getPlaceId());
        park.setVehicleId(channelTodo.getVehicleId());

        return park;
    }

    @Override
    public ParkingOrder getByReserveOrderId(String reserveOrderId) {
        return getOne(new QueryWrapper<ParkingOrder>().lambda().eq(ParkingOrder::getReserveOrderId, reserveOrderId));
    }

    @Override
    public IPage<ParkingOrderVO> selectUserParkingPage(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO) {

        List<WechatPlate> wechatPlates = userPlateService.selectBUserPlateList(parkingOrderDTO.getOpenId());

        if (ObjectUtils.isEmpty(wechatPlates)) {
            return page.setRecords(null);
        }

        List<String> plateList = new ArrayList<>();
        wechatPlates.forEach(p -> {
            plateList.add(p.getPlate());
        });

        List<ParkingOrderVO> parkingList = baseMapper.selectUserParkingPage(page, plateList, parkingOrderDTO);
        if (ObjectUtils.isEmpty(parkingList)) {
            return page.setRecords(null);
        }

        for (ParkingOrderVO p : parkingList) {
            Date exitTime = ObjectUtil.isNotEmpty(p.getExitTime()) ? p.getExitTime() : new Date();

            p.setParkingPeriod(DateUtils.yMdHm(p.getEnterTime()) + " 至 " + DateUtils.Hm(exitTime));
            p.setDurationTime(DateUtils.getDuration(p.getEnterTime(), exitTime));

            initNullAmount(p);
            //在场（实时计算费用）
            if (p.getParkingStatus().equals(ParkingStatus.SPRKING_PRESENT)) {

                if (presentRecord(p)) {
                    continue;
                }

                //已出场
            } else if (ParkingStatus.SPRKING_APPEARED.equals(p.getParkingStatus())) {
                p.setPaymentStatus(PayStatus.SUCCESS.getKey());
            }

        }

        return page.setRecords(parkingList);
    }

    @Override
    public void testDel() {
        this.removeById(1286544759124434945L);
    }

    private boolean presentRecord(ParkingOrderVO p) {
        p.setPaymentStatus(PayStatus.UNPAID.getKey());
        TempParkingChargeRuleVO chargeRule = chargeRuleService.selectStandardOneByParkLotId(p.getParklotId());
        if (ObjectUtils.isEmpty(chargeRule)) {
            return true;
        }
        ProjectCost cost = new ProjectCost();
        // 是否添加首停判断
        cost.setIsFirstParking(ParkLotCaches.isFirstParking(chargeRule.getFirstParkingConfigId(), p.getPlate(), p.getEnterTime()));
        //计算费用
        ProjectCalculate.calculate(cost, "临停计费时段", chargeRule, p.getBillingStartTime(), new Date());
        handleCost(cost, p);
        p.setTotalAmount(cost.getTotalAmount());
        p.setPaidAmount(cost.getPaidAmount());
        p.setReceiveAmount(cost.getTotalAmount());
        p.setDiscountAmount(cost.getDiscountAmount());
        p.setUnusualAmount(cost.getUnusualAmount());
        return false;
    }

    /**
     * 费用处理
     *
     * @param cost 费用
     * @param p    停车订单VO
     */
    private void handleCost(ProjectCost cost, ParkingOrderVO p) {
        cost.setReceiveAmount(cost.getTotalAmount());
        //提前缴费
        ProjectCost paidCost = tempParkingOrderService.selectPaidByParkingId(p.getId());
        if (paidCost != null) {
            log.info("-----------------超时出场扣除场内提前缴费金额-------------------");
            BigDecimal subtract = cost.getReceiveAmount().subtract(paidCost.getPaidAmount());
            cost.setReceiveAmount(subtract);
            cost.setPaidAmount(cost.getPaidAmount().add(paidCost.getPaidAmount()));
            p.setOvertime(true);
        }

    }

    /**
     * 金额空处理
     *
     * @param parkingOrderVO 停车订单VO
     */
    private void initNullAmount(ParkingOrderVO parkingOrderVO) {
        if (ObjectUtils.isEmpty(parkingOrderVO.getTotalAmount())) {
            parkingOrderVO.setTotalAmount(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(parkingOrderVO.getReceiveAmount())) {
            parkingOrderVO.setReceiveAmount(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(parkingOrderVO.getDiscountAmount())) {
            parkingOrderVO.setDiscountAmount(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(parkingOrderVO.getUnusualAmount())) {
            parkingOrderVO.setUnusualAmount(BigDecimal.ZERO);
        }
    }

    @Override
    public StatisticsVO countNumberParking(Long parkLotId) {
        Date minDate = DateUtils.getDateMinOrMaxTime(new Date(), true);
        Date maxDate = DateUtils.getDateMinOrMaxTime(new Date(), false);

        StatisticsVO statisticsVO = baseMapper.countNumberParking(parkLotId, minDate, maxDate);
        if (ObjectUtils.isEmpty(statisticsVO)) {
            statisticsVO = new StatisticsVO();
        }

        return statisticsVO;

    }

    @Override
    public int countFreeParkingSpace(Integer lotAmount, Long parkLotId) {

        int presentNumber = baseMapper.countPresent(parkLotId);

        return Math.max((lotAmount - presentNumber), 0);
    }

    @Override
    public List<ParkingOrderVO> selectParkingList(RoadSideClientDto roadSideClient, Date minDate, Date maxDate,
                                                  Integer parkingStatus) {
        return baseMapper.selectParkingList(roadSideClient, minDate, maxDate, parkingStatus);
    }

    @Override
    public void updatePresentByPlaceId(String tenantId, Long parkLotId, Long placeId) {
        baseMapper.updatePresentByPlaceId(tenantId, parkLotId, placeId);
    }

    @Override
    public ParkingOrder selectPresentByPlaceId(String tenantId, Long parkLotId, Long placeId) {
        return baseMapper.selectPresentByPlaceId(tenantId, parkLotId, placeId);
    }

    @Override
    public ParkingOrder selectPresentByPlaceId(Long placeId) {
        return this.lambdaQuery().eq(ParkingOrder::getPlaceId, placeId)
            .eq(ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_IN.getValue())
            .orderByDesc(ParkingOrder::getCreateTime)
            .last("limit 1")
            .one();
    }

    @Override
    public ParkingOrderVO selectParkingDetailOne(Long parkingId) {
        ParkingOrderVO parking = baseMapper.selectParkingDetailOne(parkingId);
        ProjectCost c = unpaidOrderService.selectUnpaidOrderAmountInfo(parking.getId());
        if (ObjectUtil.isNotEmpty(c)) {
            parking.setReceiveAmount(c.getReceiveAmount());
            parking.setPaymentStatus(PayStatus.UNPAID.getKey());
            parking.setDelayTime(DateUtils.getDuration(parking.getEnterTime(), parking.getExitTime()));

        } else {
            parking.setPaymentStatus(PayStatus.SUCCESS.getKey());
        }
        return parking;
    }

    @Override
    public boolean isPresent(String tenantId, Long parkLotId, Long placeId, int timeouts) {

        ParkingOrder parkingOrder = baseMapper.selectPresent(tenantId, parkLotId, placeId, timeouts);
        return ObjectUtil.isNotEmpty(parkingOrder);
    }

    @Override
    public ParkingOrderVO haveRecord(Long parkLotId, Long placeId, Integer timeouts) {
        return baseMapper.selectOneExitTimeParkingByPlaceId(parkLotId, placeId, timeouts);
    }

    @Override
    public ParkingOrderVO haveRecord(Long parkLotId, String plate, String openId, Integer timeouts) {
        return this.selectOneParking(parkLotId, plate, openId, timeouts);
    }


    @Override
    public boolean haveRecord(Long relationId, PlatePropertyType relationType, String plate) {
        return count(Wrappers.<ParkingOrder>lambdaQuery()
            .eq(ParkingOrder::getRelationId, relationId)
            .eq(Func.isNotBlank(plate), ParkingOrder::getPlate, plate)
            .eq(null != relationType, ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_OUT.getValue())) > 0;
    }

    @Override
    public Boolean merchantIdHaveRecord(Long merchantParkLotPlateId) {
        return haveRecord(merchantParkLotPlateId, PlatePropertyType.MERCHANT_CARD, null);
    }

    @Override
    public ParkingOrderVO selectOneParking(Long parkLotId, String plate, String openId, Integer timeouts) {

        if (StringUtils.isNotBlank(openId) && TodoContext.isNoPlate(plate)) {
            return baseMapper.selectOneExitTimeParkingByOpenId(parkLotId, openId, timeouts);
        }
        if (TodoContext.isNoPlate(plate)) {
            return null;
        }
        return baseMapper.selectOneExitTimeParkingByPlate(parkLotId, plate, timeouts);
    }

    @Override
    public int countParkingVipNumber(Long parkLotId) {
        return baseMapper.countParkingVipNumber(parkLotId);
    }

    @Override
    public List<Map<String, Object>> queryParkingInfoList(ParkingOrderDTO parkingOrderDTO) {
        return baseMapper.queryParkingInfoList(parkingOrderDTO);
    }

    /**
     * 近7天流量分析
     *
     * @param parklotId 车场id
     * @return List<ParkingOrderStatisticsVO>
     */
    @Override
    public ParkLastWeekVO lastWeekParkingOrder(String parklotId) {
        ParkLastWeekVO result = new ParkLastWeekVO();
        List<SeriesData> seriesDataList = new ArrayList<>();
        List<Parklot> parklotList;

        //获取车场列表
        if (Func.isEmpty(parklotId)) {
            List<Long> parkLotIds = userParklotService.getParkLotIds(AuthUtil.getUserId());
            parklotList = parklotService.listByIds(parkLotIds);
        } else {
            Parklot parklot = ParkLotCaches.getParkLot(Long.valueOf(parklotId));
            parklotList = Collections.singletonList(parklot);
        }

        for (int i = 0; i < parklotList.size(); i++) {
            List<ParkingOrderStatisticsVO> statisticsVO = baseMapper.lastWeekParkingOrder(parklotList.get(i).getId());

            //近七天日期
            List<String> lastWeekDay = new ArrayList<>();
            //出入场记录数量
            List<Integer> recordCount = new ArrayList<>();

            for (ParkingOrderStatisticsVO parkingOrderStatisticsVO : statisticsVO) {
                lastWeekDay.add(parkingOrderStatisticsVO.getClickDate());
                recordCount.add(Func.isEmpty(parkingOrderStatisticsVO.getCount()) ? 0 :
                    parkingOrderStatisticsVO.getCount());
            }
            //获取车场近七天日期
            if (i == 0) {
                result.setLastWeekDay(lastWeekDay);
            }
            SeriesData seriesData = new SeriesData();
            seriesData.setName(parklotList.get(i).getName());
            seriesData.setData(recordCount);
            seriesDataList.add(seriesData);
        }
        result.setData(seriesDataList);
        return result;
    }

    @Override
    public BigDecimal getTodayIncome(List<Long> parklotId) {
        return baseMapper.getTodayIncome(parklotId);
    }

    @Override
    public BigDecimal getTodayOwnFee(List<Long> parklotId) {
        return baseMapper.getTodayOwnFee(parklotId);
    }

    /**
     * 当日停车时段分析
     *
     * @param parklotId 车场id
     * @return List<StopDurationVO>
     */
    @Override
    public List<StopDurationVO> stopDuration(String parklotId) {
        return baseMapper.stopDuration(parklotId);
    }

    @Override
    public BigDecimal getCurrentMonthPaidAmount(String parklotId) {
        return baseMapper.getCurrentMonthPaidAmount(parklotId);
    }

    @Override
    public BigDecimal getLastMonthPaidAmount(String parklotId) {
        return baseMapper.getLastMonthPaidAmount(parklotId);
    }

    @Override
    public BigDecimal getCurrentYearPaidAmount(String parklotId) {
        return baseMapper.getCurrentYearPaidAmount(parklotId);
    }

    @Override
    public BigDecimal getLastYearPaidAmount(String parklotId) {
        return baseMapper.getLastYearPaidAmount(parklotId);
    }

    @Override
    public BigDecimal getCurrentMonthOweAmount(String parklotId) {
        return baseMapper.getCurrentMonthOweAmount(parklotId);
    }

    @Override
    public BigDecimal getLastMonthOweAmount(String parklotId) {
        return baseMapper.getLastMonthOweAmount(parklotId);
    }

    @Override
    public BigDecimal getCurrentYearOweAmount(String parklotId) {
        return baseMapper.getCurrentYearOweAmount(parklotId);
    }

    @Override
    public BigDecimal getLastYearOweAmount(String parklotId) {
        return baseMapper.getLastYearOweAmount(parklotId);
    }

    @Override
    public int getNumInPark(List<Long> parklotIds, Date date) {
        return baseMapper.getNumInPark(parklotIds, date);
    }

    /**
     * 泊位利用率分析
     *
     * @param parklotId 车场id 车场id
     * @return ParkLastWeekRateVO
     */
    @Override
    public ParkLastWeekRateVO parkUseRate(String parklotId) {
        ParkLastWeekRateVO result = new ParkLastWeekRateVO();
        List<Series> seriesDataList = new ArrayList<>();

        //获取车场列表
        List<Parklot> parklotList;
        if (Func.isNotEmpty(parklotId)) {
            Parklot parklot = ParkLotCaches.getParkLot(Long.valueOf(parklotId));
            parklotList = Collections.singletonList(parklot);
        } else {
            List<Long> parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
            parklotList = parklotService.listByIds(parkLotIds);
        }


        for (int i = 0; i < parklotList.size(); i++) {
            List<ParkingOrderStatisticsVO> statisticsVO = baseMapper.lastWeekParkingOrder(parklotList.get(i).getId());

            List<String> lastWeekDay = new ArrayList<>();
            List<Double> remainNumList = new ArrayList<>();

            for (ParkingOrderStatisticsVO parkingOrderStatisticsVO : statisticsVO) {
                lastWeekDay.add(parkingOrderStatisticsVO.getClickDate());

                //车场临停车位数
                Integer tempLotAmount = parklotList.get(i).getTempLotAmount();
                if (Func.isEmpty(tempLotAmount) || tempLotAmount == 0) {
                    remainNumList.add((double) 0);
                } else {
                    Integer count = parkingOrderStatisticsVO.getCount();
                    remainNumList.add(
                        BigDecimal.valueOf(count)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(tempLotAmount), 2, RoundingMode.HALF_UP)
                            .doubleValue()
                    );
                }
            }

            if (i == 0) {
                result.setLastWeekDay(lastWeekDay);
            }

            Series seriesData = new Series();
            seriesData.setName(parklotList.get(i).getName());
            seriesData.setData(remainNumList);
            seriesDataList.add(seriesData);
        }
        result.setData(seriesDataList);
        return result;
    }

    /**
     * 财务分析
     *
     * @param parklotId 车场id
     * @return 财务分析
     */
    @Override
    public List<List<String>> financialAnalysis(String parklotId) {
        List<List<String>> result = new ArrayList<>();
        List<Parklot> parklotList;
        if (Func.isNotBlank(parklotId)) {
            Parklot parklot = ParkLotCaches.getParkLot(parklotId);
            LecentAssert.notNull(parklot, "不存在此车场");
            parklotList = Collections.singletonList(parklot);
        } else {
            List<Long> parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
            parklotList = parklotService.listByIds(parkLotIds);
        }

        List<String> parkNames = new ArrayList<>();
        parkNames.add("parkName");
        parkNames.addAll(parklotList.stream().map(Parklot::getName).collect(Collectors.toList()));
        result.add(parkNames);
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<String> list3 = new ArrayList<>();
        List<String> list4 = new ArrayList<>();
        List<String> list5 = new ArrayList<>();
        List<String> list6 = new ArrayList<>();
        List<String> list7 = new ArrayList<>();
        for (Parklot parklot : parklotList) {
            List<ParkingMoneyStatisticsVO> dataList = baseMapper.financialAnalysis(parklot.getId());
            for (int i = 0; i < dataList.size(); i++) {
                if (i == 0) {
                    if (list1.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list1.add(clickDate);
                    }
                    list1.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 1) {
                    if (list2.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list2.add(clickDate);
                    }
                    list2.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 2) {
                    if (list3.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list3.add(clickDate);
                    }
                    list3.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 3) {
                    if (list4.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list4.add(clickDate);
                    }
                    list4.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 4) {
                    if (list5.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list5.add(clickDate);
                    }
                    list5.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 5) {
                    if (list6.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list6.add(clickDate);
                    }
                    list6.add(dataList.get(i).getPaidAmount().toString());
                }
                if (i == 6) {
                    if (list7.isEmpty()) {
                        String clickDate = dataList.get(i).getWeekDay();
                        list7.add(clickDate);
                    }
                    list7.add(dataList.get(i).getPaidAmount().toString());
                }
            }
        }
        result.add(list1);
        result.add(list2);
        result.add(list3);
        result.add(list4);
        result.add(list5);
        result.add(list6);
        result.add(list7);
        return result;
    }

    @Override
    public Integer getTodayParkingSum(List<Long> parklotIds) {
        return baseMapper.getTodayParkingSum(parklotIds);
    }

    @Override
    public ParkingOrder getByExitTodoId(Long exitTodoId) {
        List<ParkingOrder> list = this.list(Wrappers.<ParkingOrder>lambdaQuery().eq(ParkingOrder::getExitTodoId,
            exitTodoId));
        if (!list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public boolean confirmEnter(long parkLotId, int channelNo) {
        Channel channel = channelService.getChannelByNo(parkLotId, channelNo);
        if (Func.isEmpty(channel)) {
            log.info("车辆过地感时；通道不存在！");
            return false;
        }

        if (ChannelConstant.CHANNEL_TYPE_ENTER == channel.getType()) {
            Long enterSeconds = this.enterSeconds;
            if (Func.isEmpty(enterSeconds)) {
                enterSeconds = 30L;
            }
            baseMapper.updateEnterConfirm(parkLotId, channel.getId(), enterSeconds);
        }
        return true;
    }

    @Override
    public Long getTargetParkingId(String plate) {
        return baseMapper.getTargetParkingId(plate);
    }

    @Override
    public List<ParkingOrder> getAllCorpseCar() {
        return baseMapper.getAllCorpseCar();
    }

    @Override
    public List<ParkingOrderVO> selectCorpsePage(IPage<ParkingOrder> page, CorpseCleanLogDTO queryBean) {
        List<Long> bindParkLotIds = userParklotService.getCurrentUserBindParkLotIdsBylist(queryBean.getParkLotIdList());
        // 无关联车场直接返回空
        if (CollectionUtil.isEmpty(bindParkLotIds)) {
            return Collections.emptyList();
        }
        queryBean.setParkLotIdList(bindParkLotIds);

        return baseMapper.selectCorpsePage(page, queryBean);
    }

    @Override
    public boolean limitTimehaveRecord(Long parkLotId, String plate, Date startTime, Date endTime) {
        ParkingOrder p = baseMapper.selectExitRecord(parkLotId, plate, startTime, endTime);
        return Func.isNotEmpty(p);
    }

    @Override
    public Map<String, Integer[]> carFlowCount() {
        List<Long> parkLotIds = userParklotService.getParkLotIds(SecureUtil.getUserId());
        CarFlow carFlow = baseMapper.carFlowCount(parkLotIds);
        if (Func.isEmpty(carFlow)) {
            carFlow = new CarFlow();
            carFlow.setEnterCar(CarFlow.getDefaultData());
            carFlow.setExitCar(CarFlow.getDefaultData());
        }

        if (Func.isBlank(carFlow.getEnterCar())) {
            carFlow.setEnterCar(CarFlow.getDefaultData());
        }
        if (Func.isBlank(carFlow.getExitCar())) {
            carFlow.setExitCar(CarFlow.getDefaultData());
        }

        Map<String, Integer[]> resultMap = new HashMap<>(2);
        resultMap.put("enterCar", Func.toIntArray(carFlow.getEnterCar()));
        resultMap.put("exitCar", Func.toIntArray(carFlow.getExitCar()));
        return resultMap;
    }

    @Override
    public ParkingOrder selectOccupied(Long cardId, Long parkLotId, String plate) {
        return baseMapper.selectOccupied(cardId, parkLotId, plate);
    }

    @Override
    public ParkingOrder selectAfterCarByCardId(String tenantId, Long parkLotId, Long merchantParkLotId) {
        List<ParkingOrder> list = baseMapper.selectAfterCarByCardId(tenantId, parkLotId, merchantParkLotId);
        if (Func.isEmpty(list)) {
            return null;
        }
        List<ParkingOrder> listSort = list.stream().sorted(Comparator.comparing(ParkingOrder::getEnterTime)).collect(Collectors.toList());
        return listSort.get(0);
    }

    @Override
    public List<ParkingOrderVO> merchantAuthPlateList(Long authPlateId) {

        return baseMapper.merchantAuthPlateList(authPlateId);

    }


    @Override
    public ParkingOrder selectPresentParkingByCardId(Long cardId, Long parkLotId) {
        List<ParkingOrder> list = this.list(Wrappers.<ParkingOrder>lambdaQuery()
            .eq(ParkingOrder::getParkingStatus, 2)
            .eq(ParkingOrder::getCardId, cardId)
            .eq(ParkingOrder::getParklotId, parkLotId)
            .orderByDesc(ParkingOrder::getEnterTime)
        );
        if (Func.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public ParkingOrder selectPresentParkingByCardId(Long cardId, Long parkLotId, String plate) {
        List<ParkingOrder> list = this.list(Wrappers.<ParkingOrder>lambdaQuery()
            .eq(ParkingOrder::getParkingStatus, 2)
            .eq(ParkingOrder::getCardId, cardId)
            .eq(ParkingOrder::getParklotId, parkLotId)
            .eq(ParkingOrder::getPlate, plate)
            .orderByDesc(ParkingOrder::getEnterTime)
        );
        if (Func.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public ParkingOrder getPresentByPlate(Long parkLotId, String plate) {

        List<ParkingOrder> list = this.list(Wrappers.<ParkingOrder>lambdaQuery()
            .eq(ParkingOrder::getParklotId, parkLotId)
            .eq(ParkingOrder::getPlate, plate)
            .eq(ParkingOrder::getParkingStatus, 2)
            .orderByDesc(ParkingOrder::getEnterTime)
        );
        if (Func.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<ParkingOrder> presentRecords(String plate) {
        return this.baseMapper.presentRecords(plate, null);
    }

    @Override
    public IPage<ParkingOrderVO> selectUserParking(Query query, ParkingOrderDTO parkingOrderDTO) {
        IPage<ParkingOrderVO> page = Condition.getPage(query);
        List<BUserPlateVO> bUserPlateVOS = userPlateService.listMinePlates();
        if (Func.isNotEmpty(bUserPlateVOS)) {
            List<String> plates = bUserPlateVOS.stream().map(BUserPlateVO::getPlate).collect(Collectors.toList());
            parkingOrderDTO.setPlates(plates);
            page = selectParkingGtPageNonTenant(query, parkingOrderDTO);
        }
        return page;
    }

    private IPage<ParkingOrderVO> selectParkingGtPageNonTenant(Query query, ParkingOrderDTO parkingOrderDTO) {
        return this.userCustomPage(parkingOrderDTO, query);
    }

    @Override
    public ParkingOrder getLatestRecord(String plate) {
        return getLatestRecord(plate, null);
    }

    @Override
    public ParkingOrder getLatestRecord(String plate, List<Long> parklotIds) {
        LecentAssert.notBlank(plate, "请输入车牌！");
        List<ParkingOrder> parkingOrders = this.baseMapper.presentRecords(plate, parklotIds);
        LecentAssert.notEmpty(parkingOrders, "未查到入场信息!");
        return parkingOrders.get(0);
    }

    @Override
    public ParkingOrder queryPresentRecord(Long parklotId, String plate) {
        return this.baseMapper.presentRecord(parklotId, plate);
    }

    @Override
    public Long getUsedTimeLength(List<Long> cardIds, Long parkLotId, Date startDate, Date endDate, String plate) {
        if (Func.isEmpty(cardIds)) {
            return 0L;
        }
        return baseMapper.getUsedTimeLength(cardIds, parkLotId, startDate, endDate, plate);
    }

    @Override
    public Long merchantUseTimeLength(ParkMerchantParklot merchantParkLot, long addNum) {
        Date preSettlementTime = parkMerchantParklotService.preSettlementTime(merchantParkLot);
        Date settlementStartTime = parkMerchantParklotService.settlementStartTime(merchantParkLot.getSettleFrequency(), preSettlementTime, merchantParkLot.getSettlementCycle());

        Long usedTimeLength = 0L;
        /*
		boolean isValid = false;

		String key = String.format("%s:%s:%s", MERCHANT_TIME_LENGTH_COUNT, merchantParkLot.getParklotId(), merchantParkLot.getId());
		TimeLength redisTimeLength = CacheUtils.get(key, TimeLength.class);
		if (redisTimeLength != null) {
			usedTimeLength = redisTimeLength.getUsedLength();

			//缓存时间在统计时间内，取缓存的数据
			isValid = preSettlementTime.before(redisTimeLength.getCreateTime()) && redisTimeLength.getCreateTime().before(settlementStartTime);

			if (addNum > 0) {
				redisTimeLength.setUsedLength(usedTimeLength + addNum);
				CacheUtils.setEx(key, redisTimeLength, 24);
			}

			if (usedTimeLength < 1) {
				isValid = false;
			}
		}

		if (!isValid) {
			usedTimeLength = baseMapper.getUsedTimeLength(Func.toLongList(String.valueOf(merchantParkLot.getId())), merchantParkLot.getParklotId(), preSettlementTime, settlementStartTime, null);
			TimeLength build = TimeLength.builder()
				.createTime(DateUtil.plusMinutes(new Date(), 5))
				.usedLength(usedTimeLength + addNum).build();
			CacheUtils.setEx(key, build, 24);
		}
         */
        usedTimeLength = baseMapper.getUsedTimeLength(Func.toLongList(String.valueOf(merchantParkLot.getId())), merchantParkLot.getParklotId(), preSettlementTime, settlementStartTime, null);
        return usedTimeLength;
    }

    @Override
    public List<TimeNode> getParkingTimeNodeList(String parkingTimeNode) {
        if (Func.isBlank(parkingTimeNode)) {
            return new ArrayList<>();
        }
        List<TimeNode> timeNodeList = JSON.parseArray(parkingTimeNode, TimeNode.class);
        if (Func.isNotEmpty(timeNodeList)) {
            timeNodeList = timeNodeList.stream().filter(tn -> Func.isNotBlank(tn.getStartDate())).collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(TimeNode::getStartDate))), ArrayList::new));
            timeNodeList = timeNodeList.stream().sorted(Comparator.comparing(TimeNode::getStartDate)).collect(Collectors.toList());
            return timeNodeList;
        }
        return new ArrayList<>();
    }

    /**
     * 获取车场最新的出入场记录
     *
     * @param parklotId 车场id
     * @return JHParkingOrderVO
     */
    @Override
    public JHParkingOrderVO getLastParkingOrder(String parklotId) {

        List<Long> parklotIdList;
        if (Func.isBlank(parklotId)) {
            parklotIdList = userParklotService.getParkLotIds(AuthUtil.getUserId());
        } else {
            parklotIdList = Collections.singletonList(Long.valueOf(parklotId));
        }

        return baseMapper.getLastParkingOrderByParklotId(parklotIdList);
    }

    /**
     * 今天过车数量
     *
     * @param parklotIdList 车场id列表
     * @return Integer 今天过车数量
     */
    @Override
    public Integer getTodayPassNum(List<Long> parklotIdList) {
        return baseMapper.getTodayPassNum(parklotIdList);
    }

    @Override
    public List<FlowRateVO> carFlowRateStatistics(Integer statisticsType) {
        List<FlowRateVO> ret = getInitFlowRateData(statisticsType);
        List<Long> parkLotIds = userParklotService.getParkLotIds(AuthUtil.getUserId());
        return Func.isEmpty(parkLotIds) ? ret : processData(ret, this.baseMapper.carFlowRateStatistics(statisticsType, parkLotIds));
    }

    private List<FlowRateVO> processData(List<FlowRateVO> ret, List<FlowRateVO> carFlowRateStatistics) {
        ret.forEach(retVO -> carFlowRateStatistics.forEach(dataVO -> {
            if (retVO.getStatisticsType().equals(dataVO.getStatisticsType())
                && retVO.getTime().equals(dataVO.getTime())) {
                retVO.setNum(dataVO.getNum());
            }
        }));
        return ret;
    }

    private List<FlowRateVO> getInitFlowRateData(Integer statisticsType) {
        List<FlowRateVO> ret = new ArrayList<>();
        List<Date> times = new ArrayList<>();
        int length = 24;
        if (2 == statisticsType) {
            length = 7;
        }
        if (3 == statisticsType) {
            length = 30;
        }
        Date currDate = new Date();
        for (int i = length - 1; i > 0; i--) {
            if (1 == statisticsType) {
                times.add(DateUtils.plusMinutes(currDate, -i * 60));
            } else {
                times.add(DateUtils.plusDays(currDate, -i));
            }
        }
        times.add(currDate);
        times.forEach(time -> {
            String timeStr;
            if (1 == statisticsType) {
                timeStr = DateUtils.dateFormat(time, "yyyy-MM-dd HH");
            } else {
                timeStr = DateUtils.dateFormat(time, DateUtils.DATE_FORMAT);
            }
            FlowRateVO in = new FlowRateVO();
            in.setTime(timeStr);
            in.setNum(0);
            in.setStatisticsType("入场");
            ret.add(in);
            FlowRateVO card = new FlowRateVO();
            card.setTime(timeStr);
            card.setNum(0);
            card.setStatisticsType("出场");
            ret.add(card);
        });
        return ret;
    }

    @Override
    public List<HomeEnterAndExitVO> homeEnterAndExit(List<Long> parklots) {
        return this.baseMapper.homeEnterAndExit(parklots);
    }

    @Override
    public List<ParkingOrder> fuzzyQueryPresent(String plate, Long parkLotId) {
        return this.list(Wrappers.<ParkingOrder>lambdaQuery()
            .eq(ParkingOrder::getParklotId, parkLotId)
            .eq(ParkingOrder::getParkingStatus, 2)
            .eq(ParkingOrder::getOccupied, false)
            .eq(ParkingOrder::getCardId, 0)
            .like(ParkingOrder::getPlate, plate));
    }

    @Override
    public ParkingOrder getParkingById(Long parkingId) {
        return this.getById(parkingId);
    }

    @Override
    public Integer countEnterTempNumber(Long parkLotId, Boolean isSecondEnterConfirm) {
        return baseMapper.countEnterTempNumber(parkLotId, isSecondEnterConfirm);
    }

    @Override
    public List<ParkingOrder> presentParking(Long parkLotId, int day) {
        return this.list(Wrappers.<ParkingOrder>lambdaQuery()
            .select(ParkingOrder::getId, ParkingOrder::getPlate)
            .eq(ParkingOrder::getParklotId, parkLotId)
            .eq(ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_IN.getValue())
            .gt(ParkingOrder::getCreateTime, DateUtil.minusDays(DateUtil.now(), day))
            .ne(ParkingOrder::getPlate, "无牌车")
            .eq(ParkingOrder::getOccupied, 0)
            .orderByDesc(ParkingOrder::getCreateTime)
            .last("limit " + matchPlateQueryLimit));
    }

    @Override
    public Set<String> presentParking(Long parklotId) {
        return this.list(Wrappers.<ParkingOrder>lambdaQuery()
                .select(ParkingOrder::getPlate)
                .eq(ParkingOrder::getParklotId, parklotId)
                .eq(ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_IN.getValue())
                .eq(ParkingOrder::getOccupied, 0)
                .ne(ParkingOrder::getPlate, "无牌车"))
            .stream()
            .map(ParkingOrder::getPlate)
            .collect(Collectors.toSet());
    }

    @Override
    public CCBParkingOrderDetailVO getParkOrderDetail(Long parkingId) {
        return baseMapper.getParkOrderDetail(parkingId);
    }

    /**
     * 根据cardId查询其他在场车辆列表
     *
     * @param cardId 月卡ID
     * @param plate  车牌
     * @param openId openId
     * @return List<ParkingOrder>
     */
    @Override
    public List<ParkingOrder> listOtherPresentParkingOrderByCard(Long cardId, String plate, String openId) {
        return list(Wrappers.<ParkingOrder>lambdaQuery().select(ParkingOrder::getPlate)
            .eq(ParkingOrder::getRelationId, cardId)
            .eq(ParkingOrder::getRelationType, PlatePropertyType.MONTH_CARD)
            .eq(ParkingOrder::getParkingStatus, ParkingStatusEnum.PARK_IN.getValue())
            .ne(Func.isNotBlank(plate), ParkingOrder::getPlate, plate)
            .ne(Func.isNotBlank(openId), ParkingOrder::getOpenId, openId));
    }

    @Override
    public boolean updateById(ParkingOrder entity) {
        if (entity == null) {
            return false;
        }
        // 清除在场缓存
        ParkLotCaches.delParkingOrderByPlateOrOpenId(entity.getParklotId(), entity.getPlate(), entity.getOpenId());
        return super.updateById(entity);
    }

    @Override
    public boolean saveOrUpdate(ParkingOrder entity) {
        // 清除在场缓存
        ParkLotCaches.delParkingOrderByPlateOrOpenId(entity.getParklotId(), entity.getPlate(), entity.getOpenId());
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean visitorAuthHaveRecord(Long cardId, String plate, String openId) {
        int count;
        if (PlateCheckUtils.isNoPlate(plate)) {
            count = this.count(Wrappers.<ParkingOrder>lambdaQuery()
                .eq(ParkingOrder::getCardId, cardId)
                .eq(ParkingOrder::getOpenId, openId)
                .eq(ParkingOrder::getParkingStatus, 4)
            );
        } else {
            count = this.count(Wrappers.<ParkingOrder>lambdaQuery()
                .eq(ParkingOrder::getCardId, cardId)
                .eq(ParkingOrder::getPlate, plate)
                .eq(ParkingOrder::getParkingStatus, 4)
            );
        }
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importParkingOrder(MultipartFile file, Long parkLotId) throws Exception {
        Parklot parklot = parklotService.getOne(Wrappers.<Parklot>lambdaQuery().eq(Parklot::getId, parkLotId).last("limit 1"));
        LecentAssert.notNull(parklot, "选择的车场不存在");

        ExcelUtils.importExcel(file, ParkingPresentOrderExcel.class, 2, new ImportHandler<ParkingPresentOrderExcel>() {
            @Override
            public void saveCallBack(List<ParkingPresentOrderExcel> cacheList) {
                List<ParkingOrder> parkingOrderList = Func.copy(cacheList, ParkingOrder.class);
                parkingOrderList.forEach(p -> {
                    p.setParklotId(parklot.getId());
                    p.setBillingStartTime(p.getEnterTime());
                    p.setParkingStatus(2);
                });
                saveBatch(parkingOrderList);
            }

            @Override
            public ParkingPresentOrderExcel rowCallBack(int rowNum, Row row, ParkingPresentOrderExcel vo) {
                LecentAssert.isTrue(PlateCheckUtils.isPlate(vo.getPlate().trim()), String.format("第%s行车牌[%s]不正确!", rowNum, vo.getPlate()));
                return vo;
            }

            @Override
            public Object columnCallBack(int rowNum, int columnNum, Object cellValue) {
                LecentAssert.isFalse(F.isEmpty(cellValue), String.format("第%s行第%s列数据不能为空", rowNum, columnNum));
                return cellValue;
            }

            @Override
            public void errorCallBack(int rowNum, int columnNum, Object cellValue, String error) {
                throw new ServiceException(error);
            }

        });

        return true;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        ParkingPresentOrderExcel build = ParkingPresentOrderExcel.builder()
            .plate("贵RR1024")
            .enterTime(new Date())
            .remark("临停车")
            .build();
        List<ParkingPresentOrderExcel> dataList = new ArrayList<>();
        dataList.add(build);
        ExcelUtils.exportExcel(ParkingPresentOrderExcel.class, dataList, "在场车辆导入模板", response);

    }

    @Override
    public List<Long> getIds(Long parklotId) {
        return baseMapper.getIds(parklotId);
    }


    @Override
    public List<ParkingDetail> userUnpaidOrder(String plate, Long parklotId) {
        List<BUserPlateVO> userPlateList = userPlateService.listPlateByUserId();
        List<String> authPlates = userPlateList.stream().map(BUserPlate::getPlate).collect(Collectors.toList());
        if (Func.isNotBlank(plate) && !authPlates.contains(plate)) {
            authPlates.add(plate);
        }
        if (Func.isEmpty(authPlates)) {
            return new ArrayList<>();
        }
        return userUnpaidOrder(authPlates, userPlateList, parklotId);
    }

    private List<ParkingDetail> userUnpaidOrder(List<String> plateList, List<BUserPlateVO> userPlateList, Long parklotId) {
        List<ParkingDetail> list = baseMapper.unpaidParkingOrderByParklot(plateList, parklotId);
        list.forEach(item -> userPlateList.forEach(e -> {
            if (Func.equals(item.getPlate(), e.getPlate()) && Func.equals(e.getIsAuth(), true)) {
                item.setIsAuth(true);
            }
        }));
        return list;
    }

    @Override
    public List<ParkingDetail> userUnpaidOrder(String plate, String merchantId) {
        if (PlateValidator.isNoPlate(plate)) {
            return new ArrayList<>();
        }
        return baseMapper.unpaidParkingOrderByMerchantId(plate, merchantId);
    }

    @Override
    public IPage<ParkingDetail> userParkingOrder(Integer queryType, String plate, IPage<ParkingDetail> page) {
        List<BUserPlateVO> userPlateList = new ArrayList<>();
        List<String> userPlates = new ArrayList<>();
        if (Func.isNotBlank(plate)) {
            userPlates.add(plate);
        } else {
            userPlateList = userPlateService.listPlateByUserId();
            userPlates = userPlateList.stream().map(BUserPlate::getPlate).collect(Collectors.toList());
        }

        if (Func.isEmpty(userPlates)) {
            return page;
        }

        List<ParkingDetail> list = new ArrayList<>();
        if (QueryType.TO_PAID.equals(queryType)) {
            list = toPaidList(page, userPlates);
        } else if (QueryType.PAID.equals(queryType)) {
            list = paidList(page, userPlates);
        } else if (QueryType.UNPAID.equals(queryType)) {
            list = unpaidList(page, userPlates);
        }

        Date now = DateUtil.now();
        Map<String, Boolean> userPlateMap = userPlateList.stream()
            .collect(Collectors.toMap(BUserPlate::getPlate, BUserPlate::getIsAuth));

        list.forEach(pd -> {
            pd.setDeviceComboList(todoService.deviceCombo(pd.getPlaceId()));
            todoService.addMerchantField(pd);
            pd.setIsAuth(userPlateMap.getOrDefault(pd.getPlate(), false));
            pd.setDuration(DateUtils.getDurationMinute(pd.getEnterTime(),
                Optional.ofNullable(pd.getExitTime()).orElse(now)));
            pd.setPayType(PayWay.getNameByKey(Func.toInt(pd.getPayType())));
        });

        return page.setRecords(list);
    }

    @Override
    public Integer countUserUnpaidParkingOrder() {
        List<BUserPlateVO> userPlateList = userPlateService.listPlateByUserId();
        if (Func.isEmpty(userPlateList)) {
            return 0;
        }
        List<String> userPlates = userPlateList.stream().map(BUserPlate::getPlate).collect(Collectors.toList());
        return baseMapper.countUnpaidParkingOrder(userPlates);
    }

    @Override
    public BigDecimal selectUnpaidAmountByPlate(String plate, Long parklotId) {
        LecentAssert.notNull(plate, "车牌号不能为空");
        return baseMapper.selectUnpaidAmountByPlate(plate, parklotId);
    }

    private List<ParkingDetail> paidList(IPage<ParkingDetail> page, List<String> userPlates) {
        List<ParkingDetail> list = baseMapper.paidParkingOrder(page, userPlates);
        //已缴
        list.forEach(l -> l.setOrderType(2));
        return list;
    }

    private List<ParkingDetail> unpaidList(IPage<ParkingDetail> page, List<String> userPlates) {
        List<ParkingDetail> list = baseMapper.unpaidParkingOrder(page, userPlates);

        // 欠费订单
        list.forEach(l -> l.setOrderType(1));
        return Func.isNotEmpty(list) ? list : new ArrayList<>();
    }

    private List<ParkingDetail> toPaidList(IPage<ParkingDetail> page, List<String> userPlates) {
        List<ParkingDetail> list = unpaidList(page, userPlates);

        List<ParkingDetail> presentOrder = presentOrder(page, userPlates);
        if (Func.isNotEmpty(presentOrder)) {
            // 待缴订单
            presentOrder.forEach(l -> l.setOrderType(3));
            list.addAll(0, presentOrder);
        }
        return list;
    }

    private List<ParkingDetail> presentOrder(IPage<ParkingDetail> page, List<String> userPlates) {
        List<ParkingDetail> list = baseMapper.presentOrder(page, userPlates);

        for (ParkingDetail park : list) {
            park.setIsPaid(false);
            ChannelTodo todo = todoParkingDetail(park);
            if (todo != null) {
                park.setTodoId(todo.getId());
                park.setDuration(todo.getDuration());
                park.setTotalAmount(todo.getTotalAmount());
                park.setDiscountAmount(todo.getDiscountAmount());
                park.setReceiveAmount(todo.getReceiveAmount());
                park.setIsPaid(false);
            }
        }
        return list;
    }

    /**
     * 创建支付待办
     *
     * @param parking 停车记录
     * @return {@link ChannelTodo}
     */
    @Override
    public ChannelTodo createPayTodo(ParkingOrder parking) {
        log.info("createPayTodo start parking={}", Func.toJson(parking));
        RoadSideParkingDTO build = RoadSideParkingDTO.builder().placeId(parking.getPlaceId()).build();
        ChannelTodo channelTodo = buildRoadSideTodo(build);
        channelTodo.setPlaceId(parking.getPlaceId());
        channelTodo.setPlate(parking.getPlate());
        channelTodo.setParklotId(parking.getParklotId());
        channelTodo.setTriggerType(ChannelWay.WAY_2.getValue());
        ParkTodoContext context = ParkTodoContext.builder(new ParkChannelMessageEvent(channelTodo));
        parkAccessClient.createPayTodo(context);
        log.info("createPayTodo end context={}", Func.toJson(context));
        return context.getChannelTodo();
    }


    /**
     * 计算在场车辆的费用
     *
     * @param park 停车详情
     * @return ChannelTodo
     */
    @Override
    public ChannelTodo todoParkingDetail(ParkingDetail park) {
        log.info("todoParkingDetail start park={}", Func.toJson(park));
        RoadSideParkingDTO build = RoadSideParkingDTO.builder().placeId(park.getPlaceId()).build();
        ChannelTodo channelTodo = buildRoadSideTodo(build);
        channelTodo.setPlaceId(park.getPlaceId());
        channelTodo.setPlate(park.getPlaceCode());
        channelTodo.setParklotId(park.getParklotId());
        channelTodo.setTriggerType(ChannelWay.WAY_2.getValue());
        ParkTodoContext context = ParkTodoContext.builder(new ParkChannelMessageEvent(channelTodo));
        parkAccessClient.getParkingCost(context);
        log.info("todoParkingDetail  end  context={}", Func.toJson(context));
        return context.getChannelTodo();
    }

    private ChannelTodo buildRoadSideTodo(RoadSideParkingDTO req) {
        ChannelTodo todo = new ChannelTodo();
        Date date = req.getDate() != null ? req.getDate() : new Date();
        todo.setPlate(req.getPlate());
        todo.setStatus(ChannelConstant.TODO_STATUS_AUTO_PASS);
        todo.setImageUrl(req.getImageUrl());
        todo.setEnterImageUrl(req.getImageUrl());
        todo.setTriggerType(req.getTriggerType());
        todo.setChargeType(ChargeEnum.TEMP_STOP.name());
        todo.setDate(date);
        todo.setEnterTime(date);
        todo.setOccupied(false);
        todo.setIsMqOpenGate(false);
        return todo;
    }

    @Override
    public IPage<Map<String, Object>> getParkingList(IPage<Map<String, Object>> page, ParkingOrderDTO parkingOrderDTO) {
        if (Func.isEmpty(parkingOrderDTO.getParkLotIds())) {
            List<ParklotVO> parkLotList = parklotService.queryParkLotList(new CscCommonQueryParam());
            if (Func.isEmpty(parkLotList)) {
                return null;
            }
            parkingOrderDTO.setParkLotIds(parkLotList.stream().map(Parklot::getId).collect(Collectors.toList()));
        }
        List<Map<String, Object>> recordList = this.baseMapper.getParkingList(page, parkingOrderDTO);
        page.setRecords(recordList);
        return page;
    }

    @Override
    public boolean putPlate(ParkingOrder parking) {
        return this.baseMapper.putPlate(parking);
    }

    @Override
    public ParkingOrderVO getOrderByParkingId(Long parkingId) {
        ParkingOrder parkingOrder = this.getParkingById(parkingId);
        ParkingOrderVO parkingOrderVO = new ParkingOrderVO();
        if (null != parkingOrder) {
            BeanUtil.copyProperties(parkingOrder, parkingOrderVO);
            // 根据停车订单ID查找图片列表
            Map<Integer, List<ParkingImageDO>> parkingImageMap = this.parkingImageService.listGroupByParkingStatus(parkingOrder.getId());
            parkingOrderVO.setImageUrlMap(parkingImageMap);
        }

        return parkingOrderVO;
    }

    @Override
    public IPage<ParkingDetail> queryHavePlateAndParking(IPage<ParkingDetail> page, ParkingOrderDTO parking) {
        parking.setHavePlate(true);
        parking.setParkingStatus(ParkingStatusEnum.PARK_IN.getValue());
        List<ParkingDetail> list = queryParkingOrder(page, parking);

        for (ParkingDetail parkingDetail : list) {
            setParkingFeeInfo(parkingDetail);
        }

        return page.setRecords(list);
    }

    @Override
    public IPage<ParkingDetail> queryNoPlateAndParking(IPage<ParkingDetail> page, ParkingOrderDTO parking) {
        parking.setHavePlate(false);
        parking.setParkingStatus(ParkingStatusEnum.PARK_IN.getValue());
        return page.setRecords(queryParkingOrder(page, parking));
    }

    @Lazy
    @Resource
    private IParklotDeviceRetService parklotDeviceRetService;

    @Override
    public ParkingDetail queryParkingDetailNew(ParkingOrderDTO parking) {
        // 如果车场id、车位id和停车记录id为空，则车牌不能为无牌车
        if (Func.isEmpty(parking.getParkLotIds()) && Func.isNull(parking.getPlaceId()) && Func.isNull(parking.getId())) {
            parking.setHavePlate(true);
        }

        List<ParkingDetail> list = baseMapper.queryParkingOrderNew(new Page<ParkingDetail>().setSearchCount(false), parking);
        if (Func.isNotEmpty(list)) {
            ParkingDetail parkingDetail = list.get(0);

            if (parkingDetail.getParkingStatus() == ParkingStatusEnum.PARK_IN.getValue()) {
                // 设置计费信息
                setParkingFeeInfo(parkingDetail);
            }

            // 借用merchantName返回收费员工号
            parkingDetail.setMerchantName(AuthUtil.getUserAccount());

            // 设置小程序二维码
            if (parkingDetail.getParkingId() != null && !Boolean.FALSE.equals(parking.getNeedPayCode())) {
                parkingDetail.setQrCodeUrl(getPayQrCodeByParkingOrderId(parkingDetail.getParkingId()).getUrl());
            }

            // 设置车辆属性
            parkingDetail.setPlateProperty(getPlateProperty(parkingDetail));

            //车辆信息
            if (parkingDetail.getVehicleId() != null) {
                Vehicle vehicle = vehicleDomainService.getById(parkingDetail.getVehicleId());
                if (null != vehicle) {
                    parkingDetail.setVehicleDO(VehicleConverter.toDO(vehicle));
                }
            }

            // 添加手机号
            parkingDetail.setPhone(vehiclePhoneRelationService.getLastPhoneByVehicleId(parkingDetail.getVehicleId()));

            // 添加图片
            addParkingImages(parkingDetail);

            //设备信息
            if (parkingDetail.getPlaceId() != null) {
                List<ParklotDeviceRetVO> deviceRets = parklotDeviceRetService.findParklotDeviceInfoByPlaceId(parkingDetail.getPlaceId());
                if (Func.isNotEmpty(deviceRets)) {
                    Map<Integer, ParklotDeviceRet> deviceMap = new HashMap<>(deviceRets.size() * 2);
                    deviceRets.forEach(device -> {
                        ParklotDeviceRetVO parklotDeviceRet = new ParklotDeviceRetVO();
                        parklotDeviceRet.setDeviceSn(device.getDeviceSn());
                        parklotDeviceRet.setDeviceName(device.getDeviceName());
                        parklotDeviceRet.setDeviceId(device.getDeviceId());
                        parklotDeviceRet.setIp(device.getIp());
                        parklotDeviceRet.setMac(device.getMac());
                        parklotDeviceRet.setLng(device.getLng());
                        parklotDeviceRet.setLat(device.getLat());
                        parklotDeviceRet.setRegionCode(device.getRegionCode());
                        parklotDeviceRet.setRegionName(device.getRegionName());
                        parklotDeviceRet.setAddress(device.getAddress());
                        deviceMap.put(device.getDeviceType(), parklotDeviceRet);
                    });
                    parkingDetail.setDeviceMap(deviceMap);
                }
                // 查询打印小票数量
                List<ReceiptPrintLogVO> printLogVOS = receiptPrintLogService.selectReceiptPrintLogByParkingId(parkingDetail.getParkingId());
                parkingDetail.setReceiptPrintLogNum(printLogVOS.size());
            }

            parkingDetail.setCarType(CarType.getName(Func.toInt(parkingDetail.getCarType())));

            if (Func.isBlank(parkingDetail.getDuration())) {
                parkingDetail.setDuration(DateUtils.getDuration(parkingDetail.getEnterTime(), new Date()));
            }
            parkingDetail.setHistoryUnpaidAmount(BigDecimal.ZERO);
            if (PlateValidator.isPlate(parkingDetail.getPlate())) {
                parkingDetail.setHistoryUnpaidAmount(unpaidOrderService.countUnpaidOrderAmount(parkingDetail.getPlate()));
            }
            return parkingDetail;
        }
        return null;
    }

    /**
     * 添加停车图片
     *
     * @param parkingDetail 停车记录
     */
    private void addParkingImages(ParkingDetail parkingDetail) {
        try {
            // 根据停车订单ID查找图片列表，并根据图片类型分组
            Map<Integer, List<ParkingImageDO>> parkingImageMap =
                this.parkingImageService.listGroupByImageType(parkingDetail.getParkingId());
            if (Func.isEmpty(parkingImageMap)) {
                return;
            }
            parkingDetail.setPlateImage(Func.join(parkingImageMap.get(ParkingImageType.PLATE.getValue())));
            parkingDetail.setSceneImage(Func.join(parkingImageMap.get(ParkingImageType.SCENE.getValue())));
        } catch (Exception e) {
            log.error("[{}]未查询到停车图片", parkingDetail.getPlate());
        }
    }

    @Override
    public ParkingDetail queryParkingDetail(ParkingOrderDTO parking) {
        List<ParkingDetail> list = baseMapper.queryParkingOrder(new Page<>(), parking);
        if (Func.isNotEmpty(list)) {
            ParkingDetail parkingDetail = list.get(0);

            if (parkingDetail.getParkingStatus() == ParkingStatusEnum.PARK_IN.getValue()) {
                // 设置计费信息
                setParkingFeeInfo(parkingDetail);
            }

            // 借用merchantName返回收费员工号
            parkingDetail.setMerchantName(AuthUtil.getUserAccount());

            // 设置小程序二维码
            if (parkingDetail.getParkingId() != null) {
                parkingDetail.setQrCodeUrl(getPayQrCodeByParkingOrderId(parkingDetail.getParkingId()).getUrl());
            }

            // 设置车辆属性
            parkingDetail.setPlateProperty(getPlateProperty(parkingDetail));
            //车辆信息
            if (parkingDetail.getVehicleId() != null) {
                Vehicle vehicle = vehicleDomainService.getById(parkingDetail.getVehicleId());
                if (null != vehicle) {
                    parkingDetail.setVehicleDO(VehicleConverter.toDO(vehicle));
                    parkingDetail.setPlateColor(vehicle.getPlateColor().code());
                }
            }
            return parkingDetail;
        }
        return null;
    }

    @Override
    public List<ParkingDetail> queryParkingOrder(IPage<ParkingDetail> page, ParkingOrderDTO parking) {
        // 获取授权车场id
        List<Long> parkLotIds = parklotService.getCurrentUserBindParkLotIds(parking.getParklotId());
        parking.setParkLotIds(Func.isNotEmpty(parkLotIds) ? parkLotIds : Collections.singletonList(-1L));

        return baseMapper.queryParkingOrder(page, parking);
    }

    @Override
    public List<ParkingDetail> customQueryParkingOrder(IPage<ParkingDetail> page, ParkingOrderDTO parking) {
        // 获取授权车场id
        List<Long> parkLotIds = parklotService.getCurrentUserBindParkLotIds(parking.getParklotId());
        parking.setParkLotIds(Func.isNotEmpty(parkLotIds) ? parkLotIds : Collections.singletonList(-1L));
        Page<ParkingDetail> p = (Page<ParkingDetail>) page;
        p.setSearchCount(false);
        p.setTotal(baseMapper.countQueryParkingOrderNew(parking));
        List<ParkingDetail> list = baseMapper.queryParkingOrderNew(p, parking);
        for (ParkingDetail parkingDetail : list) {
            // 设置车辆属性
            parkingDetail.setPlateProperty(getPlateProperty(parkingDetail));
            if (ParkingStatusEnum.PARK_IN.getValue() == parkingDetail.getParkingStatus()) {
                parkingDetail.setDuration(DateUtils.getDuration(parkingDetail.getEnterTime(), new Date()));
            }
            // 添加手机号
            parkingDetail.setPhone(vehiclePhoneRelationService.getLastPhoneByVehicleId(parkingDetail.getVehicleId()));
            // 添加图片
            addParkingImages(parkingDetail);
        }
        return list;
    }

    /**
     * 获取车辆属性
     *
     * @param parkingDetail 停车场细节
     * @return {@link PlateProperty}
     */
    private PlateProperty getPlateProperty(ParkingDetail parkingDetail) {
        List<PlateProperty> properties = platePropertyService.queryPlateProperties(parkingDetail.getParklotId(),
            parkingDetail.getPlate(), parkingDetail.getEnterTime(), Optional.ofNullable(parkingDetail.getExitTime()).orElse(new Date()));
        return properties.stream().max(Comparator.comparing(PlateProperty::getEndTime)).orElse(null);
    }

    /**
     * 设置停车费用信息
     *
     * @param parkingDetail 停车细节
     */
    private void setParkingFeeInfo(ParkingDetail parkingDetail) {
        ChannelTodo todo = todoParkingDetail(parkingDetail);
        if (todo != null) {
            parkingDetail.setDuration(todo.getDuration());
            parkingDetail.setTotalAmount(todo.getTotalAmount());
            parkingDetail.setDiscountAmount(todo.getDiscountAmount());
            parkingDetail.setReceiveAmount(todo.getReceiveAmount());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ParkingDetail> signInParking(ParkingOrderEvent parking) {
        LecentAssert.notBlank(parking.getPlate(), "车牌不能为空");

        if (PlateValidator.isPlate(parking.getPlate())) {
            LecentAssert.notNull(parking.getParklotId(), "车场id不能为空");
            // 检查重复入场
            R<ParkingDetail> repeatResult = checkRepeatEntry(parking);
            if (repeatResult != null) {
                return repeatResult;
            }
            // 检查进场时间冲突
            String timeConflict = checkEnterTimeConflict(parking);
            if (timeConflict != null) {
                return R.fail(timeConflict);
            }
        }

        VehicleDTO vehicleDTO = buildVehicleDTO(parking);
        Map<Integer, List<String>> imageUrlMap = buildImageUrlMap(parking);

        ParkingOrder parkingOrder = getById(parking.getId());
        if (parkingOrder != null) {
            // todo 待优化
            // 订单信息不可修改
            throw new ServiceException("订单信息不可修改，请先登记离场再重新录入车牌。");
//            handleManualCompensation(parking, parkingOrder, vehicleDTO, imageUrlMap);
        } else {
            createNewParkingRecord(parking, vehicleDTO, imageUrlMap);
        }

        handleVehiclePhoneRelation(parking);

        return R.status(true);
    }

    private R<ParkingDetail> checkRepeatEntry(ParkingOrderEvent parking) {
        if (parking.getParkingStatus() != null && parking.getParkingStatus() == ParkingStatusEnum.PARK_REPEAT.getValue()) {
            return null;
        }

        ParkingOrderDTO query = createParkingQueryDTO(
            parking.getParklotId(),
            parking.getPlate(),
            ParkingStatusEnum.PARK_IN.getValue(),
            null,
            null
        );
        ParkingDetail existing = queryParkingDetailV2(query);

        if (existing != null && !existing.getParkingId().equals(parking.getId())) {
            return R.data(CustomResultCode.REPEAT_ENTER_ERROR.getCode(), existing, CustomResultCode.REPEAT_ENTER_ERROR.getMessage());
        }
        return null;
    }

    private String checkEnterTimeConflict(ParkingOrderEvent parking) {
        // 先按车场和车牌查询
        String conflictMessage = checkSamePlateEnterTimeConflict(parking);
        if (conflictMessage != null) {
            return conflictMessage;
        }

        // 再按车位ID查询
        return checkSamePlaceEnterTimeConflict(parking);
    }

    @Override
    public String checkSamePlateEnterTimeConflict(ParkingOrderEvent parking) {
        List<ParkingDetail> parkingDetails = baseMapper.getParkingByPlateAndEnterTime(
            parking.getParklotId(),
            parking.getPlate(),
            parking.getPlaceId(),
            parking.getEnterTime());
        if (Func.isNotEmpty(parkingDetails)) {
            return buildConflictMessage(parkingDetails.get(0));
        }
        return null;
    }

    private String checkSamePlaceEnterTimeConflict(ParkingOrderEvent parking) {
        List<ParkingDetail> parkingDetails = baseMapper.getParkingByPlaceIdAndEnterTime(
            parking.getParklotId(),
            parking.getPlaceId(),
            parking.getEnterTime());
        if (Func.isNotEmpty(parkingDetails)) {
            return buildConflictMessage(parkingDetails.get(0));
        }
        return null;
    }

    @Override
    public List<ParkingDetail> getParkingByPlaceIdAndEnterTime(ParkingOrderEvent event) {
        return baseMapper.getParkingByPlaceIdAndEnterTime(
            event.getParklotId(),
            event.getPlaceId(),
            event.getEnterTime()
        );
    }

	private String buildConflictMessage(ParkingDetail parking) {
		String pattern = "yyyy年M月d日 HH:mm";
		String enterTimeStr = DateUtil.format(parking.getEnterTime(), pattern);
		Date exitTime = Optional.ofNullable(parking.getExitTime()).orElseGet(Date::new);
		// 判断是否跨天
		String exitPattern = org.apache.commons.lang3.time.DateUtils.isSameDay(parking.getEnterTime(), exitTime) ?
			"HH:mm" : pattern;
		String exitTimeStr = DateUtil.format(exitTime, exitPattern);
		return String.format("进场时间冲突，%s-%s 时段已存在有效订单，请选择 %s 以后的秒图", enterTimeStr, exitTimeStr, exitTimeStr);
	}

    private ParkingOrderDTO createParkingQueryDTO(Long parklotId, String plate, Integer status, Date start, Date end) {
        ParkingOrderDTO dto = new ParkingOrderDTO();
        dto.setParkLotIds(Collections.singletonList(parklotId));
        dto.setPlate(plate);
        dto.setParkingStatus(status);
        dto.setHavePlate(true);
        dto.setExitStartTime(start);
        dto.setExitEndTime(end);
        return dto;
    }

    private VehicleDTO buildVehicleDTO(ParkingOrderEvent parking) {
        return VehicleDTO.builder()
            .plate(parking.getPlate())
            .plateColorCode(parking.getPlateColor())
            .type(parking.getVehicleType())
            .build();
    }

    private Map<Integer, List<String>> buildImageUrlMap(ParkingOrderEvent parking) {
        Map<Integer, List<String>> map = new HashMap<>();
        Optional.ofNullable(parking.getPlateImageUrl()).ifPresent(url ->
            map.put(ParkingImageType.PLATE.getValue(), Collections.singletonList(url)));
        Optional.ofNullable(parking.getSceneImageUrl()).ifPresent(url ->
            map.put(ParkingImageType.SCENE.getValue(), Collections.singletonList(url)));
        return map;
    }

    private void handleManualCompensation(ParkingOrderEvent event, ParkingOrder order, VehicleDTO vehicle, Map<Integer, List<String>> images) {
        roadsideParkingAppService.manualCompensatoryRegister(
            new ManualCompensatoryRegisterCommand(
                order.getId(),
                ParkingEventInfoDTO.builder()
                    .datetime(event.getEnterTime())
                    .imageUrl(event.getEnterImageUrl())
                    .imagesMap(images)
                    .vehicle(vehicle)
                    .build()
            )
        );
    }

    private void createNewParkingRecord(ParkingOrderEvent event, VehicleDTO vehicle, Map<Integer, List<String>> images) {
        LecentAssert.notNull(event.getPlaceId(), "车位id不能为空");
        LecentAssert.notNull(event.getEnterTime(), "入场时间不能为空");

        RoadSideParkingDTO dto = RoadSideParkingDTO.builder()
            .placeId(event.getPlaceId())
            .plate(event.getPlate())
            .imageUrl(event.getEnterImageUrl())
            .imageUrlMap(images)
            .date(event.getEnterTime())
            .triggerType(ChannelWay.WAY_8.getValue())
            .parkingStatus(ParkingStatusEnum.PARK_IN.getValue())
            .vehicleDTO(vehicle)
            .build();

        ChannelTodoVO result = roadSideParkingBiz.roadSideDeviceTrigger(dto);
        if (result == null) {
            throw new ServiceException("停车记录创建失败");
        }
    }

    private void handleVehiclePhoneRelation(ParkingOrderEvent event) {
        if (Func.isEmpty(event.getPhone())) {
            return;
        }
        BVehiclePhoneRelation relation = new BVehiclePhoneRelation();
        relation.setSource(PlatePhoneSourceEnum.APP_INPUT.getValue());
        relation.setPhone(event.getPhone());
        relation.setPlate(event.getPlate());
        relation.setTenantId(event.getTenantId());

        vehiclePhoneRelationService.appAddOrUpdate(relation);
    }

    @Override
    public Boolean outParking(ParkingOrder parking) {

        ParkingPlace place = parkingPlaceService.getById(parking.getPlaceId());
        LecentAssert.notNull(place, "未查询到车位信息");

        ParkingOrder order = queryPresentRecord(place.getParklotId(), parking.getPlate());
        LecentAssert.notNull(order, "车辆已离场");

        if (!PlateValidator.isNoPlate(parking.getPlate()) && parking.getPlate().length() < 7) {
            throw new ServiceException("车牌格式错误");
        }

        VehicleDTO vehicleDTO = VehicleDTO.builder()
            .plate(parking.getPlate())
            .plateColorCode(parking.getPlateColor())
            .type(parking.getVehicleType())
            .build();

        RoadSideParkingDTO sideReq = RoadSideParkingDTO.builder()
            .placeId(parking.getPlaceId())
            .plate(parking.getPlate())
            .imageUrl(parking.getExitImageUrl())
            .date(parking.getExitTime())
            .triggerType(ChannelWay.WAY_8.getValue())
            .parkingStatus(ParkingStatusEnum.PARK_OUT.getValue())
            .vehicleDTO(vehicleDTO)
            .build();
        roadSideParkingBiz.roadSideDeviceTrigger(sideReq);
        AuditLogger.add(String.valueOf(order.getId()), "manualRegistration", "人工登记出场",
            "在场", "出场", null, "人工登记出场");
        return true;
    }

    @Override
    public ParkingOrder queryLastParkingOrderByPlace(Long id) {
        return lambdaQuery().eq(ParkingOrder::getPlaceId, id).orderByDesc(ParkingOrder::getId).last("limit 1").one();
    }

    @Override
    @TenantIgnore
    public Boolean handlePlateColorUnusualOrder() {
        List<ParkingOrder> list = baseMapper.getPlateColorUnusualOrder();
        log.info("handlePlateColorUnusualOrder list:{}", Func.toJson(list));
        StringBuilder errorInfo = new StringBuilder();
        for (ParkingOrder order : list) {
            ChannelTodo todo = todoService.getById(order.getExitTodoId());
            if (todo == null) {
                errorInfo.append(order.getId()).append(",");
                continue;
            }
            todo.setErrorAmount(todo.getReceiveAmount());
            todo.setErrorAmountType(2);
            todo.setReasonId((long) UnpaidOrderType.TYPE100.getValue());
            createUnpaidOrder(todo, order);
            todo.setStatus(1);
            // 更新代办
            todoService.updateById(todo);
            order.setReasonIds(String.valueOf(UnpaidOrderType.TYPE100.getValue()));
            order.setMemo("已处理【同车牌，颜色不一致】问题");
            updateById(order);
        }
        LecentAssert.isBlank(errorInfo.toString(), "停车记录:" + errorInfo.toString() + "更新失败");
        return true;
    }


    /**
     * 获取修改后的费用
     *
     * @param parking
     * @return
     */
    @Override
    public ParkingDetailDTO getUpdateParkingOrderAmount(ParkingOrderDTO parking) {
        if (parking.getId() == null) {
            throw new ServiceException("停车记录不存在");
        }
        ParkingOrder parkingOrder = this.getById(parking.getId());
        if (parkingOrder == null) {
            throw new ServiceException("停车记录不存在");
        }
        if (parking.getEnterTime() != null && parking.getEnterTime().compareTo(parkingOrder.getEnterTime()) != 0) {
            //修改了入场时间
            parkingOrder.setEnterTime(parking.getEnterTime());
            parkingOrder.setBillingStartTime(parking.getEnterTime());
        }
        if (parking.getExitTime() != null && parking.getExitTime().compareTo(parkingOrder.getExitTime()) != 0) {
            parkingOrder.setExitTime(parking.getExitTime());
        }
        if (Func.isNotBlank(parking.getPlate())) {

            /**
             * 车牌有效校验
             */
            if (!PlateCheckUtils.isNoPlate(parking.getPlate()) && !PlateCheckUtils.isPlate(parking.getPlate())) {
                throw new ServiceException("请输入正确车牌");
            }
            if (!parking.getPlate().equals(parkingOrder.getPlate())) {
                parkingOrder.setPlate(parking.getPlate());
            }
        }
        if (parkingOrder.getEnterTime() == null || parkingOrder.getExitTime() == null) {
            throw new ServiceException("入场时间和出场时间都不能为空");
        }
        ChannelTodo resultTodo = roadSideParkingBiz.todoParkingDetail(parkingOrder);

        // 检查修改后的金额是否小于已支付金额
        if (resultTodo.getTotalAmount().compareTo(parkingOrder.getPaidAmount()) < 0) {
            throw new ServiceException("该订单已经支付，请全额退款后再进行操作");
        }

        return Func.copy(resultTodo, ParkingDetailDTO.class);
    }


    /**
     * 修改停车信息
     *
     * @param parking
     * @return
     */
    @Override
    @Transactional
    public boolean updateParkingOrder(ParkingOrderDTO parking) {
        ParkingOrder parkingOrder = verifyOrder(parking);
        Long abnormalId = null;
        BladeUser user = AuthUtil.getUser();
        ParkingOrderAbnormal parkingOrderAbnormal = parkingOrderAbnormalService.findParkingOrderAbnormal(parkingOrder.getId());
        if (parkingOrderAbnormal != null) {
            abnormalId = parkingOrderAbnormal.getId();
            if (parkingOrderAbnormal.getStatus() != 0) {
                long time = new Date().getTime() - parkingOrderAbnormal.getCreateTime().getTime();
                if (time < (60 * 1000)) {
                    throw new ServiceException("该记录已处理请刷新页面！");
                }
                if (!user.getUserId().equals(parkingOrderAbnormal.getHandlerPersonId())) {
                    throw new ServiceException("该订单已被" + parkingOrderAbnormal.getHandlerPersonName() + "处理，只能" + parkingOrderAbnormal.getHandlerPersonName() + "本人可以处理！");
                }
            }
        }
        ParkingOrderDTO oldParking = new ParkingOrderDTO();
        oldParking.setPlate(parkingOrder.getPlate());
        oldParking.setEnterTime(parkingOrder.getEnterTime());
        oldParking.setEnterImageUrl(parkingOrder.getEnterImageUrl());
        oldParking.setExitTime(parkingOrder.getExitTime());
        oldParking.setExitImageUrl(parkingOrder.getExitImageUrl());
        boolean isUpdate = false;
        boolean isUpPlate = false;
        int delImage = 0;

        checkParkingOrderConflict(parking, parkingOrder);

		if (Func.isNotBlank(parking.getPlate())) {
			if (!parking.getPlate().equals(parkingOrder.getPlate())) {
				parkingOrder.setPlate(parking.getPlate());
				isUpPlate = true;
				isUpdate = true;
			}
			if (parkingOrder.getVehicleId() != null) {
				Vehicle vehicle = vehicleDomainService.getById(parkingOrder.getVehicleId());
				if (vehicle != null) {
					if (vehicle.getPlateColor() != null) {
						oldParking.setPlateColor(vehicle.getPlateColor().code());
					}
					oldParking.setVehicleType(vehicle.getType());
				}
			}
			boolean isUpPlateColor = false;
			if (Func.isNotBlank(parking.getPlateColor()) && !parking.getPlateColor().equals(oldParking.getPlateColor())) {
				isUpdate = true;
				isUpPlateColor = true;
			}
			/**
			 * 车牌颜色或车牌变化需要更新车辆属性表
			 */
			if (isUpPlate || isUpPlateColor) {
				VehicleDTO vehicleDTO = VehicleDTO.builder()
					.plate(parking.getPlate())
					.plateColorCode(parking.getPlateColor())
					.type(parking.getVehicleType())
					.build();
				//更新车辆信息
				Vehicle vehicle = vehicleAssembler.fromDTO(vehicleDTO);
				parkingOrder.setVehicleId(vehicle.getId());
			}
		}

        if (parking.getEnterTime() != null && parking.getEnterTime().compareTo(parkingOrder.getEnterTime()) != 0) {
            //修改了入场时间
            isUpdate = true;
            parkingOrder.setEnterTime(parking.getEnterTime());
            parkingOrder.setBillingStartTime(parking.getEnterTime());
            //时间轴
            List<TimeNode> timeNodeList = this.getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
            TimeNode enterTimeNode = null;
            if (Func.isNotEmpty(timeNodeList)) {
                List<TimeNode> rmTimeNodeList = new ArrayList<>();
                for (TimeNode timeNode : timeNodeList) {
                    Date d = DateUtils.parseFormatByS(timeNode.getStartDate());
                    //删除入场时间大于修改后的入场时间节点
                    if (timeNode.getName().contains("进入")) {
                        enterTimeNode = timeNode;
                    } else if (d.compareTo(parking.getEnterTime()) < 0) {
                        rmTimeNodeList.add(timeNode);
                    }
                }
                timeNodeList.removeAll(rmTimeNodeList);
            } else {
                timeNodeList = new ArrayList<>();
            }
            if (enterTimeNode == null) {
                enterTimeNode = new TimeNode();
                enterTimeNode.setName("临停进入，开始计费");
            }
            enterTimeNode.setStartDate(DateUtils.format(parking.getEnterTime()));
            List<TimeNode> newtimeNodeList = new ArrayList<>();
            newtimeNodeList.add(enterTimeNode);
            newtimeNodeList.addAll(timeNodeList);
            parkingOrder.setParkingTimeNode(JSON.toJSONString(newtimeNodeList));

        }
        //场景图片处理
        ParkingImages parkingImages = null;
        if (Func.isNotBlank(parking.getEnterImageUrl()) && !parking.getEnterImageUrl().equals(parkingOrder.getEnterImageUrl())) {
            isUpdate = true;
            delImage = 1;
            String enterImageUrl = ImageUtils.uploadOss(parking.getEnterImageUrl(), "leliven-park-roadside");
            if (Func.isBlank(enterImageUrl)) {
                throw new ServiceException("进场图片保存失败");
            }

            parkingOrder.setEnterImageUrl(enterImageUrl);
			uploadOssCovertImagePkInfMark(parkingOrder,true);
            parkingOrder.setEnterWay(ChannelWay.WAY_7.getValue());
        }

        if (Func.isNotEmpty(parking.getEnterSceneImageUrls())) {
            isUpdate = true;
            delImage = 1;
            List<String> urls = new ArrayList<>();
            String url = null;
            for (String images : parking.getEnterSceneImageUrls()) {
                url = ImageUtils.uploadOss(images, "leliven-park-roadside");
                if (Func.isBlank(url)) {
                    throw new ServiceException("进场场景图片保存失败");
                }
                urls.add(url);
            }

            if (parkingImages == null) {
                parkingImages = new ParkingImages(parkingOrder.getId());
                parkingImages.setParklotId(parkingOrder.getParklotId());
            }

            ParkingImage parkingImage = new ParkingImage(null, ParkingImageType.SCENE, urls);
            parkingImages.getEnterImages().addImage(parkingImage);
        }

		if (parking.getExitTime() != null && parking.getExitTime().compareTo(parkingOrder.getExitTime()) != 0) {
			isUpdate = true;
			parkingOrder.setExitTime(parking.getExitTime());
			//时间轴
			List<TimeNode> timeNodeList = this.getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
			if (Func.isNotEmpty(timeNodeList)) {
				List<TimeNode> rmTimeNodeList = new ArrayList<>();
				for (TimeNode timeNode : timeNodeList) {
					Date d = DateUtils.parseFormatByS(timeNode.getStartDate());
					//删除大于出场时间的节点
					if (d.compareTo(parking.getExitTime()) > 0 || "车辆出场".equals(timeNode.getName())) {
						rmTimeNodeList.add(timeNode);
					}
				}
				timeNodeList.removeAll(rmTimeNodeList);
			}
			parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));
			// 删除未缴费订单
			unpaidOrderService.deleteUnpaidOrderByParkingId(parkingOrder.getId());
		}
		if (Func.isNotBlank(parking.getExitImageUrl()) && !parking.getExitImageUrl().equals(parkingOrder.getExitImageUrl())) {
            isUpdate = true;
            if (delImage == 1) {
                delImage = 3;
            } else {
                delImage = 2;
            }
            String exitImageUrl = ImageUtils.uploadOss(parking.getExitImageUrl(), "leliven-park-roadside");
            if (Func.isBlank(exitImageUrl)) {
                throw new ServiceException("出场图片保存失败");
            }
            parkingOrder.setExitImageUrl(exitImageUrl);
			uploadOssCovertImagePkInfMark(parkingOrder,false);
            parkingOrder.setExitWay(ChannelWay.WAY_7.getValue());
        }
        //场景图片处理
        if (Func.isNotEmpty(parking.getExitSceneImageUrls())) {
            isUpdate = true;
            if (delImage == 1 || delImage == 3) {
                delImage = 3;
            } else {
                delImage = 2;
            }
            List<String> urls = new ArrayList<>();
            String url = null;
            for (String images : parking.getExitSceneImageUrls()) {
                url = ImageUtils.uploadOss(images, "leliven-park-roadside");
                if (Func.isBlank(url)) {
                    throw new ServiceException("进场场景图片保存失败");
                }
                urls.add(url);
            }

            if (parkingImages == null) {
                parkingImages = new ParkingImages(parkingOrder.getId());
                parkingImages.setParklotId(parkingOrder.getParklotId());
            }
            ParkingImage parkingImage = new ParkingImage(null, ParkingImageType.SCENE, urls);
            parkingImages.getExitImages().addImage(parkingImage);
        }

        if (parking.getExitTime() != null && parking.getExitTime().compareTo(parkingOrder.getExitTime()) != 0) {
            isUpdate = true;
            parkingOrder.setExitTime(parking.getExitTime());
            //时间轴
            List<TimeNode> timeNodeList = this.getParkingTimeNodeList(parkingOrder.getParkingTimeNode());
            if (Func.isNotEmpty(timeNodeList)) {
                List<TimeNode> rmTimeNodeList = new ArrayList<>();
                for (TimeNode timeNode : timeNodeList) {
                    Date d = DateUtils.parseFormatByS(timeNode.getStartDate());
                    //删除大于出场时间的节点
                    if (d.compareTo(parking.getExitTime()) > 0 || "车辆出场".equals(timeNode.getName())) {
                        rmTimeNodeList.add(timeNode);
                    }
                }
                timeNodeList.removeAll(rmTimeNodeList);
            }
            parkingOrder.setParkingTimeNode(JSON.toJSONString(timeNodeList));
            // 删除未缴费订单
            unpaidOrderService.deleteUnpaidOrderByParkingId(parkingOrder.getId());
        }

        if (Func.isNotBlank(parking.getHandlerRemark())) {
            isUpdate = true;
        }

        if (isUpdate) {
            this.updateById(parkingOrder);
            this.saveParkingOrderAbnormal(parking, user, abnormalId, 1);
            //删除图片
            this.delImage(delImage, parkingOrder, parkingImages);
            //记录日志
            AuditLogger.add(String.valueOf(parkingOrder.getId()), "upParkingOrder", "异常订单修改",
                toLog(oldParking), toLog(parkingOrder), null, parking.getHandlerRemark());
            //需要重新计算费用
            updateParkingOrderByFee(parkingOrder);
            //删除区间内无效的停车记录
            delInvalidParkingOrder(parkingOrder);
        }
        return true;
    }

	// 进出场图片添加停车信息标记
	private void uploadOssCovertImagePkInfMark(ParkingOrder parkingOrder, boolean isEnter){
		try {
			Parklot parklot = ParklotCacheMapper.INSTANCE.getById(parkingOrder.getParklotId());
			ParkingPlace place = ParkingPlaceCacheMapper.INSTANCE.getById(parkingOrder.getPlaceId());
			String placeCode = null != place ? place.getPayCode() : null;
			boolean idUp = false;
			if(isEnter){
				String enterImageUrl = ImageUtils.uploadOssCovertImagePkInfMark(parkingOrder.getEnterImageUrl(),
					ParkingConstant.OSS_CODE_ROADSIDE,parklot.getName(),placeCode, parkingOrder.getPlate(), true, parkingOrder.getEnterTime());
				if(StringUtil.isNotBlank(enterImageUrl)){
					parkingOrder.setEnterImageUrl(enterImageUrl);
					idUp = true;
				}
			}else {
				String exitImgUrl = ImageUtils.uploadOssCovertImagePkInfMark(parkingOrder.getExitImageUrl(),
					ParkingConstant.OSS_CODE_ROADSIDE,parklot.getName(),placeCode, parkingOrder.getPlate(),false, parkingOrder.getExitTime());
				if(StringUtil.isNotBlank(exitImgUrl)){
					parkingOrder.setExitImageUrl(exitImgUrl);
					idUp = true;
				}
			}
			if(idUp){
			 	log.info("进出场图片标记停车信息,车场/路段={},车牌={}",parklot.getName(), parkingOrder.getPlate());
			}
		}catch (Exception e){
			log.error("进出场图片标记停车信息异常", e);
		}
	}

    private void checkParkingOrderConflict(ParkingOrderDTO parking, ParkingOrder parkingOrder) {
        if (parking.getEnterTime() == null || parking.getExitTime() == null) {
            return;
        }
        // 检查车牌时空交叉
        ParkingOrderDTO query = createParkingQueryDTO(
            parkingOrder.getParklotId(),
            parking.getPlate(),
            null,
            DateUtil.plusSeconds(parking.getEnterTime(), 1),
            parking.getExitTime()
        );
        query.setExcludeIds(Collections.singletonList(parkingOrder.getId()));
        ParkingDetail conflict = queryParkingDetailV2(query);
        if (conflict != null) {
            throw new ServiceException(String.format("%s在 %s-%s 时段已存在有效订单", parking.getPlate(),
                DateUtil.format(conflict.getEnterTime(), "yyyy年M月d日 HH:mm"),
                // 判断是否跨天
                org.apache.commons.lang3.time.DateUtils.isSameDay(conflict.getEnterTime(), conflict.getExitTime()) ?
                    DateUtil.format(conflict.getExitTime(), "HH:mm") :
                    DateUtil.format(conflict.getExitTime(), "yyyy年M月d日 HH:mm")));
        }

        // 检查车位时空交叉
        query = createParkingQueryDTO(
            parkingOrder.getParklotId(),
            null,
            null,
            parking.getEnterTime(),
            parking.getExitTime()
        );
        query.setPlaceId(parkingOrder.getPlaceId());
        query.setExcludeIds(Collections.singletonList(parkingOrder.getId()));
        conflict = queryParkingDetailV2(query);
        if (conflict != null) {
            throw new ServiceException(String.format("同车位在 %s-%s 时段已存在有效订单",
                DateUtil.format(conflict.getEnterTime(), "yyyy年M月d日 HH:mm"),
                // 判断是否跨天
                org.apache.commons.lang3.time.DateUtils.isSameDay(conflict.getEnterTime(), conflict.getExitTime()) ?
                    DateUtil.format(conflict.getExitTime(), "HH:mm") :
                    DateUtil.format(conflict.getExitTime(), "yyyy年M月d日 HH:mm")));
        }
    }

    private String toLog(ParkingOrder parking) {
        StringBuffer sb = new StringBuffer();
        sb.append("车牌号：" + parking.getPlate());
        sb.append(",车牌颜色:" + parking.getPlateColor());
        sb.append(",入场时间:" + DateUtils.format(parking.getEnterTime()));
        sb.append(",入场图片:" + parking.getEnterImageUrl());
        sb.append(",出场时间:" + DateUtils.format(parking.getExitTime()));
        sb.append(",出场图片:" + parking.getExitImageUrl());
        return sb.toString();
    }

    private ParkingOrder verifyOrder(ParkingOrderDTO parking) {
        if (parking.getAbnormalType() == null) {
            throw new ServiceException("差异类型必传");
        }
        if (parking.getId() == null) {
            throw new ServiceException("停车记录不存在");
        }
        ParkingOrder parkingOrder = this.getById(parking.getId());
        if (parkingOrder == null) {
            throw new ServiceException("停车记录不存在");
        }
        if (parkingOrder.getPlaceId() == null) {
            throw new ServiceException("没有车位信息不能修改");
        }
        parking.setPlaceId(parkingOrder.getPlaceId());
        parking.setParklotId(parkingOrder.getParklotId());
        if (Func.isBlank(parking.getPlate())) {
            parking.setPlate(parkingOrder.getPlate());
        }
        if (parking.getEnterTime() == null) {
            parking.setEnterTime(parkingOrder.getEnterTime());
        }
        if (parking.getExitTime() == null) {
            parking.setExitTime(parkingOrder.getExitTime());
        }
        Integer orderCount = baseMapper.checkParkingOrderEnterAndExitTime(parking);
        LecentAssert.isFalse(orderCount > 0, "该停车记录进出场时间覆盖到了已有的其他停车记录");
        parkingOrder.setMemo("异常订单修改");
        Channel channel = ParkLotCaches.getChannelByNo(parking.getParklotId(), 1);
        LecentAssert.notNull(channel, "通道不存在！");
        if (parkingOrder.getExitChannelId() == null) {
            parkingOrder.setExitChannelId(channel.getId());
        }
        return parkingOrder;
    }

    /**
     * 保存停车订单异常记录
     *
     * @param parking    停车订单DTO
     * @param user       当前用户
     * @param abnormalId 异常记录ID
     * @param status     状态
     */
    @Override
    public void saveParkingOrderAbnormal(ParkingOrderDTO parking, BladeUser user, Long abnormalId, Integer status) {
        ParkingOrderAbnormal parkingOrderAbnormal = new ParkingOrderAbnormal();
        parkingOrderAbnormal.setId(abnormalId);
        parkingOrderAbnormal.setParklotId(parking.getParklotId());
        parkingOrderAbnormal.setParkingId(parking.getId());
        parkingOrderAbnormal.setHandlerDate(new Date());
        parkingOrderAbnormal.setRemark(parking.getHandlerRemark());
        parkingOrderAbnormal.setAbnormalType(parking.getAbnormalType());
        parkingOrderAbnormal.setHandlerPersonId(Optional.ofNullable(user).map(BladeUser::getUserId).orElse(null));
        parkingOrderAbnormal.setHandlerPersonName(Optional.ofNullable(user).map(BladeUser::getUserName).orElse(null));
        parkingOrderAbnormal.setTenantId(parking.getTenantId());
        parkingOrderAbnormal.setStatus(status);
        this.parkingOrderAbnormalService.saveOrUpdate(parkingOrderAbnormal);
    }


    /**
     * 删除图片
     *
     * @param delImage      1 入场图片 2 出场图片 3 全部
     * @param parkingOrder  停车记录
     * @param parkingImages 停车图片
     */
    private void delImage(Integer delImage, ParkingOrder parkingOrder, ParkingImages parkingImages) {
        switch (delImage) {
            case 1:
                //删除入场图片
                parkingImageRepositoryI.deleteByParkingOrderId(parkingOrder.getId(), ParkingStatusEnum.PARK_IN);
                break;
            case 2:
                //删除出场图片
                parkingImageRepositoryI.deleteByParkingOrderId(parkingOrder.getId(), ParkingStatusEnum.PARK_OUT);
                break;
            case 3:
                //全部删除
                parkingImageRepositoryI.deleteByParkingOrderId(parkingOrder.getId());
                break;
        }
        if (parkingImages != null) {
            parkingImageRepositoryI.saveOrUpdate(parkingImages);
        }
    }

    private void updateParkingOrderByFee(ParkingOrder parkingOrder) {
        ParkingOrder oldParkingOrder = BeanUtil.copy(parkingOrder, ParkingOrder.class);
        // 删除临停订单
        List<TempParkingUnpaidOrder> tempParkingUnpaidOrders = unpaidOrderService.listByParkingId(parkingOrder.getId());
        if (Func.isNotEmpty(tempParkingUnpaidOrders)) {
            List<Long> ids = tempParkingUnpaidOrders.stream().map(BaseEntity::getId).collect(Collectors.toList());
            unpaidOrderService.deleteBatchIds(ids);
        }
        ChannelTodo resultTodo = roadSideParkingBiz.todoParkingDetail(parkingOrder);
        resultTodo.setRelationType(resultTodo.getCardType());
        resultTodo.setErrorAmount(resultTodo.getReceiveAmount());
        resultTodo.setErrorAmountType(2);
        if (resultTodo.getReceiveAmount().compareTo(BigDecimal.ZERO) > 0) {
            resultTodo.setReasonId((long) UnpaidOrderType.TYPE100.getValue());
            // 生成未缴订单
            this.createUnpaidOrder(resultTodo, parkingOrder);
        }
        resultTodo.setStatus(1);
        // 更新代办
        todoService.updateById(resultTodo);
        // 更新出场
        this.updateParkingInfoWithEventType(parkingOrder, resultTodo, null);
        // 出场信息还原
        this.lambdaUpdate().set(ParkingOrder::getExitChannelId, oldParkingOrder.getExitChannelId())
            .set(ParkingOrder::getExitWay, oldParkingOrder.getExitWay())
            .set(ParkingOrder::getExitTodoId, oldParkingOrder.getExitTodoId())
            .set(ParkingOrder::getTargetParkingId, null)
            .eq(BaseEntity::getId, oldParkingOrder.getId()).update();
    }

    /**
     * 删除区间内无效的停车记录
     *
     * @param parkingOrder
     */
    private void delInvalidParkingOrder(ParkingOrder parkingOrder) {
        ParkingOrderDTO param = new ParkingOrderDTO();
        param.setParklotId(parkingOrder.getParklotId());
        param.setPlaceId(parkingOrder.getPlaceId());
        param.setEnterTime(parkingOrder.getEnterTime());
        param.setExitTime(parkingOrder.getExitTime());
        List<ParkingOrderVO> list = orderMapper.findNotTempOrderParkingOrder(param);
        if (Func.isNotEmpty(list)) {
            for (ParkingOrder order : list) {
                if (order.getId().equals(parkingOrder.getId())) {
                    continue;
                }
                this.lambdaUpdate()
                    .eq(BaseEntity::getId, order.getId())
                    .set(BaseEntity::getIsDeleted, 1).update();
                //记录日志
                AuditLogger.add(String.valueOf(order.getId()), "delParkingOrder", "删除停车记录",
                    "", "", null, "删除区间内无效的停车记录");
            }
        }
    }


    /**
     * 删除停车记录
     *
     * @param parking
     * @return
     */
    @Override
    @Transactional
    public boolean delParkingOrder(ParkingOrderDTO parking) {
        if (Func.isEmpty(parking.getIds())) {
            throw new ServiceException("停车记录不存在");
        }
        if (Func.isBlank(parking.getHandlerRemark())) {
            throw new ServiceException("备注不能为空");
        }
        parking.getIds().forEach(id -> {
            if (Func.equals(parking.getId(), id)) {
                throw new ServiceException("不能删除当前处理中的停车记录");
            }
            delParkingOrder(id, parking.getHandlerRemark());
        });
        return true;
    }

    private boolean delParkingOrder(Long id, String remark) {
        ParkingOrder parkingOrder = this.getById(id);
        if (parkingOrder == null) {
            throw new ServiceException("停车记录不存在");
        }
        BladeUser user = AuthUtil.getUser();
        if (Func.isEmpty(user)) {
            user = new BladeUser();
            user.setUserId(10001L);
            user.setUserName("系统");
        }
        Integer abnormalType = null;
        Long abnormalId = null;
        ParkingOrderAbnormal parkingOrderAbnormal = parkingOrderAbnormalService.findParkingOrderAbnormal(parkingOrder.getId());
        if (parkingOrderAbnormal != null) {
            if (parkingOrderAbnormal.getStatus() != 0) {
                if (!user.getUserId().equals(parkingOrderAbnormal.getHandlerPersonId())) {
                    throw new ServiceException("该订单已被" + parkingOrderAbnormal.getHandlerPersonName() + "处理，只能" + parkingOrderAbnormal.getHandlerPersonName() + "本人可以处理！");
                }
            }
            abnormalType = parkingOrderAbnormal.getAbnormalType();
            abnormalId = parkingOrderAbnormal.getId();
        }
        /**
         * 获取差异类型
         */
        if (abnormalType == null) {
            List<ParkingOrderVO> parkingOrderVOS = getAbnormalParkingOrderList(id);
            if (Func.isNotEmpty(parkingOrderVOS)) {
                abnormalType = parkingOrderVOS.get(0).getAbnormalType();
            }
        }

        // 删除临停订单
        List<TempParkingOrder> tempParkingOrders = tempParkingOrderService.selectPaidListByParkingId(parkingOrder.getId());
        log.info("delParkingOrder tempParkingOrders={}", JSON.toJSONString(tempParkingOrders));
        if (Func.isNotEmpty(tempParkingOrders)) {
            throw new ServiceException("存在已支付的订单不能删除");
        }
        // 删除临停欠缴订单
        deleteTempParkingUnpaidOrder(parkingOrder.getId(), remark);

        //删除停车记录
        this.lambdaUpdate()
            .eq(BaseEntity::getId, id)
            .set(BaseEntity::getIsDeleted, 1).update();
        ParkingOrderDTO parking = new ParkingOrderDTO();
        parking.setId(id);
        parking.setParklotId(parkingOrder.getParklotId());
        parking.setHandlerRemark(remark);
        parking.setAbnormalType(abnormalType);
        this.saveParkingOrderAbnormal(parking, user, abnormalId, 2);
        //记录日志
        AuditLogger.add(String.valueOf(parkingOrder.getId()), "delParkingOrder", "删除停车记录",
            "", "", null, remark);
        return true;
    }

    /**
     * 查询异常停车记录
     *
     * @param id
     * @return
     */
    private List<ParkingOrderVO> getAbnormalParkingOrderList(Long id) {
        List<ParkingOrderVO> recordList = null;
        ParkingOrderDTO parkingOrderDTO = new ParkingOrderDTO();
        parkingOrderDTO.setId(id);
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.parkingOrderAbnormalMapper.getAbnormalParkingOrderList(null, parkingOrderDTO);
        } else {
            recordList = this.parkingOrderAbnormalMapper.getAbnormalParkingOrderListByDoris(null, parkingOrderDTO);
        }
        return recordList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cleanParkingOrder(ParkingOrderDTO parking) {
        ParkingOrder parkingOrder = this.getById(parking.getId());
        LecentAssert.notNull(parkingOrder, "停车记录不存在");

        ParkingOrder presentParking = selectPresentByPlaceId(parkingOrder.getPlaceId());
        boolean present = presentParking != null && parkingOrder.getId().equals(presentParking.getId());

        // 查询支付订单
        List<TempParkingOrder> tempParkingOrders = tempParkingOrderService.selectPaidListByParkingId(parkingOrder.getId());
        if (Func.isNotEmpty(tempParkingOrders)) {
            if (present) {
                throw new ServiceException("在场停车记录已产生支付不能删除");
            } else {
                // 通过支付信息进行出场
                parkingPaidCompensateExitHandler.doHandle(parkingOrderConverter.fromDO(parkingOrder));
                return true;
            }
        }

        // 删除临停欠缴订单
        deleteTempParkingUnpaidOrder(parkingOrder.getId(), parking.getHandlerRemark());

        // 删除停车记录
        boolean delete = deleteLogic(Collections.singletonList(parkingOrder.getId()));
        LecentAssert.isTrue(delete, "停车记录删除失败");

        if (present) {
            // 将车位设置为空闲
            ParkingOrderDomainEvent domainEvent =
                new ParkingOrderDomainEvent(this, ParkingOrderEventType.CLEARED, parkingOrderConverter.fromDO(parkingOrder));
            SpringDomainEventPublisher.publish(domainEvent);
        }

        return true;
    }

    /**
     * 删除临停未缴订单
     *
     * @param parkingId 停车id
     * @param remark    备注
     */
    private void deleteTempParkingUnpaidOrder(Long parkingId, String remark) {
        List<TempParkingUnpaidOrder> tempParkingUnpaidOrders = unpaidOrderService.listByParkingId(parkingId);
        if (Func.isNotEmpty(tempParkingUnpaidOrders)) {
            List<Long> ids = tempParkingUnpaidOrders.stream().map(BaseEntity::getId).collect(Collectors.toList());
            Boolean delete = tempParkingOrderService.batchRemoveOrder(ids, remark);
            LecentAssert.isTrue(delete, "未缴订单删除失败");
        }
    }

    @Override
    public List<ParkingOrderVO> findOccupyParkingOrderVO(ParkingOrderDTO parkingOrderDTO) {
        return baseMapper.findOccupyParkingOrderVO(parkingOrderDTO);
    }

    @Override
    public ParkingDetail queryParkingDetailV2(ParkingOrderDTO parking) {
        parking.setNeedPayCode(false);
        return queryParkingDetailNew(parking);
    }

    @Override
    public IPage<ParkingDetail> userPaidOrder(Long userId, IPage<ParkingDetail> page) {
        List<ParkingDetail> records = baseMapper.queryPaidOrderByUserId(page, userId);
        records.forEach(pd -> {
            pd.setDeviceComboList(todoService.deviceCombo(pd.getPlaceId()));
            todoService.addMerchantField(pd);
        });
        return page.setRecords(records);
    }

    @Override
    public Boolean refundAbnormalOrder(Long parkingOrderId) {
        handleAbnormalOrder(parkingOrderId, "已退款处理");

        // 记录审计日志
        AuditLogger.add(
            String.valueOf(parkingOrderId),
            "refundParkingOrder",
            "异常订单退款处理",
            "",
            "",
            null,
            "退款处理"
        );

        return true;
    }

    @Override
    public Boolean ignoreAbnormalOrder(Long parkingOrderId) {
        handleAbnormalOrder(parkingOrderId, "无需处理");

        // 记录审计日志
        AuditLogger.add(
            String.valueOf(parkingOrderId),
            "ignoreAbnormalOrder",
            "异常订单无需处理",
            "",
            "",
            null,
            "无需处理"
        );

        return true;
    }

    @Override
    public ParkingOrder getParkingImageUrlById(Long parkingOrderId) {
        return getOne(Wrappers.<ParkingOrder>lambdaQuery()
            .select(ParkingOrder::getId, ParkingOrder::getParklotId, ParkingOrder::getEnterImageUrl, ParkingOrder::getExitImageUrl)
            .eq(ParkingOrder::getId, parkingOrderId));
    }

    private void handleAbnormalOrder(Long parkingOrderId, String remark) {
        // 1. 获取并验证停车订单
        ParkingOrder parkingOrder = this.getById(parkingOrderId);
        LecentAssert.notNull(parkingOrder, "停车记录不存在");

        // 2. 获取当前操作用户
        BladeUser user = AuthUtil.getUser();

        // 3. 处理异常订单相关信息
        Integer abnormalType = null;
        Long abnormalId = null;

        // 获取停车订单异常记录
        ParkingOrderAbnormal parkingOrderAbnormal = parkingOrderAbnormalService.findParkingOrderAbnormal(parkingOrder.getId());
        if (parkingOrderAbnormal != null) {
            // 检查异常订单处理权限
            if (parkingOrderAbnormal.getStatus() != 0) {
                if (!user.getUserId().equals(parkingOrderAbnormal.getHandlerPersonId())) {
                    throw new ServiceException("该订单已被" + parkingOrderAbnormal.getHandlerPersonName()
                        + "处理，只能" + parkingOrderAbnormal.getHandlerPersonName() + "本人可以处理！");
                }
            }
            abnormalType = parkingOrderAbnormal.getAbnormalType();
            abnormalId = parkingOrderAbnormal.getId();
        }

        // 4. 获取异常类型（如果未从异常记录中获取到）
        if (abnormalType == null) {
            List<ParkingOrderVO> parkingOrderVOS = getAbnormalParkingOrderList(parkingOrderId);
            if (Func.isNotEmpty(parkingOrderVOS)) {
                abnormalType = parkingOrderVOS.get(0).getAbnormalType();
            }
        }

        // 5. 构建并保存退款处理信息
        ParkingOrderDTO parking = new ParkingOrderDTO();
        parking.setId(parkingOrderId);
        parking.setParklotId(parkingOrder.getParklotId());
        parking.setHandlerRemark(remark);
        parking.setAbnormalType(abnormalType);
        this.saveParkingOrderAbnormal(parking, user, abnormalId, 1);
    }

    @Override
    public IPage<ParkingOrderVO> queryByPlateAndTime(IPage<ParkingOrderVO> page, ParkingOrderDTO parking) {
        // 获取用户授权的车场ID列表
        List<Long> parkLotIds = userParklotService.getCurrentUserBindParkLotIds();
        if (Func.isEmpty(parkLotIds)) {
            return page;
        }

        // 设置查询参数
        parking.setParkLotIds(parkLotIds);

        // 查询数据
        List<ParkingOrderVO> list;
        if (ActiveProfilesUtil.isTest()) {
            list = baseMapper.queryByPlateAndTimeFromMysql(page, parking);
        } else {
            list = baseMapper.queryByPlateAndTimeFromDoris(page, parking);
        }

        // 添加名称信息
        addName(list);

        return page.setRecords(list);
    }
}
