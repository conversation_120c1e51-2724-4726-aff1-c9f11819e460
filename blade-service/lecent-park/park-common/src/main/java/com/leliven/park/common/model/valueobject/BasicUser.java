package com.leliven.park.common.model.valueobject;

import com.leliven.ddd.core.annotations.ValueObject;

import lombok.Getter;

/**
 * 基础用户值对象
 */
@Getter
@ValueObject
public class BasicUser {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 私有构造函数
     *
     * @param id 用户ID
     * @param username 用户名
     * @param nickName 昵称
     */
    private BasicUser(Long id, String username, String nickName) {
        this.id = id;
        this.username = username;
        this.nickName = nickName;
    }

    /**
     * 创建基础用户值对象
     *
     * @param id 用户ID
     * @param username 用户名
     * @param nickName 昵称
     * @return 基础用户值对象 {@link BasicUser}
     */
    public static BasicUser of(Long id, String username, String nickName) {
        return new BasicUser(id, username, nickName);
    }


}
