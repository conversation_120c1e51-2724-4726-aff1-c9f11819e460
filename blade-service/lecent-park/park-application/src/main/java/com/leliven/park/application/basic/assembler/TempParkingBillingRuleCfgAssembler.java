package com.leliven.park.application.basic.assembler;

import com.leliven.park.application.basic.dto.TempParkingBillingRuleCfgDTO;
import com.leliven.park.application.basic.dto.TempParkingBillingSegmentCfgDTO;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingRuleCfg;
import com.leliven.park.domain.basic.billingrule.model.TempParkingBillingSegmentCfg;
import com.leliven.ddd.core.annotations.Assembler;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Assembler
public class TempParkingBillingRuleCfgAssembler {


    public TempParkingBillingRuleCfgDTO toDTO(TempParkingBillingRuleCfg tempParkingBillingRuleCfg) {
        if (tempParkingBillingRuleCfg == null) {
            return null;
        }

        TempParkingBillingRuleCfgDTO billingRuleCfgDTO = new TempParkingBillingRuleCfgDTO();
        billingRuleCfgDTO.setName(tempParkingBillingRuleCfg.getName());
        billingRuleCfgDTO.setMemo(tempParkingBillingRuleCfg.getMemo());
        billingRuleCfgDTO.setParklotId(tempParkingBillingRuleCfg.getParklotId());
        billingRuleCfgDTO.setChargeType(tempParkingBillingRuleCfg.getChargeType().getCode());
        billingRuleCfgDTO.setIncludeFreeTime(tempParkingBillingRuleCfg.isIncludeFreeTime());
        billingRuleCfgDTO.setFreeDurationMinutes(tempParkingBillingRuleCfg.getFreeDuration().toMinutes());
        billingRuleCfgDTO.setFreeLeaveDurationMinutesAfterPaid(tempParkingBillingRuleCfg.getFreeLeaveDurationAfterPaid().toMinutes());
        billingRuleCfgDTO.setChargePreTime(tempParkingBillingRuleCfg.getChargePreTime());
        billingRuleCfgDTO.setChargePreFee(tempParkingBillingRuleCfg.getChargePreFee());
        billingRuleCfgDTO.setChargePreMinute(tempParkingBillingRuleCfg.getChargePreMinute());
        billingRuleCfgDTO.setPreTopTime(tempParkingBillingRuleCfg.getPreTopTime());
        billingRuleCfgDTO.setPreTopFee(tempParkingBillingRuleCfg.getPreTopFee());
        billingRuleCfgDTO.setPerNHour(tempParkingBillingRuleCfg.getPerNHour());
        billingRuleCfgDTO.setPerNHourCappingFee(tempParkingBillingRuleCfg.getPerNHourCappingFee());
        billingRuleCfgDTO.setPerFirstTime(tempParkingBillingRuleCfg.getPerFirstTime());
        billingRuleCfgDTO.setPerFixedFree(tempParkingBillingRuleCfg.getPerFixedFree());
        billingRuleCfgDTO.setSegmentConfigs(TempParkingBillingSegmentCfgAssembler.toDTOList(tempParkingBillingRuleCfg.getSegmentConfigs()));

        return billingRuleCfgDTO;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class TempParkingBillingSegmentCfgAssembler {

        public static TempParkingBillingSegmentCfgDTO toDTO(TempParkingBillingSegmentCfg segmentConfig) {
            if (segmentConfig == null) {
                return null;
            }

            TempParkingBillingSegmentCfgDTO segmentConfigDTO = new TempParkingBillingSegmentCfgDTO();
            segmentConfigDTO.setBillingStartTime(segmentConfig.getBillingTimeRange().getStartTime());
            segmentConfigDTO.setBillingEndTime(segmentConfig.getBillingTimeRange().getEndTime());
            segmentConfigDTO.setBillingCycleAmount(segmentConfig.getBillingCycleAmountAsBigDecimal());
            segmentConfigDTO.setBillingCycleMinutes(segmentConfig.getBillingCycleMinutes());
            segmentConfigDTO.setMaxBillingAmount(segmentConfig.getMaxBillingAmountAsBigDecimal());
            segmentConfigDTO.setCrossSegmentFreeMinutes(segmentConfig.getCrossSegmentFreeMinutes());

            return segmentConfigDTO;
        }

        public static List<TempParkingBillingSegmentCfgDTO> toDTOList(List<TempParkingBillingSegmentCfg> segmentConfigs) {
            return segmentConfigs.stream().map(TempParkingBillingSegmentCfgAssembler::toDTO).collect(Collectors.toList());
        }
    }
}
