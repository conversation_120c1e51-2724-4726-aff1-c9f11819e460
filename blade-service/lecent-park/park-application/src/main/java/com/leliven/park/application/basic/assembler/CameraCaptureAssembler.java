package com.leliven.park.application.basic.assembler;

import com.lecent.device.dto.DeviceParkingEventDTO;
import com.lecent.device.event.DeviceCameraCaptureEvent;
import com.lecent.park.core.notify.domain.MsgRequest;
import com.lecent.park.entity.ParklotDeviceRet;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.application.common.assembler.ParkingDeviceAssembler;
import com.leliven.park.domain.basic.place.ParkingPlaceGateway;
import com.leliven.park.domain.basic.place.model.ParkingPlaceCameraCapture;
import com.leliven.park.domain.basic.place.model.ParkingSpace;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceCameraCaptureGroup;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.domain.basic.place.support.CameraCaptureDeviceTypeSpecification;
import com.leliven.park.common.model.ParkingDevice;
import com.leliven.park.infrastructure.gateway.persistence.basic.doris.dataobject.ParkingPlaceCameraCaptureRecordDO;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.mapper.ParklotDeviceRelationMapperService;
import com.leliven.ddd.core.annotations.Assembler;
import com.leliven.vehicle.model.Vehicle;
import lombok.RequiredArgsConstructor;
import org.springblade.core.tool.utils.Func;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 摄像头抓拍装配器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class CameraCaptureAssembler {

	private final ParkingPlaceGateway parkingPlaceGateway;
    private final CameraCaptureDeviceTypeSpecification cameraCaptureDeviceTypeSpec;
	private final ParklotDeviceRelationMapperService parkingDeviceRelationMapperService;

	public Optional<ParkingPlaceCameraCapture> fromDeviceCameraCaptureEvent(DeviceCameraCaptureEvent event) {
		ParkingDevice parkingDevice = ParkingDeviceAssembler.fromDevice(event.getSource());
		return this.parkingPlaceGateway.asCacheSupplier().getByBoundDeviceId(parkingDevice.getId())
			.map(space ->
				ParkingPlaceCameraCapture.create(CameraCaptureRecordAssembler.cameraCaptureEvent2Domain(event))
					.belongToDevice(parkingDevice)
					.belongToParkingSpace(space)
			);
	}

	public List<ParkingPlaceCameraCaptureRecordDO> fromDeviceCameraCaptureEvents(List<DeviceCameraCaptureEvent> events) {
		List<Long> deviceIds = events.stream().map(e -> e.getSource().getId()).distinct().collect(Collectors.toList());
		Map<Long, ParklotDeviceRet> parkingDeviceRelationMap = parkingDeviceRelationMapperService.selectMapByDeviceIds(deviceIds);
		return events.stream()
			.filter(e -> parkingDeviceRelationMap.containsKey(e.getSource().getId()) && Func.isNotBlank(e.getPayload().getImageUrl()))
			.map(e -> {
				ParklotDeviceRet parklotDeviceRet = parkingDeviceRelationMap.get(e.getSource().getId());
				ParkingPlaceCameraCaptureRecordDO captureRecord = new ParkingPlaceCameraCaptureRecordDO();
				captureRecord.setParkingPlaceId(parklotDeviceRet.getParkPlaceId());
				captureRecord.setCaptureTime(e.getPayload().getCaptureTime());
				captureRecord.setType(e.getPayload().getType().getCode());
				captureRecord.setImageUrl(e.getPayload().getImageUrl());
				Vehicle vehicle = Vehicle.of(e.getPayload().getPlate(), e.getPayload().getPlateColor());
				captureRecord.setPlate(vehicle.getPlate());
				captureRecord.setPlateColorCode(vehicle.getPlateColor().code());
				captureRecord.setPlateColorCnName(vehicle.getPlateColor().chineseName());
				captureRecord.setParkingState(ParkingPlaceIdleState.resolve(e.getPayload().getParkingState()).getValue());
				captureRecord.setDeviceType(DeviceType.resolve(e.getSource().getType()).getValue());
				captureRecord.setTenantId(e.getSource().getTenantId());
				return captureRecord;
			}).collect(Collectors.toList());
	}

	public List<ParkingPlaceCameraCapture> fromDeviceParkingEvents(List<MsgRequest<DeviceParkingEventDTO>> events) {
		List<ParkingPlaceCameraCapture> captures = new ArrayList<>();
		events.forEach(e -> {
			ParkingDevice device = ParkingPlaceDeviceAssembler.from(e.getBody());
			if (this.cameraCaptureDeviceTypeSpec.isSatisfiedBy(device)) {
				ParkingSpace parkingSpace = this.parkingPlaceGateway.getByDevice(device);
				if (Objects.nonNull(parkingSpace)) {
					ParkingPlaceCameraCaptureGroup group = CameraCaptureRecordAssembler.fromDeviceParkingEventDTO(e.getBody());
					ParkingPlaceCameraCapture capture = ParkingPlaceCameraCapture.create(group.getRecords())
						.belongToDevice(device)
						.belongToParkingSpace(parkingSpace);
					captures.add(capture);
				}
			}
		});
		return captures;
	}
}
