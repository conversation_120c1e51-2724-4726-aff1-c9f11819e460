package com.leliven.park.application.basic.assembler;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.leliven.ddd.shared.assembler.SimpleLocationAssemble;
import com.leliven.park.application.basic.dto.InspectionItemResultDTO;
import com.leliven.park.application.basic.dto.command.InspectionRecordReportCommand;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskItem;
import com.leliven.park.domain.basic.inspection.model.valueobject.*;
import com.leliven.park.infrastructure.gateway.persistence.basic.assembler.InspectionAssembler;
import com.leliven.ddd.core.annotations.Assembler;
import lombok.RequiredArgsConstructor;
import org.springblade.common.utils.ObjectValidator;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tool.utils.Func;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 巡检记录装配器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class InspectionRecordAssembler {

    private final InspectionAssembler inspectionAssembler;

    /**
     * 将巡检记录上报命令转换为巡检对象记录
     *
     * @param command 巡检记录上报命令 {@link InspectionRecordReportCommand}
     * @return 巡检对象记录 {@link InspectionObjectRecord}
     */
	public InspectionObjectRecord toObjectRecord(InspectionRecordReportCommand command) {
        // 验证参数
        ObjectValidator.requireNonNull(command, "上报命令不能为空");
        // 获取巡检员
        Inspector inspector = inspectionAssembler.toInspectorByCurrentUser();

		return InspectionObjectRecord.builder()
            .object(toInspectionObject(command))
            .inspectionTime(LocalDateTime.now())
            .location(SimpleLocationAssemble.toUnvalidatedValueObject(command.getLocation()))
            .inspector(inspector)
            .type(InspectionType.ACTIVE_REPORT)
			.recordItems(fromRecordItemDTOList(command.getRecordItems()))
			.build();
	}

    /**
     * 根据巡检对象类型、对象Id转换为巡检对象
     *
     * @param command 巡检记录上报命令 {@link InspectionRecordReportCommand}
     * @return 巡检对象 {@link InspectionObject}
     */
    public InspectionObject toInspectionObject(InspectionRecordReportCommand command) {
        InspectionObjectType type = InspectionObjectType.resolve(command.getObjectType());
        switch (type) {
            case PARKLOT:
                return InspectionObject.ofParklot(command.getObjectId(), "");
            case PARKING_CHANNEL:
                return InspectionObject.ofParkingChannel(command.getObjectId(), "");
            case PARKING_PLACE:
                return InspectionObject.ofParkingPlace(command.getObjectId(), "");
            default:
                throw new ServiceException("不支持的巡检对象类型");
        }
    }

    /**
     * 将巡检任务项DTO转换为巡检任务项
     *
     * @param recordItemDTO 巡检任务项DTO {@link InspectionItemResultDTO}
     * @return 巡检任务项 {@link InspectionTaskItem}
     */
    public InspectionRecordItem fromRecordItemDTO(InspectionItemResultDTO recordItemDTO) {
		boolean createWorkOrderEnable = Func.toBoolean(recordItemDTO.getCreateWorkOrderEnable(), Boolean.FALSE);
		return InspectionRecordItem.builder()
			.taskItemId(recordItemDTO.getTaskItemId())
            .itemId(recordItemDTO.getItemId())
			.status(InspectionResultStatus.resolve(recordItemDTO.getStatus()))
			.abnormalInfo(InspectionAbnormalInfo.builder()
				.phenomenonId(recordItemDTO.getAbnormalDescId())
				.phenomenonName(recordItemDTO.getAbnormalTitle())
				.abnormalReasonId(Func.toLong(recordItemDTO.getAbnormalReasonId(), 0L))
				.abnormalReasonName(Func.toStr(recordItemDTO.getAbnormalReasonName(), "未知原因"))
				.abnormalImages(recordItemDTO.getAbnormalImages())
				.createWorkOrderEnable(createWorkOrderEnable)
				.workOrderId(createWorkOrderEnable ? IdWorker.getId() : null)
				.resolved(Func.toBoolean(recordItemDTO.getResolved(), Boolean.TRUE))
				.build())
            .additionalInfo(recordItemDTO.getAdditionalInfo())
			.build();
    }

    /**
     * 将巡检任务项DTO列表转换为巡检任务项Map
     *
     * @param recordItemDTOList 巡检任务项DTO列表 {@link InspectionItemResultDTO}
     * @return 巡检任务项Map {@link Map}<{@link Long}, {@link InspectionTaskItem}>
     */
    public List<InspectionRecordItem> fromRecordItemDTOList(List<InspectionItemResultDTO> recordItemDTOList) {
        return recordItemDTOList.stream().map(this::fromRecordItemDTO)
            .collect(Collectors.toList());
    }
}
