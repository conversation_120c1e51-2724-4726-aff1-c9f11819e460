package com.leliven.park.application.basic.assembler;


import com.leliven.park.application.basic.dto.InspectionPlanDTO;
import com.leliven.park.domain.basic.inspection.model.InspectionPlan;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionExecutorAssignConfig;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionInterval;
import com.leliven.ddd.core.annotations.Assembler;
import com.leliven.ddd.core.valueobject.DateTimeRange;
import com.leliven.ddd.core.valueobject.TimeRange;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springblade.core.tool.utils.Func;

import java.util.Objects;

/**
 * 巡检计划装配器
 *
 * <AUTHOR>
 */
@Assembler
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class InspectionPlanAssembler {

    /**
     * 将DTO转换为巡检计划领域对象
     *
     * @param dto 巡检计划DTO {@link InspectionPlanDTO}
     * @return 巡检计划领域对象 {@link InspectionPlan}
     */
    public static InspectionPlan fromDTO(InspectionPlanDTO dto) {
        return new InspectionPlan(dto.getId())
			.configureName(dto.getName())
			.configureValidityPeriod(DateTimeRange.ofFullDay(dto.getStartDate(), dto.getEndDate()))
			.configureWorkTimeRange(TimeRange.of(dto.getWorkTimeStart(), dto.getWorkTimeEnd()))
			.configureInterval(InspectionInterval.of(dto.getIntervalUnit(), dto.getIntervalValue()))
			.configureItems(dto.getItemIds())
			.configureParklots(dto.getParklotIds())
			.configureExecutorAssignConfig(InspectionExecutorAssignConfig.of(
                Func.toBoolean(dto.getExecutorAssignConfig().getDutyGroupEnabled(), Boolean.FALSE),
                dto.getExecutorAssignConfig().getDutyGroups()))
            .configureTaskPriority(dto.getTaskPriority())
            .configureRemark(dto.getRemark());
    }

    /**
     * 将巡检计划领域对象转换为DTO
     *
     * @param plan 巡检计划领域对象 {@link InspectionPlan}
     * @return 巡检计划DTO {@link InspectionPlanDTO}
     */
	public static InspectionPlanDTO domainObject2DTO(InspectionPlan plan) {
        InspectionPlanDTO dto = new InspectionPlanDTO();
        dto.setId(plan.getId());
        dto.setName(plan.getName());
        dto.setStartDate(plan.getValidityPeriod().getStartDateTime().toLocalDate());
        dto.setEndDate(plan.getValidityPeriod().getEndDateTime().toLocalDate());
        dto.setWorkTimeStart(plan.getWorkTimeRange().getStartTime());
        dto.setWorkTimeEnd(plan.getWorkTimeRange().getEndTime());
        dto.setIntervalUnit(plan.getInterval().getUnit().getValue());
        dto.setIntervalValue(plan.getInterval().getValue());
        dto.setItemIds(plan.getItemIds());
        dto.setParklotIds(plan.getParklotIds());
        dto.setTaskPriority(plan.getTaskPriority());
        InspectionExecutorAssignConfig executorAssignConfig = plan.getExecutorAssignConfig();
        if (Objects.nonNull(executorAssignConfig)) {
            InspectionPlanDTO.ExecutorAssignConfigDTO executorAssignConfigDTO = new InspectionPlanDTO.ExecutorAssignConfigDTO();
            executorAssignConfigDTO.setDutyGroupEnabled(executorAssignConfig.isDutyGroupEnabled());
            executorAssignConfigDTO.setDutyGroups(executorAssignConfig.getDutyGroups());
            dto.setExecutorAssignConfig(executorAssignConfigDTO);
        }
        dto.setRemark(plan.getRemark());
		return dto;
	}
}
