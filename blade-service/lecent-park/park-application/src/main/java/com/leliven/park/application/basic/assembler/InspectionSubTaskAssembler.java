package com.leliven.park.application.basic.assembler;


import com.leliven.park.application.basic.dto.InspectionSubTaskCompleteResultDTO;
import com.leliven.park.domain.basic.inspection.InspectionTaskGateway;
import com.leliven.park.domain.basic.inspection.model.InspectionSubTask;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionSubTaskJumpType;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionTaskStatus;
import com.leliven.park.infrastructure.gateway.persistence.basic.InspectionSubTaskGatewayReadable;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.InspectionSubTaskVO;
import com.leliven.ddd.core.annotations.Assembler;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

import org.springblade.common.exception.NotFoundException;

/**
 * 巡检子任务装配器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class InspectionSubTaskAssembler {

	private final InspectionTaskGateway taskGateway;
	private final InspectionSubTaskGatewayReadable subTaskGatewayReadable;

	/**
	 * 构建巡检子任务完成结果
	 *
	 * @param completedSubTask 已完成的巡检子任务 {@link InspectionSubTask}
	 * @param jumpTypeCode 跳转类型 {@link InspectionSubTaskJumpType#getCode()}
	 * @return 巡检子任务完成结果DTO {@link InspectionSubTaskCompleteResultDTO}
	 */
	public InspectionSubTaskCompleteResultDTO buildCompletionResult(InspectionSubTask completedSubTask, String jumpTypeCode) {
		Long taskId = completedSubTask.getTaskId();
		Long currentSubTaskId = completedSubTask.getId();

		// 构建基础结果
		InspectionSubTaskCompleteResultDTO.InspectionSubTaskCompleteResultDTOBuilder resultBuilder =
			InspectionSubTaskCompleteResultDTO.builder()
				.taskId(taskId)
				.currentCompletedId(currentSubTaskId);

		// 查询最新巡检任务状态
		InspectionTaskStatus latestTaskStatus = this.taskGateway.getStatus(taskId)
			.orElseThrow(() -> new NotFoundException("巡检任务不存在: id=" + taskId));

		// 设置当前任务状态
		resultBuilder.taskStatus(latestTaskStatus.getValue());

		// 如果任务正在进行中，且指定了跳转类型，则处理跳转逻辑
		InspectionSubTaskJumpType jumpType = InspectionSubTaskJumpType.resolve(jumpTypeCode);
		if (latestTaskStatus.isInProgress() && Objects.nonNull(jumpType)) {
			InspectionSubTaskVO targetSubTask = this.getTargetSubTaskVO(taskId, currentSubTaskId, jumpTypeCode);
			// 设置跳转任务信息
			resultBuilder.hasNextPendingSubTask(Objects.nonNull(targetSubTask))
				.nextPendingSubTask(targetSubTask);
		}

		return resultBuilder.build();
	}

	/**
	 * 根据任务ID和当前子任务ID以及跳转类型获取目标子任务
	 *
	 * @param taskId 任务ID {@link Long}
	 * @param currentSubTaskId 当前子任务ID {@link Long}
	 * @param jumpTypeCode 跳转类型 {@link String}
	 * @return 目标子任务 {@link InspectionSubTaskVO}
	 */
	public InspectionSubTaskVO getTargetSubTaskVO(Long taskId, Long currentSubTaskId, String jumpTypeCode) {
		InspectionSubTaskJumpType jumpType = InspectionSubTaskJumpType.resolve(jumpTypeCode);
		if (Objects.isNull(jumpType)) {
			return null;
		}

		switch (jumpType) {
			case PREVIOUS:
				return this.subTaskGatewayReadable.getPreviousPendingSubTaskVO(taskId, currentSubTaskId);
			case NEXT:
				return this.subTaskGatewayReadable.getNextPendingSubTaskVO(taskId, currentSubTaskId);
			default:
				return null;
		}
	}

}
