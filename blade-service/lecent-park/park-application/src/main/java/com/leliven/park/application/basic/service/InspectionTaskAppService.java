package com.leliven.park.application.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.common.constant.ParkMQConstant;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.leliven.ddd.shared.dto.SimpleLocationDTO;
import com.leliven.park.application.basic.dto.command.InspectionTaskChangeExecutorsCommand;
import com.leliven.park.domain.basic.inspection.InspectionTaskGateway;
import com.leliven.park.domain.basic.inspection.event.InspectionSubTaskEvent;
import com.leliven.park.domain.basic.inspection.event.InspectionTaskEvent;
import com.leliven.park.domain.basic.inspection.model.InspectionSubTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTask;
import com.leliven.park.domain.basic.inspection.model.InspectionTaskExecutor;
import com.leliven.park.domain.basic.inspection.support.InspectionTaskDomainService;
import com.leliven.park.infrastructure.gateway.persistence.basic.InspectionTaskGatewayReadable;
import com.leliven.park.infrastructure.gateway.persistence.basic.assembler.InspectionTaskAssembler;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.InspectionTaskQuery;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.InspectionTaskVO;
import com.leliven.ddd.core.event.SpringDomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检任务服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskAppService {

    private final MqSender mqSender;
    private final InspectionTaskGateway taskGateway;
    private final InspectionTaskAssembler taskAssembler;
    private final InspectionTaskDomainService taskDomainService;
    private final InspectionTaskGatewayReadable taskGatewayReadable;


    /**
     * 分页查询巡检任务
     *
     * @param query 查询条件
     * @return 分页数据
     */
    public IPage<InspectionTaskVO> page(InspectionTaskQuery query) {
        if (Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())) {
            query.setAscs("it_status");
            query.setDescs("it_start_time");
        }

        return taskGatewayReadable.page(query);
    }

    /**
     * 根据当前用户查询巡检任务
     *
     * @param query 查询条件
     * @return 分页数据
     */
    public IPage<InspectionTaskVO> pageByCurrentUser(InspectionTaskQuery query) {
        return taskGatewayReadable.pageByCurrentUser(query);
    }

    /**
     * 根据任务id获取任务详情
     *
     * @param id 任务id
     * @return 任务详情 {@link InspectionTaskVO}
     */
	public InspectionTaskVO detail(Long id) {
        return taskGatewayReadable.getViewObjectById( id);
	}

    /**
     * 根据当前用户查询进行中任务
     *
     * @return 进行中任务列表 {@link List}<{@link InspectionTaskVO}>
     */
    public List<InspectionTaskVO> listInProgressTaskByCurrentUser() {
        return taskGatewayReadable.listInProgressTaskByUserId(AuthUtil.getUserId());
    }

    /**
     * 获取当前登录用户今日剩余巡检任务数量
     *
     * @return 剩余任务数量
     */
    public Integer countTodayRemainTasksByCurrentUser() {
        return taskGatewayReadable.countTodayRemainTasksByUserId(AuthUtil.getUserId());
    }

    /**
     * 根据任务ID查询巡检轨迹点列表
     *
     * @param id 巡检任务ID {@link Long}
     * @return 巡检轨迹点列表 {@link List}<{@link SimpleLocationDTO}>
     */
    public List<SimpleLocationDTO> tracks(Long id) {
        return taskGatewayReadable.taskTracks(id);
    }

    /**
     * 根据任务id生成任务项
     *
     * @param id 任务id
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    public boolean generateTaskItems(Long id) {
        InspectionTask task = taskGateway.getOrElseThrow(id);

        // 发布任务已创建事件
        SpringDomainEventPublisher.publish(InspectionTaskEvent.created(this, task));

        return true;
    }

    /**
     * 手动将任务状态变更为进行中
     *
     * @param id 任务ID
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    public boolean startTask(Long id) {
        InspectionTask task = taskGateway.getOrElseThrow(id);

        // 使用领域对象的start方法
        boolean success = task.start();
        if (success) {
            // 保存任务状态变更
            taskGateway.update(task);

            // 发布任务开始事件
            SpringDomainEventPublisher.publish(
                InspectionTaskEvent.of(this, InspectionTaskEvent.EventType.STARTED, task));

            log.info("成功将任务[{}]状态变更为进行中", id);
        }

        return success;
    }

    /**
     * 取消任务
     *
     * @param id 任务ID
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    public boolean cancelTask(Long id) {
        return taskDomainService.cancelTask(id);
    }

    /**
     * 变更任务执行人
     *
     * @param command 变更任务执行人命令
     * @return 是否成功 {@code true} 成功 {@code false} 失败
     */
    public boolean changeTaskExecutors(InspectionTaskChangeExecutorsCommand command) {

        InspectionTask task = taskGateway.getOrElseThrow(command.getTaskId())
            .changeExecutors(() -> taskAssembler.toExecutors(command.getExecutorIds()));

        // 保存任务变更
        taskGateway.update(task);

        return true;
    }

    /**
     * 监听巡检子任务已完成事件，更新巡检任务状态
     *
     * @param subTaskCompletedEvent 巡检子任务已完成事件
     */
    @TransactionalEventListener(condition = "#subTaskCompletedEvent.isCompletedType()",
        phase = TransactionPhase.BEFORE_COMMIT)
    public void onSubTaskCompleted(InspectionSubTaskEvent subTaskCompletedEvent) {
        try {
            InspectionSubTask subTask = subTaskCompletedEvent.getPayload();
            this.taskGateway.getOrElseThrow(subTask.getTaskId())
                .executeIf(InspectionTask::isUncompleted, task -> {
                    BigDecimal completionRate = this.taskGateway.getlatestCompletionRate(task.getId());
                    task.updateTaskProgress(subTask.getCompleteTime(), completionRate);

					log.info("更新巡检任务状态，任务id：{}, 完成率：{}, 状态：{}",
                        task.getId(), task.getCompletionRate(), task.getTaskStatus().getDesc());
                    this.taskGateway.update(task);

					task.executeIf(InspectionTask::isCompleted, t ->
						SpringDomainEventPublisher.publish(
							InspectionTaskEvent.of(this, InspectionTaskEvent.EventType.COMPLETED, t)));
                });
        } catch (Exception e) {
            log.error("更新巡检任务进度失败", e);
            throw e;
        }
    }


	/**
	 * 更新巡检任务状态
	 * <p>
	 * 1. 将未开始且开始时间已到的任务设置为进行中
	 * 2. 将进行中且结束时间已到的任务设置为已超时
	 * </p>
	 *
	 * @param now 当前时间
	 */
	public void updateTaskStatus(LocalDateTime now) {
		log.info("开始更新巡检任务状态，当前时间：{}", now);
		try {
			// 更新未开始的任务为进行中
			List<InspectionTask> inProgressTasks = taskDomainService.updateTasksToInProgress(now);
			if (!inProgressTasks.isEmpty()) {
				log.info("已将{}个未开始的巡检任务更新为进行中", inProgressTasks.size());
			}
        } catch (Exception e) {
            log.error("处理巡检任务状态为进行中失败", e);
            throw e;
        }

        try {
            // 更新进行中的任务为已超时
            List<InspectionTask> timeoutTasks = taskDomainService.updateTasksToTimeout(now);
            if (!timeoutTasks.isEmpty()) {
                log.info("已将{}个进行中的巡检任务更新为已超时", timeoutTasks.size());
            }
        } catch (Exception e) {
            log.error("处理巡检任务状态为已超时失败", e);
            throw e;
        }
    }

    /**
     * 监听巡检任务事件，推送任务状态变更消息
     *
     * @param event 巡检任务领域事件
     */
    @TransactionalEventListener
    public void onInspectionTaskEvent(InspectionTaskEvent event) {
        switch (event.getEventType()) {
            case STARTED:
            case TIMEOUT:
            case COMPLETED:
                pushToRelatedUser(event);
                break;
            default:
                break;
        }
    }

    /**
     * 推送消息至相关人员
     *
     * @param event 巡检任务领域事件 {@link InspectionTaskEvent}
     */
    private void pushToRelatedUser(InspectionTaskEvent event) {
        InspectionTask task = event.getPayload();
        // 获取巡检任务的执行人
        List<InspectionTaskExecutor> executors = task.getExecutors();

        String todoMessageJson = Func.toJson(InspectionTaskAssembler.toVO(task));
		if (log.isDebugEnabled()) {
			log.debug("发布巡检任务代办消息：{}", todoMessageJson);
		}
        executors.forEach(executor -> {
            log.info("推送消息，事件类型：{}，任务id：{}，推送人ID：{}",
                event.getEventType(), event.getPayload().getId(), executor.getExecutorId());
            // 推送消息
            mqSender.sendMessage(
                todoMessageJson,
			    ParkMQConstant.ROUTING_KEY_V2 + "task.todo.inspection." + executor.getExecutorId(),
			    ParkMQConstant.EXCHANGE
            );
        });
    }



}
