package com.leliven.park.application.basic.dto.command;

import com.leliven.ddd.shared.dto.SimpleLocationDTO;
import com.leliven.park.application.basic.dto.InspectionItemResultDTO;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionSubTaskJumpType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 巡检子任务完成命令
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "巡检子任务完成命令")
public class InspectionSubTaskCompleteCommand {

    /**
     * 巡检子任务ID
     */
    @NotNull(message = "巡检子任务ID不能为空")
    @ApiModelProperty(value = "巡检子任务ID")
    private Long id;

    /**
     * 跳转类型
     * 
     * @see InspectionSubTaskJumpType
     */
    @ApiModelProperty(value = "跳转类型（previous：跳转上一子任务，next：跳转下一子任务，null：不跳转）")
    private String jumpType;

    /**
     * 巡检人完成位置DTO
     */
    @ApiModelProperty(value = "巡检人完成位置")
    private SimpleLocationDTO completeLocation;

    /**
     * 巡检记录项列表
     */
    @ApiModelProperty(value = "巡检记录项列表")
    @NotEmpty(message = "巡检记录项列表不能为空")
    private List<InspectionItemResultDTO> taskItems;

}
