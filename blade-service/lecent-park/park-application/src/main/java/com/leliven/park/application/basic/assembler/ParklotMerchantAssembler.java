package com.leliven.park.application.basic.assembler;


import com.leliven.ddd.core.annotations.Assembler;
import com.leliven.park.application.basic.dto.ParklotMerchantDTO;
import com.leliven.park.domain.basic.merchant.entity.ParklotMerchantAdd;
import com.leliven.park.domain.basic.merchant.entity.ParklotMerchantUpdate;
import com.leliven.park.domain.basic.parklot.model.ParklotDomain;
import com.leliven.park.infrastructure.gateway.persistence.basic.converter.ParklotConverter;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.dataobject.ParklotMerchantDO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotMerchantNameVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotMerchantVO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotNameVO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springblade.common.utils.stream.StreamUtils;
import org.springblade.core.tool.utils.Func;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 车场商户装配器
 *
 * <AUTHOR>
 */
@Assembler
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ParklotMerchantAssembler {

	/**
	 * DTO转DO
	 */
	public static ParklotMerchantAdd toAddFromDTO(ParklotMerchantDTO dto) {
		ParklotMerchantAdd add = new ParklotMerchantAdd();
		add.setName(dto.getName());
		add.setRemark(dto.getRemark());
		add.setStatus(dto.getStatus());
		return add;
	}

	/**
	 * DTO转DO
	 */
	public static ParklotMerchantUpdate toUpdateFromDTO(ParklotMerchantDTO dto) {
		ParklotMerchantUpdate up = new ParklotMerchantUpdate();
		up.setId(dto.getId());
		up.setName(dto.getName());
		up.setRemark(dto.getRemark());
		up.setStatus(dto.getStatus());
		return up;
	}

	/**
	 * DO转DTO
	 */
	public static ParklotMerchantDTO domainObject2DTO(ParklotMerchantDO merchantDO) {
		ParklotMerchantDTO dto = new ParklotMerchantDTO();
		dto.setId(merchantDO.getId());
		dto.setName(merchantDO.getName());
		dto.setRemark(merchantDO.getRemark());
		dto.setStatus(merchantDO.getStatus());
		return dto;
	}

	/**
	 * DO转VO
	 */
	public static ParklotMerchantVO domainObject2VO(ParklotMerchantDO merchantDO, List<ParklotDomain> parklotDomains) {
		ParklotMerchantVO vo = new ParklotMerchantVO();
		vo.setId(merchantDO.getId());
		vo.setName(merchantDO.getName());
		vo.setRemark(merchantDO.getRemark());
		vo.setStatus(merchantDO.getStatus());
		List<ParklotNameVO> parklotList = StreamUtils.toList(parklotDomains, ParklotConverter::domainToNameVO);
		if (Func.isNotEmpty(parklotList)) {
			vo.setParklotIds(parklotList.stream().map(t -> t.getId().toString()).collect(Collectors.joining(",")));
		}
		return vo;
	}

	/**
	 * DO转VO
	 */
	public static ParklotMerchantNameVO domainObject2NameVO(ParklotMerchantDO parklotMerchantDO) {
		ParklotMerchantNameVO vo = new ParklotMerchantNameVO();
		vo.setId(parklotMerchantDO.getId());
		vo.setName(parklotMerchantDO.getName());
		return vo;
	}
}
