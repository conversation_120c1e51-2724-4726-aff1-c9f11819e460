package com.leliven.park.application.basic.dto.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 变更任务执行人命令
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "变更任务执行人命令", description = "变更巡检任务执行人的请求参数")
public class InspectionTaskChangeExecutorsCommand {

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "巡检任务ID", required = true)
    private Long taskId;

    /**
     * 新的执行人ID列表
     */
    @NotEmpty(message = "执行人列表不能为空")
    @ApiModelProperty(value = "新的执行人ID列表", required = true)
    private List<Long> executorIds;
} 
