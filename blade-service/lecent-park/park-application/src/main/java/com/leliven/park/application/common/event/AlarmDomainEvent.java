package com.leliven.park.application.common.event;

import com.leliven.csc.alarm.dto.AlarmEvent;
import com.leliven.ddd.core.event.AbstractDomainEvent;
import com.leliven.ddd.core.event.DomainEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车位告警领域事件
 *
 * <AUTHOR>
 */
public class AlarmDomainEvent
	extends AbstractDomainEvent<AlarmEvent, AlarmDomainEvent.EventType> {

	public AlarmDomainEvent(Object source, AlarmEvent payload) {
		super(source, null, payload);
	}

	@Getter
	@AllArgsConstructor
	public enum EventType implements DomainEventType {

		CREATED,
		;
	}
}
