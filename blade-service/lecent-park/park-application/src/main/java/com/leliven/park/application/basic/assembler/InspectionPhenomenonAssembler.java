package com.leliven.park.application.basic.assembler;

import com.leliven.park.infrastructure.gateway.persistence.basic.dto.InspectionPhenomenonForwardRuleDTO;
import com.leliven.park.application.basic.dto.command.InspectionPhenomenonAddCommand;
import com.leliven.park.application.basic.dto.command.InspectionPhenomenonEditCommand;
import com.leliven.park.domain.basic.inspection.model.InspectionPhenomenon;
import com.leliven.park.domain.basic.inspection.model.valueobject.InspectionPhenomenonForwardRule;
import com.leliven.ddd.core.annotations.Assembler;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡检现象封装器
 *
 * <AUTHOR>
 */
@Assembler
@RequiredArgsConstructor
public class InspectionPhenomenonAssembler {

    /**
	 * 添加命令转领域对象
	 *
	 * @param addCommand 添加命令
	 * @return 巡检现象领域对象
	 */
	public InspectionPhenomenon addCmd2DomainObject(InspectionPhenomenonAddCommand addCommand) {
		return InspectionPhenomenon.create(addCommand.getName(), addCommand.getCode(), addCommand.getDescription())
			.addForwardRules(PhenomenonForwardRuleAssembler.fromDTOList(addCommand.getForwardRules()))
			.addAbnormalReasonIds(addCommand.getAbnormalReasonIds());
	}

	/**
	 * 编辑命令转领域对象
	 *
	 * @param editCommand 编辑命令
	 * @return 巡检现象领域对象
	 */
	public InspectionPhenomenon editCmd2DomainObject(InspectionPhenomenonEditCommand editCommand) {
		return InspectionPhenomenon.of(editCommand.getId())
			.name(editCommand.getName())
			.code(editCommand.getCode())
			.description(editCommand.getDescription())
			.addForwardRules(PhenomenonForwardRuleAssembler.fromDTOList(editCommand.getForwardRules()))
			.addDefaultAbnormalReasonId()
			.addAbnormalReasonIds(editCommand.getAbnormalReasonIds());
	}


	@NoArgsConstructor(access = AccessLevel.PRIVATE)
	public static class PhenomenonForwardRuleAssembler {

		/**
		 * 将数据传输对象转换成领域对象
		 *
		 * @param dto 数据传输对象
		 * @return 数据对象
		 */
		public static InspectionPhenomenonForwardRule fromDTO(InspectionPhenomenonForwardRuleDTO dto) {
			return InspectionPhenomenonForwardRule.of(dto.getTargetType(), dto.getTargetId(), dto.getForceForward());
		}

		/**
		 * 将数据传输对象列表转换成领域对象列表
		 *
		 * @param dtoList 数据传输对象列表
		 * @return 领域对象列表
		 */
		public static List<InspectionPhenomenonForwardRule> fromDTOList(List<InspectionPhenomenonForwardRuleDTO> dtoList) {
			return dtoList.stream().map(PhenomenonForwardRuleAssembler::fromDTO).collect(Collectors.toList());
		}
	}
}
