package com.leliven.analysis.infra.persistence.basic.converter;

import com.leliven.analysis.domain.place.model.ParkingPlaceStatus;
import com.leliven.analysis.infra.persistence.basic.mysql.dataobject.ParkingPlaceStatusDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 停车位状态明细领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Mapper
public interface ParkingPlaceStatusConverter
    extends DomainEntityConverter<ParkingPlaceStatus, ParkingPlaceStatusDO> {

    ParkingPlaceStatusConverter INSTANCE = Mappers.getMapper(ParkingPlaceStatusConverter.class);

    @Override
    ParkingPlaceStatus fromDO(ParkingPlaceStatusDO dataObject);

    @Override
    ParkingPlaceStatusDO toDO(ParkingPlaceStatus domainObject);
}
