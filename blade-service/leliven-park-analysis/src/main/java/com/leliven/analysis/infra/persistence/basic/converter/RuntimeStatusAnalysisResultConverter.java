package com.leliven.analysis.infra.persistence.basic.converter;

import cn.hutool.core.util.BooleanUtil;
import com.leliven.analysis.domain.common.model.BasicMetricValue;
import com.leliven.analysis.domain.place.model.ParkingPlaceStatusMetric;
import com.leliven.analysis.domain.place.model.RuntimeStatusAnalysisResult;
import com.leliven.analysis.domain.place.model.valueobject.ParkingPlaceStatusMetricType;
import com.leliven.analysis.infra.persistence.basic.redis.dataobject.RuntimeStatusAnalysisResultDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 车位运行状态分析结果领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Component
public class RuntimeStatusAnalysisResultConverter
    implements DomainEntityConverter<RuntimeStatusAnalysisResult, RuntimeStatusAnalysisResultDO> {

    @Override
    public RuntimeStatusAnalysisResult fromDO(RuntimeStatusAnalysisResultDO dataObject) {
        if (Objects.isNull(dataObject)) {
            return null;
        }

        ParkingPlaceStatusMetricType metricType = ParkingPlaceStatusMetricType.resolve(dataObject.getMetricCode());
        BasicMetricValue metricValue = new BasicMetricValue(dataObject.getMetricValue())
            .normal(Objects.equals(1, dataObject.getMetricStatus()));
        ParkingPlaceStatusMetric metric = new ParkingPlaceStatusMetric(metricType, metricValue);
        return new RuntimeStatusAnalysisResult(dataObject.getParkingPlaceId(), metric, dataObject.getCount());
    }

    @Override
    public RuntimeStatusAnalysisResultDO toDO(RuntimeStatusAnalysisResult domainObject) {
        if (Objects.isNull(domainObject)) {
            return null;
        }

        RuntimeStatusAnalysisResultDO dataObject = new RuntimeStatusAnalysisResultDO();
        dataObject.setParkingPlaceId(domainObject.getParkingPlaceId());
        dataObject.setMetricCode(domainObject.getMetric().getMetricCode());
        dataObject.setMetricValue(domainObject.getMetric().getMetricValue().getValue());
        dataObject.setMetricStatus(BooleanUtil.toInteger(domainObject.getMetric().isNormal()));
        dataObject.setCount(domainObject.getCount());

        return dataObject;
    }

}
