package com.leliven.analysis.infra.persistence.basic.converter;

import com.leliven.analysis.domain.place.model.ParkingPlace;
import com.leliven.analysis.infra.persistence.basic.mysql.dataobject.ParkingPlaceDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 停车位领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Mapper
public interface ParkingPlaceConverter extends DomainEntityConverter<ParkingPlace, ParkingPlaceDO> {

    ParkingPlaceConverter INSTANCE = Mappers.getMapper(ParkingPlaceConverter.class);

    @Override
    @Mapping(target = "status", ignore = true)
    ParkingPlace fromDO(ParkingPlaceDO dataObject);

    @Override
    ParkingPlaceDO toDO(ParkingPlace domainObject);
}
