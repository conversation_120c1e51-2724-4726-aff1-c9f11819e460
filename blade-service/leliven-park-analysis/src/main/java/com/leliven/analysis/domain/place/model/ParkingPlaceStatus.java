package com.leliven.analysis.domain.place.model;

import com.leliven.ddd.core.model.TenantDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.ObjectValidator;

import java.time.Duration;
import java.util.Date;

/**
 * 车位状况领域对象
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ParkingPlaceStatus extends TenantDomain {

    /**
     * 车位ID
     */
    private Long placeId;

    /**
     * 车辆 id
     */
    private Long vehicleId;

    /**
     * 车牌
     */
    private String plate;

    /**
     * 上一次停车订单 id
     */
    private Long lastParkingOrderId;

    /**
     * 停车订单 id
     */
    private Long parkingOrderId;

    /**
     * 车场ID
     */
    private Long parklotId;

    /**
     * 进场时间
     */
    private Date enterTime;

    /**
     * 上一次离场时间
     */
    private Date lastExitTime;

    /**
     * 空闲状态 0：占用 1：空闲
     */
    private Integer ideState;

    /**
     * 车位整体状态 0：异常 1：正常
     */
    private Integer overallStatus;

    /**
     * 获取停车时长
     *
     * @param endTime 截止时间
     * @return 停车时长
     */
    public Duration getParkingDuration(Date endTime) {
        ObjectValidator.requireNonNull(endTime, "endTime must not be null");

        return null == enterTime ? Duration.ZERO : Duration.between(enterTime.toInstant(), endTime.toInstant());
    }
}
