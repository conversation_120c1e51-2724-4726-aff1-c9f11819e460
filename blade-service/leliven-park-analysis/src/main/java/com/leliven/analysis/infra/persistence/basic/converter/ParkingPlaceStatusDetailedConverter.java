package com.leliven.analysis.infra.persistence.basic.converter;

import com.leliven.analysis.domain.common.model.BasicMetricValue;
import com.leliven.analysis.domain.place.model.ParkingPlaceStatusDetailed;
import com.leliven.analysis.domain.place.model.ParkingPlaceStatusMetric;
import com.leliven.analysis.domain.place.model.valueobject.ParkingPlaceStatusMetricType;
import com.leliven.analysis.infra.persistence.basic.doris.dataobject.ParkingPlaceStatusDetailedDO;
import com.leliven.ddd.core.converter.DomainEntityConverter;
import org.springframework.stereotype.Component;

/**
 * 停车位状态明细领域对象转换器
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Component
public class ParkingPlaceStatusDetailedConverter
    implements DomainEntityConverter<ParkingPlaceStatusDetailed, ParkingPlaceStatusDetailedDO> {

    @Override
    public ParkingPlaceStatusDetailed fromDO(ParkingPlaceStatusDetailedDO dataObject) {
        if (dataObject == null) {
            return null;
        }

        ParkingPlaceStatusDetailed domainObject = new ParkingPlaceStatusDetailed();
        domainObject.setParkingPlaceId(dataObject.getParkingPlaceId());
        domainObject.setParklotId(dataObject.getParklotId());
        domainObject.setTenantId(dataObject.getTenantId());
        domainObject.setCreateTime(dataObject.getCreateTime());
        domainObject.setUpdateTime(dataObject.getUpdateTime());
        domainObject.setCategory(dataObject.getCategory());

        ParkingPlaceStatusMetricType metricType = ParkingPlaceStatusMetricType.resolve(dataObject.getMetricCode());
        BasicMetricValue metricValue = new BasicMetricValue(dataObject.getMetricValue())
            .description(dataObject.getMetricValueDesc())
            .normal(dataObject.getMetricStatus() == 1);

        ParkingPlaceStatusMetric metric = new ParkingPlaceStatusMetric(metricType, metricValue);
        domainObject.setMetric(metric);

        return domainObject;
    }

    @Override
    public ParkingPlaceStatusDetailedDO toDO(ParkingPlaceStatusDetailed domainObject) {
        if (domainObject == null) {
            return null;
        }

        ParkingPlaceStatusDetailedDO dataObject = new ParkingPlaceStatusDetailedDO();
        dataObject.setParkingPlaceId(domainObject.getParkingPlaceId());
        dataObject.setParklotId(domainObject.getParklotId());
        dataObject.setTenantId(domainObject.getTenantId());
        dataObject.setCreateTime(domainObject.getCreateTime());
        dataObject.setUpdateTime(domainObject.getUpdateTime());
        dataObject.setCategory(domainObject.getCategory());

        ParkingPlaceStatusMetric metric = domainObject.getMetric();
        dataObject.setMetricCode(metric.getMetricCode());
        dataObject.setMetricValue(metric.getMetricValue().getValue());
        dataObject.setMetricValueDesc(metric.getMetricValueDescription());
        dataObject.setMetricStatus(metric.isNormal() ? 1 : 0);
        return dataObject;
    }
}
