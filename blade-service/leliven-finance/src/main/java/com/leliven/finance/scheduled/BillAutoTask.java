package com.leliven.finance.scheduled;

import com.leliven.finance.entity.BillDetail;
import com.leliven.finance.entity.BillTotal;
import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.entity.ParkBusinessOrder;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.service.ICommonService;
import com.leliven.finance.service.IMerchantReconciliationCycleService;
import com.leliven.finance.service.IReconciliationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.redis.lock.RedisLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 账单自动任务
 *
 * <AUTHOR>
 * @since 2023/5/31
 */
@Slf4j
@Component
public class BillAutoTask {

    @Resource
    private ICommonService commonService;

    @Resource
    private IMerchantReconciliationCycleService merchantReconciliationCycleService;

    @Resource
    private IReconciliationTaskService reconciliationTaskService;

    /**
     * 收集业务订单
     */
    @Scheduled(cron = "0 0 4 1/1 * ?")
    @RedisLock(value = "leliven:finance:collect:business:order:lock", waitTime = -1)
    public void collectBusinessOrder() {
        commonService.createYesterdayBill(ParkBusinessOrder.class);
    }

    /**
     * 创建对账明细
     */
    @Scheduled(cron = "0 0 7 1/1 * ?")
    @RedisLock(value = "leliven:finance:create:bill:detail:lock", waitTime = -1)
    public void createBillDetail() {
        commonService.createYesterdayBill(BillDetail.class);
    }

    /**
     * 创建对账单
     */
    @Scheduled(cron = "0 0 8 1/1 * ?")
    @RedisLock(value = "leliven:finance:create:bill:total:lock", waitTime = -1)
    public void createBillTotal() {
        commonService.createYesterdayBill(BillTotal.class);
    }

    /**
     * 执行对账任务调度
     * 每10分钟检查一次是否有需要执行的对账任务
     */
    @Scheduled(cron = "0 */10 * * * ?")
    @RedisLock(value = "leliven:finance:reconciliation:task:schedule:lock", waitTime = -1)
    public void scheduleReconciliationTasks() {
        try {
            log.info("开始执行对账任务调度检查");

            // 获取需要执行的配置
            java.util.List<MerchantReconciliationCycle> configsToExecute =
                merchantReconciliationCycleService.getConfigsToExecute(java.time.LocalDateTime.now());

            if (configsToExecute.isEmpty()) {
                log.debug("当前没有需要执行的对账任务配置");
                return;
            }

            log.info("找到 {} 个需要执行的对账任务配置", configsToExecute.size());

            for (MerchantReconciliationCycle config : configsToExecute) {
                try {
                    // 创建对账任务
                    ReconciliationTask task = reconciliationTaskService.createReconciliationTask(
                        config.getMerchantId(),
                        config.getTenantId(),
                        config.getChannel(),
                        java.time.LocalDate.now().minusDays(1), // 对账昨天的数据
                        config.getCycleType(),
                        java.time.LocalDate.now().minusDays(1),
                        java.time.LocalDate.now().minusDays(1)
                    );

                    if (task != null) {
                        log.info("成功创建对账任务: taskId={}, merchantId={}, channel={}",
                            task.getId(), config.getMerchantId(), config.getChannel());

                        // 异步执行对账任务
                        reconciliationTaskService.executeReconciliationTask(task.getId());

                        // 更新配置的执行时间
                        java.time.LocalDateTime nextExecuteTime = merchantReconciliationCycleService
                            .calculateNextExecuteTime(config, java.time.LocalDateTime.now());
                        merchantReconciliationCycleService.updateExecuteTime(
                            config.getId(),
                            java.time.LocalDateTime.now(),
                            nextExecuteTime
                        );

                        // 重置失败次数
                        merchantReconciliationCycleService.resetFailureCount(config.getId());

                    } else {
                        log.warn("创建对账任务失败: merchantId={}, channel={}",
                            config.getMerchantId(), config.getChannel());

                        // 增加失败次数
                        merchantReconciliationCycleService.incrementFailureCount(config.getId());
                    }

                } catch (Exception e) {
                    log.error("处理对账任务配置失败: configId={}, merchantId={}, channel={}",
                        config.getId(), config.getMerchantId(), config.getChannel(), e);

                    // 增加失败次数
                    merchantReconciliationCycleService.incrementFailureCount(config.getId());
                }
            }

            log.info("对账任务调度检查完成，处理了 {} 个配置", configsToExecute.size());

        } catch (Exception e) {
            log.error("对账任务调度执行异常", e);
        }
    }

    /**
     * 清理过期的对账任务
     * 每天凌晨2点执行，清理30天前的对账任务
     */
    @Scheduled(cron = "0 0 2 1/1 * ?")
    @RedisLock(value = "leliven:finance:cleanup:expired:tasks:lock", waitTime = -1)
    public void cleanupExpiredTasks() {
        try {
            log.info("开始清理过期的对账任务");

            Integer deletedCount = reconciliationTaskService.deleteExpiredTasks(30);

            log.info("清理过期对账任务完成，删除了 {} 个任务", deletedCount);

        } catch (Exception e) {
            log.error("清理过期对账任务异常", e);
        }
    }
}
