package com.leliven.finance.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.lecent.park.entity.Parklot;
import com.lecent.park.feign.IParkClient;
import com.lecent.payment.feign.IPaymentClient;
import com.leliven.finance.constant.BillConstant;
import com.leliven.finance.entity.*;
import com.leliven.finance.enums.BillHandleStatus;
import com.leliven.finance.enums.BillStatus;
import com.leliven.finance.enums.ReconciliationTaskStatus;
import com.leliven.finance.excel.BaseBillExcel;
import com.leliven.finance.mapper.BillSettleMapper;
import com.leliven.finance.mapper.BillTotalMapper;
import com.leliven.finance.service.*;
import com.leliven.finance.util.BillCommonUtil;
import com.leliven.finance.vo.BillSettleVO;
import com.leliven.finance.vo.BillTable;
import com.leliven.finance.vo.BillTotalVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springblade.common.excel.CustomCellStyleStrategy;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateTimeUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用服务
 *
 * <AUTHOR>
 * @since 2023/6/5
 */
@Slf4j
@Service
public class CommonServiceImpl implements ICommonService {

    @Resource
    private IParkClient parkClient;

    @Resource
    private BillSettleMapper billSettleMapper;

    @Lazy
    @Resource
    private IBillSettleService billSettleService;

    @Lazy
    @Resource
    private IBillSplittingTotalService billSplittingTotalService;

    @Lazy
    @Resource
    private IBillSplittingDetailService billSplittingDetailService;

    @Lazy
    @Resource
    private IBillTotalService billTotalService;

    @Lazy
    @Resource
    private IBillDetailService billDetailService;

    @Lazy
    @Resource
    private IParkBusinessOrderService parkBusinessOrderService;

    @Resource
    private IPaymentClient paymentClient;

    @Resource
    private BillTotalMapper billTotalMapper;

    @Override
    public BillTable buildBillTable(String date) {
        return buildBillTable(null, date);
    }

    @Override
    public BillTable buildBillTable(List<Long> billTotalIds, String date) {
        return buildBillTable(billTotalIds, date, null);
    }

    @Override
    public BillTable buildBillTable(List<Long> billTotalIds, String date, List<? extends BaseBill> bills) {
        BillTable billTable = new BillTable();

        // 设置账单数据
        billTable.setBills(bills);

        //设置币种和单位
        billTable.setCurrency(BillConstant.CURRENCY_YUAN);
        billTable.setPriceUnit(BillConstant.PRICE_UNIT_YUAN);

        // 设置编制单位
        billTable.setMakeUnit(AuthUtil.getUserName());

        if (Func.isNotEmpty(billTotalIds)) {
            // 设置车场名称
            List<Long> parklotIds = billTotalService.queryParklotIds(billTotalIds);
            List<Parklot> parklotList = BillCommonUtil.data(parkClient.queryParklotByIds(parklotIds));
            if (Func.isNotEmpty(parklotList)) {
                String parklotName = parklotList.stream().map(Parklot::getName).collect(Collectors.joining());
                billTable.setParklotName(parklotName);
            }

            // 设置统计时段
            String timePeriods = billTotalService.getTimePeriods(billTotalIds);
            billTable.setTimePeriods(timePeriods);
        }

        billTable.setDate(date);

        return billTable;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <B extends BaseBill> Boolean updateBillStatus(B bill, BillStatus billStatus) {
        LecentAssert.notNull(bill, "账单不能为空");
        LecentAssert.notNull(bill.getId(), "账单id不能为空");
        LecentAssert.notNull(billStatus, "账单状态不能为空");

        Class<? extends BaseBill> clazz = bill.getClass();

        boolean result = false;

        if (clazz.isAssignableFrom(BillSettle.class)) {
            result = updateBillSettleStatus((BillSettle) bill, billStatus);
        } else if (clazz.isAssignableFrom(BillSplittingTotal.class)) {
            result = updateBillSplittingTotalStatus((BillSplittingTotal) bill, billStatus);
        } else if (clazz.isAssignableFrom(BillSplittingDetail.class)) {
            result = updateBillSplittingDetailStatus((BillSplittingDetail) bill, billStatus);
        } else if (clazz.isAssignableFrom(BillTotal.class)) {
            result = updateBillTotalStatus((BillTotal) bill, billStatus);
        } else if (clazz.isAssignableFrom(BillDetail.class)) {
            result = updateBillDetailStatus((BillDetail) bill, billStatus);
        }
        return result;
    }

    @Override
    public <B extends BaseBill> Boolean updateBillStatus(List<B> bills, BillStatus billStatus) {
        boolean result = false;
        if (Func.isNotEmpty(bills)) {
            for (B bill : bills) {
                result = updateBillStatus(bill, billStatus);
                if (!result) {
                    break;
                }
            }
        }
        return result;
    }

    private boolean updateBillSettleStatus(BillSettle bill, BillStatus billStatus) {
        BillSettleVO settle = billSettleMapper.selectBillSettleById(bill.getId());
        LecentAssert.notNull(settle, "未查询到结算单");

        LecentAssert.isFalse(settle.getBillStatus().equals(billStatus.getValue()), "结算单{}，请勿重复操作", billStatus.getDesc());

        if (BillStatus.PAID.equals(billStatus)) {
            Integer count = billSplittingTotalService.lambdaQuery()
                    .eq(BillSplittingTotal::getBillSettleId, settle.getId())
                    .eq(BillSplittingTotal::getBillStatus, BillStatus.UNPAID.getValue())
                    .count();
            if (count > 0) {
                return false;
            }
        }

        settle.setBillStatus(billStatus.getValue());

        boolean result = billSettleService.updateById(settle);

        if (result && !BillStatus.PAID.equals(billStatus)) {
            updateBillStatus(settle.getBillSplittingTotals(), billStatus);
        }

        return result;
    }

    private boolean updateBillSplittingTotalStatus(BillSplittingTotal bill, BillStatus billStatus) {
        BillSplittingTotal total = billSplittingTotalService.getById(bill.getId());
        LecentAssert.notNull(total, "未查询到分账信息");

        LecentAssert.isFalse(total.getBillStatus().equals(billStatus.getValue()), "分账单{}，请勿重复操作", billStatus.getDesc());

        if (BillStatus.PAID.equals(billStatus)) {
            LecentAssert.notBlank(bill.getProof(), "支付凭证不能为空");
            total.setProof(bill.getProof());
        } else if (BillStatus.CANCEL.equals(billStatus) && total.getBillStatus().equals(BillStatus.PAID.getValue())) {
            throw new ServiceException("已支付的账单不允许撤销");
        }

        total.setBillStatus(billStatus.getValue());

        boolean result = billSplittingTotalService.updateById(total);
        LecentAssert.isTrue(result, "分账单更新失败");

        List<BillSplittingDetail> list = billSplittingDetailService.lambdaQuery()
                .eq(BillSplittingDetail::getBillSplittingTotalId, total.getId())
                .list();
        if (Func.isNotEmpty(list)) {
            updateBillStatus(list, billStatus);
        }

        if (BillStatus.PAID.equals(billStatus)) {
            BillSettle settle = billSettleService.getById(total.getBillSettleId());
            if (settle != null) {
                updateBillStatus(settle, billStatus);
            }
        }

        return true;
    }

    private boolean updateBillSplittingDetailStatus(BillSplittingDetail bill, BillStatus billStatus) {
        BillSplittingDetail detail = billSplittingDetailService.getById(bill.getId());
        LecentAssert.notNull(detail, "未查询到分账信息");

        LecentAssert.isFalse(detail.getBillStatus().equals(billStatus.getValue()), "分账明细{}，请勿重复操作", billStatus.getDesc());

        if (BillStatus.CANCEL.equals(billStatus) && detail.getBillStatus().equals(BillStatus.PAID.getValue())) {
            throw new ServiceException("已支付的账单不允许撤销");
        }

        detail.setBillStatus(billStatus.getValue());

        boolean result = billSplittingDetailService.updateById(detail);
        LecentAssert.isTrue(result, "分账明细更新失败");

        List<BillTotal> list = billTotalService.lambdaQuery().eq(BillTotal::getId, detail.getBillTotalId()).list();

        if (BillStatus.CANCEL.equals(billStatus) && Func.isNotEmpty(list)) {
            updateBillStatus(list, BillStatus.FINISH);
        }

        return true;
    }

    private boolean updateBillTotalStatus(BillTotal bill, BillStatus billStatus) {
        BillTotal total = billTotalService.getById(bill.getId());
        LecentAssert.notNull(total, "未查询到对账单信息");

        if (!total.getBillStatus().equals(billStatus.getValue())) {
            total.setBillStatus(billStatus.getValue());
            boolean result = billTotalService.updateById(total);
            LecentAssert.isTrue(result, "对账单更新失败");

            boolean needUpdateDetail = BillStatus.WAIT.equals(billStatus) || BillStatus.FINISH.equals(billStatus);
            if (needUpdateDetail) {
                List<BillDetail> list = billDetailService.lambdaQuery().in(BillDetail::getBillTotalId, bill.getId()).list();
                updateBillStatus(list, billStatus);
            }
        }

        return true;
    }

    private boolean updateBillDetailStatus(BillDetail bill, BillStatus billStatus) {
        BillDetail billDetail = billDetailService.getById(bill.getId());
        LecentAssert.notNull(billDetail, "未查询到对账明细");

        boolean update = billDetailService.lambdaUpdate()
                .set(BillDetail::getBillStatus, billStatus.getValue())
                .set(BillStatus.WAIT.equals(billStatus), BillDetail::getBillTotalId, null)
                .set(BillStatus.FINISH.equals(billStatus), BillDetail::getBillTotalId, bill.getBillTotalId())
                .eq(BillDetail::getId, bill.getId()).update();
        LecentAssert.isTrue(update, "对账明细更新失败");

        return true;
    }

    @Override
    public Boolean createHistoryBill(Class<?> clazz) {
//        List<ConfigEntity> merchantList = BillCommonUtil.data(paymentClient.getConfigAutomaticList());
//
//        if (merchantList != null) {
//            ICommonService service = SpringUtil.getBean(ICommonService.class);
//
//            for (ConfigEntity configEntity : merchantList) {
//                if (Func.isBlank(configEntity.getMerchantId())) {
//                    continue;
//                }
//
//                service.createBill(configEntity.getMerchantId(), configEntity.getTenantId(), clazz);
//            }
//        }

        return true;
    }

    @Override
    public Boolean createYesterdayBill(Class<?> clazz) {
//        List<ConfigEntity> merchantList = BillCommonUtil.data(paymentClient.getConfigAutomaticList());
//
//        if (merchantList != null) {
//            ICommonService service = SpringUtil.getBean(ICommonService.class);
//
//            LocalDate yesterday = LocalDate.now().minusDays(1);
//
//            for (ConfigEntity configEntity : merchantList) {
//                if (Func.isBlank(configEntity.getMerchantId())) {
//                    continue;
//                }
//
//                try {
//                    service.createBill(configEntity.getMerchantId(), configEntity.getTenantId(), yesterday, clazz);
//                } catch (Exception e) {
//                    log.error("[创建昨日账单] 商户[{}]创建失败:", configEntity.getMerchantId(), e);
//                }
//            }
//        }

        return true;
    }

    @Override
    public Boolean createBill(LocalDate startDate, LocalDate endDate, Class<?> clazz) {
//        List<ConfigEntity> merchantList = BillCommonUtil.data(paymentClient.getConfigAutomaticList());
//
//        if (merchantList != null) {
//            ICommonService service = SpringUtil.getBean(ICommonService.class);
//
//            for (ConfigEntity configEntity : merchantList) {
//                if (Func.isBlank(configEntity.getMerchantId())) {
//                    continue;
//                }
//
//                service.createBill(configEntity.getMerchantId(), configEntity.getTenantId(), startDate, endDate, clazz);
//            }
//        }

        return true;
    }

    @Override
    public Boolean createBill(String merchantId, String tenantId, Class<?> clazz) {
        return createBill(merchantId, tenantId, DateTimeUtil.parseDate("2023-01-01"), LocalDate.now(), clazz);
    }

    @Override
    public Boolean createBill(String merchantId, String tenantId, LocalDate startDate, LocalDate endDate, Class<?> clazz) {
        ICommonService service = SpringUtil.getBean(ICommonService.class);
        while (endDate.isAfter(startDate)) {
            try {
                service.createBill(merchantId, tenantId, startDate, clazz);
            } catch (Exception e) {
                log.error("[创建商户账单] 商户[{}]在[{}]创建失败:", merchantId, startDate, e);
            }
            startDate = startDate.plusDays(1);
        }
        return true;
    }

    @Override
    public Boolean createBill(String merchantId, String tenantId, LocalDate date, Class<?> clazz) {
        boolean result = false;
        if (clazz.isAssignableFrom(ParkBusinessOrder.class)) {
            result = parkBusinessOrderService.syncParkOrder(merchantId, date);
        } else if (clazz.isAssignableFrom(BillDetail.class)) {
            result = billDetailService.createBillDetail(merchantId, tenantId, date);
        } else if (clazz.isAssignableFrom(BillTotal.class)) {
            BillTotalVO totalVO = new BillTotalVO();
            totalVO.setDate(date);
            totalVO.setMerchantId(merchantId);
            totalVO.setTenantId(tenantId);
            result = billTotalService.createBillTotal(totalVO);
        }
        return result;
    }

    @Override
    public Boolean updateBill(Long id, Class<?> clazz) {
        boolean result = false;
        if (clazz.isAssignableFrom(BillTotal.class)) {
            BillTotal total = billTotalService.getById(id);
            LecentAssert.notNull(total, "未查询到对账单信息");

            if (total.getBillStatus().equals(BillStatus.SPLITTING.getValue())) {
                throw new ServiceException(StringUtil.format("商户[{}]在[{}]的对账单已分账不能修改", total.getMerchantId(), total.getDate()));
            }

            BillTotal billTotal = billTotalMapper.countBillDetailByBillTotalId(total.getId());
            billTotal.setId(total.getId());

            Integer count = billDetailService.lambdaQuery()
                    .eq(BillDetail::getBillTotalId, total.getId())
                    .eq(BillDetail::getStatus, 0)
                    .eq(BillDetail::getHandlerStatus, BillHandleStatus.WAIT.getValue())
                    .count();

            billTotal.setStatus(count > 0 ? 0 : 1);
            billTotal.setBillStatus(BillStatus.FINISH.getValue());

            result = billTotalService.updateById(billTotal);
        }
        return result;
    }

    @Override
    public void export(HttpServletResponse response, BillTable billTable) {
        String parklotName = Func.isNotEmpty(billTable.getParklotName()) ? billTable.getParklotName() : "";
        String timePeriods = Func.isNotEmpty(billTable.getTimePeriods()) ? billTable.getTimePeriods() : "";
        String title = billTable.getTitle();

        List<? extends BaseBillExcel> excels = billTable.getBillExcel();
        Class<? extends BaseBillExcel> clazz = excels.get(0).getClass();
        int column = clazz.getDeclaredFields().length;

        BillCommonUtil.excelFileName(response, title + "-" + System.currentTimeMillis());

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter writer = EasyExcelFactory.write(outputStream).build();

            // 创建sheet
            WriteSheet sheet = new WriteSheet();
            sheet.setSheetNo(0);
            sheet.setSheetName(title);

            // 写入表头
            WriteTable table = new WriteTable();
            table.setTableNo(1);
            table.setHead(BillCommonUtil.tableTitle(billTable, column));
            table.setCustomWriteHandlerList(Collections.singletonList(new CustomCellStyleStrategy()
                    .columnWidth(16)
                    .createContentStyle()
                    .fillPatternType(FillPatternType.SOLID_FOREGROUND)
                    .fillForegroundColor(IndexedColors.PALE_BLUE.getIndex())
                    .fontSize((short) 14)
                    .bold(true)
                    .createHeadStyle()));

            writer.write(new ArrayList<>(), sheet, table);


            // 写入数据
            table = new WriteTable();
            table.setTableNo(2);
            table.setClazz(clazz);
            table.setCustomWriteHandlerList(Collections.singletonList(new CustomCellStyleStrategy()
                    .columnWidth(16)
                    .createContentStyle()
                    .fillPatternType(FillPatternType.SOLID_FOREGROUND)
                    .fillForegroundColor(IndexedColors.PALE_BLUE.getIndex())
                    .fontSize((short) 14)
                    .bold(true)
                    .mergeCellIndex(Collections.singletonList(Arrays.asList(billTable.getStartCellIndex(), billTable.getEndCellIndex())))
                    .rowNum(excels.size())
                    .createHeadStyle()));

            writer.write(excels, sheet, table);

            // 写入备注
            if (Boolean.TRUE.equals(billTable.getNeedRemark())) {
                table = new WriteTable();
                table.setTableNo(3);
                table.setHead(BillCommonUtil.remark(column, parklotName, timePeriods));
                table.setCustomWriteHandlerList(Collections.singletonList(new CustomCellStyleStrategy()
                        .fontColor(IndexedColors.RED.getIndex())
                        .createHeadStyle()));

                writer.write(new ArrayList<>(), sheet, table);
            }

            writer.finish();
        } catch (IOException e) {
            log.info("[导出财务报表] excel导出错误:", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createBillByTask(ReconciliationTask task) {
        LecentAssert.notNull(task, "对账任务不能为空");

        try {
            // 根据任务信息创建账单
            Boolean result = createBill(
                task.getMerchantId(),
                task.getTenantId(),
                task.getCycleStartDate(),
                task.getCycleEndDate(),
                BillTotal.class
            );

            if (result) {
                log.info("基于对账任务创建账单成功: taskId={}, merchantId={}", task.getId(), task.getMerchantId());
            } else {
                log.warn("基于对账任务创建账单失败: taskId={}, merchantId={}", task.getId(), task.getMerchantId());
            }

            return result;
        } catch (Exception e) {
            log.error("基于对账任务创建账单异常: taskId={}", task.getId(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBillByTask(ReconciliationTask task) {
        LecentAssert.notNull(task, "对账任务不能为空");
        LecentAssert.notNull(task.getBillTotalId(), "关联的对账单ID不能为空");

        try {
            // 更新关联的对账单
            Boolean result = updateBill(task.getBillTotalId(), BillTotal.class);

            if (result) {
                log.info("基于对账任务更新账单成功: taskId={}, billTotalId={}", task.getId(), task.getBillTotalId());
            } else {
                log.warn("基于对账任务更新账单失败: taskId={}, billTotalId={}", task.getId(), task.getBillTotalId());
            }

            return result;
        } catch (Exception e) {
            log.error("基于对账任务更新账单异常: taskId={}", task.getId(), e);
            return false;
        }
    }

    @Override
    public BillTable getBillTableByTask(ReconciliationTask task) {
        LecentAssert.notNull(task, "对账任务不能为空");

        try {
            // 根据任务的周期日期构建账单表
            String dateStr = DateTimeUtil.formatDate(task.getReconciliationDate());

            if (task.getBillTotalId() != null) {
                // 如果有关联的对账单ID，使用该ID构建账单表
                return buildBillTable(Collections.singletonList(task.getBillTotalId()), dateStr);
            } else {
                // 否则根据日期构建账单表
                return buildBillTable(dateStr);
            }
        } catch (Exception e) {
            log.error("获取对账任务的账单数据异常: taskId={}", task.getId(), e);
            return new BillTable();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeTaskProcessing(Long taskId, Integer reconciliationResult) {
        LecentAssert.notNull(taskId, "任务ID不能为空");
        LecentAssert.notNull(reconciliationResult, "对账结果不能为空");

        try {
            // 获取对账任务服务
            IReconciliationTaskService reconciliationTaskService = SpringUtil.getBean(IReconciliationTaskService.class);

            // 获取任务信息
            ReconciliationTask task = reconciliationTaskService.getById(taskId);
            if (task == null) {
                log.warn("对账任务不存在: taskId={}", taskId);
                return false;
            }

            // 完成对账任务
            Boolean result = reconciliationTaskService.completeReconciliationTask(
                taskId,
                reconciliationResult,
                task.getBillTotalId()
            );

            if (result) {
                log.info("完成对账任务处理成功: taskId={}, result={}", taskId, reconciliationResult);
            } else {
                log.warn("完成对账任务处理失败: taskId={}, result={}", taskId, reconciliationResult);
            }

            return result;
        } catch (Exception e) {
            log.error("完成对账任务处理异常: taskId={}", taskId, e);
            return false;
        }
    }
}
