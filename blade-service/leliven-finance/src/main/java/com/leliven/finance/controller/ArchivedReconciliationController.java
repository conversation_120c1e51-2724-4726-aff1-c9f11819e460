package com.leliven.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.finance.entity.BillTotal;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.enums.BillStatus;
import com.leliven.finance.enums.ReconciliationTaskStatus;
import com.leliven.finance.service.IBillTotalService;
import com.leliven.finance.service.ICommonService;
import com.leliven.finance.service.IReconciliationTaskService;
import com.leliven.finance.vo.BillTable;
import com.leliven.finance.vo.BillTotalVO;
import com.leliven.finance.vo.ReconciliationTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 已归档对账单控制器
 * 展示已完成处理的对账单
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/archived-reconciliation")
@Api(value = "已归档对账单", tags = "已归档对账单接口")
public class ArchivedReconciliationController extends BladeController {

    private final IBillTotalService billTotalService;
    private final IReconciliationTaskService reconciliationTaskService;
    private final ICommonService commonService;

    /**
     * 分页查询已归档对账单
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询已归档对账单", notes = "传入BillTotalVO")
    public R<IPage<BillTotalVO>> list(BillTotalVO billTotal, Query query) {
        // 只查询已对账或已分账的对账单
        if (billTotal.getBillStatus() == null) {
            billTotal.setBillStatuses(Arrays.asList(
                BillStatus.RECONCILED.getValue(),
                BillStatus.SETTLED.getValue()
            ));
        }
        IPage<BillTotalVO> pages = billTotalService.selectBillTotalPage(Condition.getPage(query), billTotal);
        return R.data(pages);
    }

    /**
     * 分页查询已完成的对账任务
     */
    @GetMapping("/tasks")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页查询已完成对账任务", notes = "传入ReconciliationTaskVO")
    public R<IPage<ReconciliationTaskVO>> tasks(ReconciliationTaskVO reconciliationTask, Query query) {
        // 只查询已完成的任务
        if (reconciliationTask.getTaskStatus() == null) {
            reconciliationTask.setTaskStatuses(Arrays.asList(
                ReconciliationTaskStatus.COMPLETED.getValue()
            ));
        }
        IPage<ReconciliationTaskVO> pages = reconciliationTaskService.selectReconciliationTaskPage(Condition.getPage(query), reconciliationTask);
        return R.data(pages);
    }

    /**
     * 查看已归档对账单详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "对账单详情", notes = "传入对账单id")
    public R<BillTotal> detail(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        BillTotal detail = billTotalService.getById(id);
        return R.data(detail);
    }

    /**
     * 查看对账任务详情
     */
    @GetMapping("/task-detail/{id}")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "对账任务详情", notes = "传入任务id")
    public R<ReconciliationTask> taskDetail(@ApiParam(value = "任务id", required = true) @PathVariable Long id) {
        ReconciliationTask detail = reconciliationTaskService.getById(id);
        return R.data(detail);
    }

    /**
     * 获取对账单的账单表数据
     */
    @GetMapping("/bill-table/{id}")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "获取账单表数据", notes = "传入对账单id")
    public R<BillTable> getBillTable(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        BillTotal billTotal = billTotalService.getById(id);
        if (billTotal == null) {
            return R.fail("对账单不存在");
        }
        
        BillTable billTable = commonService.buildBillTable(
            Arrays.asList(id), 
            billTotal.getBillDate().toString()
        );
        return R.data(billTable);
    }

    /**
     * 导出已归档对账单
     */
    @GetMapping("/export/{id}")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "导出对账单", notes = "传入对账单id")
    public void export(@ApiParam(value = "对账单id", required = true) @PathVariable Long id,
                      HttpServletResponse response) {
        BillTotal billTotal = billTotalService.getById(id);
        if (billTotal == null) {
            return;
        }
        
        BillTable billTable = commonService.buildBillTable(
            Arrays.asList(id), 
            billTotal.getBillDate().toString()
        );
        
        commonService.export(response, billTable);
    }

    /**
     * 批量导出已归档对账单
     */
    @PostMapping("/batch-export")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "批量导出对账单", notes = "传入对账单id列表")
    public void batchExport(@ApiParam(value = "对账单id列表", required = true) @RequestBody List<Long> ids,
                           @ApiParam(value = "导出日期") @RequestParam(required = false) String date,
                           HttpServletResponse response) {
        if (date == null) {
            date = LocalDate.now().toString();
        }
        
        BillTable billTable = commonService.buildBillTable(ids, date);
        commonService.export(response, billTable);
    }

    /**
     * 获取归档统计信息
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "获取归档统计", notes = "传入查询条件")
    public R<ArchivedStatistics> statistics(@ApiParam(value = "开始日期") @RequestParam(required = false) LocalDate startDate,
                                          @ApiParam(value = "结束日期") @RequestParam(required = false) LocalDate endDate,
                                          @ApiParam(value = "商户ID") @RequestParam(required = false) String merchantId,
                                          @ApiParam(value = "渠道") @RequestParam(required = false) Integer channel) {
        ArchivedStatistics statistics = new ArchivedStatistics();
        
        // 设置默认查询时间范围（最近30天）
        if (startDate == null) {
            startDate = LocalDate.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        // 统计已归档对账单数量
        long archivedBillCount = billTotalService.lambdaQuery()
            .in(BillTotal::getBillStatus, Arrays.asList(
                BillStatus.RECONCILED.getValue(),
                BillStatus.SETTLED.getValue()
            ))
            .ge(BillTotal::getBillDate, startDate)
            .le(BillTotal::getBillDate, endDate)
            .eq(merchantId != null, BillTotal::getMerchantId, merchantId)
            .eq(channel != null, BillTotal::getChannel, channel)
            .count();
        statistics.setArchivedBillCount(archivedBillCount);
        
        // 统计已完成任务数量
        long completedTaskCount = reconciliationTaskService.lambdaQuery()
            .eq(ReconciliationTask::getTaskStatus, ReconciliationTaskStatus.COMPLETED.getValue())
            .ge(ReconciliationTask::getReconciliationDate, startDate)
            .le(ReconciliationTask::getReconciliationDate, endDate)
            .eq(merchantId != null, ReconciliationTask::getMerchantId, merchantId)
            .eq(channel != null, ReconciliationTask::getChannel, channel)
            .count();
        statistics.setCompletedTaskCount(completedTaskCount);
        
        // 统计平账数量
        long balancedTaskCount = reconciliationTaskService.lambdaQuery()
            .eq(ReconciliationTask::getTaskStatus, ReconciliationTaskStatus.COMPLETED.getValue())
            .eq(ReconciliationTask::getReconciliationResult, 1)
            .ge(ReconciliationTask::getReconciliationDate, startDate)
            .le(ReconciliationTask::getReconciliationDate, endDate)
            .eq(merchantId != null, ReconciliationTask::getMerchantId, merchantId)
            .eq(channel != null, ReconciliationTask::getChannel, channel)
            .count();
        statistics.setBalancedTaskCount(balancedTaskCount);
        
        // 计算平账率
        if (completedTaskCount > 0) {
            statistics.setBalanceRate((double) balancedTaskCount / completedTaskCount * 100);
        } else {
            statistics.setBalanceRate(0.0);
        }
        
        // 统计今日归档数量
        long todayArchivedCount = billTotalService.lambdaQuery()
            .in(BillTotal::getBillStatus, Arrays.asList(
                BillStatus.RECONCILED.getValue(),
                BillStatus.SETTLED.getValue()
            ))
            .ge(BillTotal::getUpdateTime, LocalDate.now().atStartOfDay())
            .eq(merchantId != null, BillTotal::getMerchantId, merchantId)
            .eq(channel != null, BillTotal::getChannel, channel)
            .count();
        statistics.setTodayArchivedCount(todayArchivedCount);
        
        return R.data(statistics);
    }

    /**
     * 按月份统计归档数据
     */
    @GetMapping("/monthly-statistics")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "按月份统计", notes = "传入年份")
    public R<List<MonthlyStatistics>> monthlyStatistics(@ApiParam(value = "年份") @RequestParam(required = false) Integer year) {
        if (year == null) {
            year = LocalDate.now().getYear();
        }
        
        // TODO: 实现按月份统计的具体逻辑
        // 这里需要调用相应的Mapper方法来获取月度统计数据
        
        return R.data(Arrays.asList()); // 返回空列表，实际实现时需要填充数据
    }

    /**
     * 重新归档对账单
     */
    @PostMapping("/re-archive/{id}")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "重新归档", notes = "传入对账单id")
    public R<Boolean> reArchive(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        Boolean result = commonService.updateBillStatus(
            billTotalService.getById(id), 
            BillStatus.RECONCILED
        );
        return R.status(result);
    }

    /**
     * 归档统计信息
     */
    public static class ArchivedStatistics {
        private Long archivedBillCount;
        private Long completedTaskCount;
        private Long balancedTaskCount;
        private Double balanceRate;
        private Long todayArchivedCount;

        // Getters and Setters
        public Long getArchivedBillCount() { return archivedBillCount; }
        public void setArchivedBillCount(Long archivedBillCount) { this.archivedBillCount = archivedBillCount; }
        public Long getCompletedTaskCount() { return completedTaskCount; }
        public void setCompletedTaskCount(Long completedTaskCount) { this.completedTaskCount = completedTaskCount; }
        public Long getBalancedTaskCount() { return balancedTaskCount; }
        public void setBalancedTaskCount(Long balancedTaskCount) { this.balancedTaskCount = balancedTaskCount; }
        public Double getBalanceRate() { return balanceRate; }
        public void setBalanceRate(Double balanceRate) { this.balanceRate = balanceRate; }
        public Long getTodayArchivedCount() { return todayArchivedCount; }
        public void setTodayArchivedCount(Long todayArchivedCount) { this.todayArchivedCount = todayArchivedCount; }
    }

    /**
     * 月度统计信息
     */
    public static class MonthlyStatistics {
        private Integer month;
        private Long archivedCount;
        private Long balancedCount;
        private Double balanceRate;

        // Getters and Setters
        public Integer getMonth() { return month; }
        public void setMonth(Integer month) { this.month = month; }
        public Long getArchivedCount() { return archivedCount; }
        public void setArchivedCount(Long archivedCount) { this.archivedCount = archivedCount; }
        public Long getBalancedCount() { return balancedCount; }
        public void setBalancedCount(Long balancedCount) { this.balancedCount = balancedCount; }
        public Double getBalanceRate() { return balanceRate; }
        public void setBalanceRate(Double balanceRate) { this.balanceRate = balanceRate; }
    }
}
