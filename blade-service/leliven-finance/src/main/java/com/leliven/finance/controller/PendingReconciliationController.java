package com.leliven.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.finance.entity.BillDetail;
import com.leliven.finance.entity.BillTotal;
import com.leliven.finance.enums.BillHandleMethod;
import com.leliven.finance.enums.BillHandleStatus;
import com.leliven.finance.enums.BillStatus;
import com.leliven.finance.service.IBillDetailService;
import com.leliven.finance.service.IBillTotalService;
import com.leliven.finance.service.ICommonService;
import com.leliven.finance.vo.BillDetailVO;
import com.leliven.finance.vo.BillTotalVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 待处理对账单控制器
 * 展示需要人工干预和处理的对账单
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/pending-reconciliation")
@Api(value = "待处理对账单", tags = "待处理对账单接口")
public class PendingReconciliationController extends BladeController {

    private final IBillTotalService billTotalService;
    private final IBillDetailService billDetailService;
    private final ICommonService commonService;

    /**
     * 分页查询待处理对账单
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询待处理对账单", notes = "传入BillTotalVO")
    public R<IPage<BillTotalVO>> list(BillTotalVO billTotal, Query query) {
        // 只查询不平账的对账单
        billTotal.setBillStatus(BillStatus.UNRECONCILED.getValue());
        IPage<BillTotalVO> pages = billTotalService.selectBillTotalPage(Condition.getPage(query), billTotal);
        return R.data(pages);
    }

    /**
     * 查询待处理对账明细
     */
    @GetMapping("/details")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "查询待处理对账明细", notes = "传入BillDetailVO")
    public R<IPage<BillDetailVO>> details(BillDetailVO billDetail, Query query) {
        // 只查询不平账且未处理的明细
        billDetail.setBillStatus(BillStatus.UNRECONCILED.getValue());
        billDetail.setHandlerStatus(BillHandleStatus.UNHANDLED.getValue());
        IPage<BillDetailVO> pages = billDetailService.selectBillDetailPage(Condition.getPage(query), billDetail);
        return R.data(pages);
    }

    /**
     * 查看对账单详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "对账单详情", notes = "传入对账单id")
    public R<BillTotal> detail(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        BillTotal detail = billTotalService.getById(id);
        return R.data(detail);
    }

    /**
     * 查看对账明细详情
     */
    @GetMapping("/detail-item/{id}")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "对账明细详情", notes = "传入明细id")
    public R<BillDetail> detailItem(@ApiParam(value = "明细id", required = true) @PathVariable Long id) {
        BillDetail detail = billDetailService.getById(id);
        return R.data(detail);
    }

    /**
     * 处理对账明细 - 修正日期
     */
    @PostMapping("/handle-correct-date/{id}")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修正日期", notes = "传入明细id和正确日期")
    public R<Boolean> handleCorrectDate(@ApiParam(value = "明细id", required = true) @PathVariable Long id,
                                      @ApiParam(value = "正确日期", required = true) @RequestParam String correctDate) {
        BillDetail billDetail = new BillDetail();
        billDetail.setId(id);
        billDetail.setHandlerStatus(BillHandleStatus.HANDLED.getValue());
        billDetail.setHandlerMethod(BillHandleMethod.CORRECT_DATE.getValue());
        billDetail.setProcessStatus(2); // 已完成
        billDetail.setProcessTime(LocalDateTime.now());
        billDetail.setProcessorName(AuthUtil.getUserName());
        billDetail.setHandlerRemark("修正日期为: " + correctDate);
        
        Boolean result = billDetailService.updateById(billDetail);
        return R.status(result);
    }

    /**
     * 处理对账明细 - 删除记录
     */
    @PostMapping("/handle-delete/{id}")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "删除记录", notes = "传入明细id和删除原因")
    public R<Boolean> handleDelete(@ApiParam(value = "明细id", required = true) @PathVariable Long id,
                                 @ApiParam(value = "删除原因") @RequestParam(required = false) String reason) {
        BillDetail billDetail = new BillDetail();
        billDetail.setId(id);
        billDetail.setHandlerStatus(BillHandleStatus.HANDLED.getValue());
        billDetail.setHandlerMethod(BillHandleMethod.DELETE.getValue());
        billDetail.setProcessStatus(2); // 已完成
        billDetail.setProcessTime(LocalDateTime.now());
        billDetail.setProcessorName(AuthUtil.getUserName());
        billDetail.setHandlerRemark("删除原因: " + (reason != null ? reason : "无"));
        
        Boolean result = billDetailService.updateById(billDetail);
        return R.status(result);
    }

    /**
     * 处理对账明细 - 忽略记录
     */
    @PostMapping("/handle-ignore/{id}")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "忽略记录", notes = "传入明细id和忽略原因")
    public R<Boolean> handleIgnore(@ApiParam(value = "明细id", required = true) @PathVariable Long id,
                                 @ApiParam(value = "忽略原因") @RequestParam(required = false) String reason) {
        BillDetail billDetail = new BillDetail();
        billDetail.setId(id);
        billDetail.setHandlerStatus(BillHandleStatus.HANDLED.getValue());
        billDetail.setHandlerMethod(BillHandleMethod.IGNORE.getValue());
        billDetail.setProcessStatus(2); // 已完成
        billDetail.setProcessTime(LocalDateTime.now());
        billDetail.setProcessorName(AuthUtil.getUserName());
        billDetail.setHandlerRemark("忽略原因: " + (reason != null ? reason : "无"));
        
        Boolean result = billDetailService.updateById(billDetail);
        return R.status(result);
    }

    /**
     * 批量处理对账明细
     */
    @PostMapping("/batch-handle")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "批量处理", notes = "传入处理信息")
    public R<Boolean> batchHandle(@Valid @RequestBody BatchHandleRequest request) {
        boolean allSuccess = true;
        
        for (Long id : request.getIds()) {
            try {
                BillDetail billDetail = new BillDetail();
                billDetail.setId(id);
                billDetail.setHandlerStatus(BillHandleStatus.HANDLED.getValue());
                billDetail.setHandlerMethod(request.getHandleMethod());
                billDetail.setProcessStatus(2); // 已完成
                billDetail.setProcessTime(LocalDateTime.now());
                billDetail.setProcessorName(AuthUtil.getUserName());
                billDetail.setHandlerRemark(request.getRemark());
                
                Boolean result = billDetailService.updateById(billDetail);
                if (!result) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                allSuccess = false;
            }
        }
        
        return R.status(allSuccess);
    }

    /**
     * 标记对账单为已处理
     */
    @PostMapping("/mark-processed/{id}")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "标记已处理", notes = "传入对账单id")
    public R<Boolean> markProcessed(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        Boolean result = commonService.updateBillStatus(
            billTotalService.getById(id), 
            BillStatus.RECONCILED
        );
        return R.status(result);
    }

    /**
     * 重新对账
     */
    @PostMapping("/re-reconcile/{id}")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "重新对账", notes = "传入对账单id")
    public R<Boolean> reReconcile(@ApiParam(value = "对账单id", required = true) @PathVariable Long id) {
        Boolean result = commonService.updateBill(id, BillTotal.class);
        return R.status(result);
    }

    /**
     * 获取待处理统计信息
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "获取待处理统计", notes = "无参数")
    public R<PendingStatistics> statistics() {
        PendingStatistics statistics = new PendingStatistics();
        
        // 统计待处理对账单数量
        long pendingBillCount = billTotalService.lambdaQuery()
            .eq(BillTotal::getBillStatus, BillStatus.UNRECONCILED.getValue())
            .count();
        statistics.setPendingBillCount(pendingBillCount);
        
        // 统计待处理明细数量
        long pendingDetailCount = billDetailService.lambdaQuery()
            .eq(BillDetail::getBillStatus, BillStatus.UNRECONCILED.getValue())
            .eq(BillDetail::getHandlerStatus, BillHandleStatus.UNHANDLED.getValue())
            .count();
        statistics.setPendingDetailCount(pendingDetailCount);
        
        // 统计今日新增待处理数量
        long todayPendingCount = billTotalService.lambdaQuery()
            .eq(BillTotal::getBillStatus, BillStatus.UNRECONCILED.getValue())
            .ge(BillTotal::getCreateTime, LocalDateTime.now().toLocalDate().atStartOfDay())
            .count();
        statistics.setTodayPendingCount(todayPendingCount);
        
        return R.data(statistics);
    }

    /**
     * 批量处理请求对象
     */
    public static class BatchHandleRequest {
        private List<Long> ids;
        private Integer handleMethod;
        private String remark;

        // Getters and Setters
        public List<Long> getIds() { return ids; }
        public void setIds(List<Long> ids) { this.ids = ids; }
        public Integer getHandleMethod() { return handleMethod; }
        public void setHandleMethod(Integer handleMethod) { this.handleMethod = handleMethod; }
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }

    /**
     * 待处理统计信息
     */
    public static class PendingStatistics {
        private Long pendingBillCount;
        private Long pendingDetailCount;
        private Long todayPendingCount;

        // Getters and Setters
        public Long getPendingBillCount() { return pendingBillCount; }
        public void setPendingBillCount(Long pendingBillCount) { this.pendingBillCount = pendingBillCount; }
        public Long getPendingDetailCount() { return pendingDetailCount; }
        public void setPendingDetailCount(Long pendingDetailCount) { this.pendingDetailCount = pendingDetailCount; }
        public Long getTodayPendingCount() { return todayPendingCount; }
        public void setTodayPendingCount(Long todayPendingCount) { this.todayPendingCount = todayPendingCount; }
    }
}
