package com.leliven.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.vo.ReconciliationTaskVO;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tenant.annotation.TenantIgnore;

import java.time.LocalDate;
import java.util.List;

/**
 * 对账任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface ReconciliationTaskMapper extends BaseMapper<ReconciliationTask> {

    /**
     * 自定义分页查询对账任务
     *
     * @param page 分页对象
     * @param reconciliationTask 查询条件
     * @return 分页结果
     */
    List<ReconciliationTaskVO> selectReconciliationTaskPage(IPage<ReconciliationTaskVO> page, @Param("reconciliationTask") ReconciliationTaskVO reconciliationTask);

    /**
     * 根据商户和渠道查询最新的对账任务
     *
     * @param merchantId 商户ID
     * @param channel 对账渠道
     * @return 最新的对账任务
     */
    @TenantIgnore
    ReconciliationTask selectLatestTaskByMerchantAndChannel(@Param("merchantId") String merchantId, @Param("channel") Integer channel);

    /**
     * 查询指定状态的对账任务
     *
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    @TenantIgnore
    List<ReconciliationTask> selectTasksByStatus(@Param("taskStatus") Integer taskStatus);

    /**
     * 查询需要重试的对账任务
     *
     * @return 需要重试的任务列表
     */
    @TenantIgnore
    List<ReconciliationTask> selectTasksNeedRetry();

    /**
     * 获取对账任务统计信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param merchantId 商户ID（可选）
     * @param channel 渠道（可选）
     * @return 统计信息
     */
    @TenantIgnore
    ReconciliationTaskVO selectTaskStatistics(@Param("startDate") LocalDate startDate, 
                                             @Param("endDate") LocalDate endDate, 
                                             @Param("merchantId") String merchantId, 
                                             @Param("channel") Integer channel);

    /**
     * 删除过期的对账任务
     *
     * @param expireDays 过期天数
     * @return 删除数量
     */
    @TenantIgnore
    Integer deleteExpiredTasks(@Param("expireDays") Integer expireDays);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param taskStatus 新状态
     * @return 更新数量
     */
    @TenantIgnore
    Integer batchUpdateTaskStatus(@Param("taskIds") List<Long> taskIds, @Param("taskStatus") Integer taskStatus);
}
