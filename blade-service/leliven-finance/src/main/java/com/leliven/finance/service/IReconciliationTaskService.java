package com.leliven.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.vo.ReconciliationTaskVO;
import org.springblade.core.mp.base.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * 对账任务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IReconciliationTaskService extends BaseService<ReconciliationTask> {

    /**
     * 自定义分页查询对账任务
     *
     * @param page 分页对象
     * @param reconciliationTask 查询条件
     * @return 分页结果
     */
    IPage<ReconciliationTaskVO> selectReconciliationTaskPage(IPage<ReconciliationTaskVO> page, ReconciliationTaskVO reconciliationTask);

    /**
     * 创建对账任务
     *
     * @param merchantId 商户ID
     * @param tenantId 租户ID
     * @param channel 对账渠道
     * @param reconciliationDate 对账日期
     * @param cycleType 周期类型
     * @param cycleStartDate 周期开始日期
     * @param cycleEndDate 周期结束日期
     * @return 创建的任务
     */
    ReconciliationTask createReconciliationTask(String merchantId, String tenantId, Integer channel, 
                                               LocalDate reconciliationDate, Integer cycleType, 
                                               LocalDate cycleStartDate, LocalDate cycleEndDate);

    /**
     * 执行对账任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    Boolean executeReconciliationTask(Long taskId);

    /**
     * 批量执行对账任务
     *
     * @param taskIds 任务ID列表
     * @return 执行结果
     */
    Boolean batchExecuteReconciliationTask(List<Long> taskIds);

    /**
     * 取消对账任务
     *
     * @param taskId 任务ID
     * @param reason 取消原因
     * @return 取消结果
     */
    Boolean cancelReconciliationTask(Long taskId, String reason);

    /**
     * 重试失败的对账任务
     *
     * @param taskId 任务ID
     * @return 重试结果
     */
    Boolean retryReconciliationTask(Long taskId);

    /**
     * 根据商户和渠道查询最新的对账任务
     *
     * @param merchantId 商户ID
     * @param channel 对账渠道
     * @return 最新的对账任务
     */
    ReconciliationTask getLatestTaskByMerchantAndChannel(String merchantId, Integer channel);

    /**
     * 查询指定状态的对账任务
     *
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    List<ReconciliationTask> getTasksByStatus(Integer taskStatus);

    /**
     * 查询需要重试的对账任务
     *
     * @return 需要重试的任务列表
     */
    List<ReconciliationTask> getTasksNeedRetry();

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param taskStatus 新状态
     * @param failureReason 失败原因（可选）
     * @return 更新结果
     */
    Boolean updateTaskStatus(Long taskId, Integer taskStatus, String failureReason);

    /**
     * 完成对账任务
     *
     * @param taskId 任务ID
     * @param reconciliationResult 对账结果
     * @param billTotalId 关联的对账单ID
     * @return 完成结果
     */
    Boolean completeReconciliationTask(Long taskId, Integer reconciliationResult, Long billTotalId);

    /**
     * 获取对账任务统计信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param merchantId 商户ID（可选）
     * @param channel 渠道（可选）
     * @return 统计信息
     */
    ReconciliationTaskVO getTaskStatistics(LocalDate startDate, LocalDate endDate, String merchantId, Integer channel);

    /**
     * 删除过期的对账任务
     *
     * @param expireDays 过期天数
     * @return 删除数量
     */
    Integer deleteExpiredTasks(Integer expireDays);
}
