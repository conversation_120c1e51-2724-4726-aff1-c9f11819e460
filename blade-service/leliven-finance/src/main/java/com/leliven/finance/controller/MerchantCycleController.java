package com.leliven.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.service.IMerchantReconciliationCycleService;
import com.leliven.finance.vo.MerchantReconciliationCycleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户对账周期管理控制器
 * 配置每个商户的对账周期
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/merchant-cycle")
@Api(value = "商户对账周期管理", tags = "商户对账周期管理接口")
public class MerchantCycleController extends BladeController {

    private final IMerchantReconciliationCycleService merchantReconciliationCycleService;

    /**
     * 分页查询商户对账周期配置
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询", notes = "传入MerchantReconciliationCycleVO")
    public R<IPage<MerchantReconciliationCycleVO>> list(MerchantReconciliationCycleVO merchantReconciliationCycle, Query query) {
        IPage<MerchantReconciliationCycleVO> pages = merchantReconciliationCycleService.selectMerchantReconciliationCyclePage(Condition.getPage(query), merchantReconciliationCycle);
        return R.data(pages);
    }

    /**
     * 查看配置详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "详情", notes = "传入id")
    public R<MerchantReconciliationCycle> detail(@ApiParam(value = "主键id", required = true) @RequestParam Long id) {
        MerchantReconciliationCycle detail = merchantReconciliationCycleService.getById(id);
        return R.data(detail);
    }

    /**
     * 新增或修改配置
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "新增或修改", notes = "传入MerchantReconciliationCycle")
    public R submit(@Valid @RequestBody MerchantReconciliationCycle merchantReconciliationCycle) {
        Boolean result = merchantReconciliationCycleService.saveOrUpdateMerchantCycle(merchantReconciliationCycle);
        return R.status(result);
    }

    /**
     * 删除配置
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(merchantReconciliationCycleService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 启用或禁用配置
     */
    @PostMapping("/toggle-status/{id}")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "启用或禁用配置", notes = "传入id和状态")
    public R<Boolean> toggleStatus(@ApiParam(value = "配置id", required = true) @PathVariable Long id,
                                 @ApiParam(value = "是否启用", required = true) @RequestParam Boolean enabled) {
        Boolean result = merchantReconciliationCycleService.enableOrDisableConfig(id, enabled);
        return R.status(result);
    }

    /**
     * 批量启用或禁用配置
     */
    @PostMapping("/batch-toggle-status")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "批量启用或禁用", notes = "传入id列表和状态")
    public R<Boolean> batchToggleStatus(@ApiParam(value = "配置id列表", required = true) @RequestBody List<Long> ids,
                                      @ApiParam(value = "是否启用", required = true) @RequestParam Boolean enabled) {
        Boolean result = merchantReconciliationCycleService.batchEnableOrDisable(ids, enabled);
        return R.status(result);
    }

    /**
     * 根据商户ID查询配置
     */
    @GetMapping("/by-merchant/{merchantId}")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "按商户查询", notes = "传入商户ID")
    public R<List<MerchantReconciliationCycle>> getByMerchant(@ApiParam(value = "商户ID", required = true) @PathVariable String merchantId) {
        List<MerchantReconciliationCycle> configs = merchantReconciliationCycleService.getByMerchantId(merchantId);
        return R.data(configs);
    }

    /**
     * 根据渠道查询配置
     */
    @GetMapping("/by-channel/{channel}")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "按渠道查询", notes = "传入渠道")
    public R<List<MerchantReconciliationCycle>> getByChannel(@ApiParam(value = "渠道", required = true) @PathVariable Integer channel) {
        List<MerchantReconciliationCycle> configs = merchantReconciliationCycleService.getByChannel(channel);
        return R.data(configs);
    }

    /**
     * 查询所有启用的配置
     */
    @GetMapping("/enabled")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "查询启用的配置", notes = "无参数")
    public R<List<MerchantReconciliationCycle>> getEnabled() {
        List<MerchantReconciliationCycle> configs = merchantReconciliationCycleService.getEnabledConfigs();
        return R.data(configs);
    }

    /**
     * 查询需要执行的配置
     */
    @GetMapping("/to-execute")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "查询需要执行的配置", notes = "传入当前时间")
    public R<List<MerchantReconciliationCycle>> getToExecute(@ApiParam(value = "当前时间") @RequestParam(required = false) LocalDateTime currentTime) {
        if (currentTime == null) {
            currentTime = LocalDateTime.now();
        }
        List<MerchantReconciliationCycle> configs = merchantReconciliationCycleService.getConfigsToExecute(currentTime);
        return R.data(configs);
    }

    /**
     * 测试配置有效性
     */
    @PostMapping("/validate")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "验证配置", notes = "传入配置信息")
    public R<Boolean> validate(@Valid @RequestBody MerchantReconciliationCycle config) {
        Boolean result = merchantReconciliationCycleService.validateConfig(config);
        return R.status(result);
    }

    /**
     * 计算下次执行时间
     */
    @PostMapping("/calculate-next-time")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "计算下次执行时间", notes = "传入配置信息和基准时间")
    public R<LocalDateTime> calculateNextTime(@Valid @RequestBody MerchantReconciliationCycle config,
                                            @ApiParam(value = "基准时间") @RequestParam(required = false) LocalDateTime baseTime) {
        if (baseTime == null) {
            baseTime = LocalDateTime.now();
        }
        LocalDateTime nextTime = merchantReconciliationCycleService.calculateNextExecuteTime(config, baseTime);
        return R.data(nextTime);
    }

    /**
     * 重置失败次数
     */
    @PostMapping("/reset-failure-count/{id}")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "重置失败次数", notes = "传入配置id")
    public R<Boolean> resetFailureCount(@ApiParam(value = "配置id", required = true) @PathVariable Long id) {
        Boolean result = merchantReconciliationCycleService.resetFailureCount(id);
        return R.status(result);
    }

    /**
     * 手动更新执行时间
     */
    @PostMapping("/update-execute-time/{id}")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "更新执行时间", notes = "传入配置id和时间")
    public R<Boolean> updateExecuteTime(@ApiParam(value = "配置id", required = true) @PathVariable Long id,
                                      @ApiParam(value = "最后执行时间") @RequestParam(required = false) LocalDateTime lastExecuteTime,
                                      @ApiParam(value = "下次执行时间") @RequestParam(required = false) LocalDateTime nextExecuteTime) {
        Boolean result = merchantReconciliationCycleService.updateExecuteTime(id, lastExecuteTime, nextExecuteTime);
        return R.status(result);
    }

    /**
     * 复制配置到其他商户
     */
    @PostMapping("/copy-config/{sourceId}")
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "复制配置", notes = "传入源配置id和目标商户列表")
    public R<Boolean> copyConfig(@ApiParam(value = "源配置id", required = true) @PathVariable Long sourceId,
                               @ApiParam(value = "目标商户ID列表", required = true) @RequestBody List<String> targetMerchantIds) {
        Boolean result = merchantReconciliationCycleService.copyConfigToMerchants(sourceId, targetMerchantIds);
        return R.status(result);
    }

    /**
     * 删除商户的所有配置
     */
    @PostMapping("/delete-by-merchant/{merchantId}")
    @ApiOperationSupport(order = 16)
    @ApiOperation(value = "删除商户配置", notes = "传入商户ID")
    public R<Boolean> deleteByMerchant(@ApiParam(value = "商户ID", required = true) @PathVariable String merchantId) {
        Boolean result = merchantReconciliationCycleService.deleteByMerchantId(merchantId);
        return R.status(result);
    }

    /**
     * 获取配置统计信息
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 17)
    @ApiOperation(value = "获取统计信息", notes = "无参数")
    public R<MerchantReconciliationCycleVO> statistics() {
        MerchantReconciliationCycleVO statistics = merchantReconciliationCycleService.getConfigStatistics();
        return R.data(statistics);
    }

    /**
     * 根据商户和渠道查询配置
     */
    @GetMapping("/by-merchant-channel")
    @ApiOperationSupport(order = 18)
    @ApiOperation(value = "按商户和渠道查询", notes = "传入商户ID和渠道")
    public R<MerchantReconciliationCycle> getByMerchantAndChannel(@ApiParam(value = "商户ID", required = true) @RequestParam String merchantId,
                                                                @ApiParam(value = "渠道", required = true) @RequestParam Integer channel) {
        MerchantReconciliationCycle config = merchantReconciliationCycleService.getByMerchantAndChannel(merchantId, channel);
        return R.data(config);
    }

    /**
     * 批量创建默认配置
     */
    @PostMapping("/create-default-configs")
    @ApiOperationSupport(order = 19)
    @ApiOperation(value = "批量创建默认配置", notes = "传入商户ID列表")
    public R<Boolean> createDefaultConfigs(@ApiParam(value = "商户ID列表", required = true) @RequestBody List<String> merchantIds) {
        boolean allSuccess = true;
        
        for (String merchantId : merchantIds) {
            try {
                // 为每个商户创建默认的微信对账配置
                MerchantReconciliationCycle defaultConfig = new MerchantReconciliationCycle();
                defaultConfig.setMerchantId(merchantId);
                defaultConfig.setMerchantName(""); // 需要根据merchantId查询商户名称
                defaultConfig.setChannel(1); // 微信
                defaultConfig.setCycleType(1); // 日
                defaultConfig.setCycleValue(1);
                defaultConfig.setExecuteTime(java.time.LocalTime.of(8, 0));
                defaultConfig.setAutoReconciliation(true);
                defaultConfig.setStartDate(java.time.LocalDate.now());
                defaultConfig.setEnabled(true);
                defaultConfig.setMaxRetryCount(3);
                defaultConfig.setDescription("系统默认配置");
                
                Boolean result = merchantReconciliationCycleService.saveOrUpdateMerchantCycle(defaultConfig);
                if (!result) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                allSuccess = false;
            }
        }
        
        return R.status(allSuccess);
    }
}
