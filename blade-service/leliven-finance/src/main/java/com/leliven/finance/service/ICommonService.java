package com.leliven.finance.service;

import com.leliven.finance.entity.BaseBill;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.enums.BillStatus;
import com.leliven.finance.vo.BillTable;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/5
 */
public interface ICommonService {

    /**
     * 建立账单表
     *
     * @param date         日期
     * @return {@link BillTable}
     */
    BillTable buildBillTable(String date);

    /**
     * 建立账单表
     *
     * @param billTotalIds 对账单ids
     * @param date         日期
     * @return {@link BillTable}
     */
    BillTable buildBillTable(List<Long> billTotalIds, String date);

    /**
     * 建立账单表
     *
     * @param billTotalIds 对账单ids
     * @param date         日期
     * @param bills        账单数据
     * @return {@link BillTable}
     */
    BillTable buildBillTable(List<Long> billTotalIds, String date, List<? extends BaseBill> bills);

    /**
     * 更新账单状态
     *
     * @param bill       账单
     * @param billStatus 账单状态
     * @return {@link Boolean}
     */
    <B extends BaseBill> Boolean updateBillStatus(B bill, BillStatus billStatus);

    /**
     * 更新账单状态
     *
     * @param bills      账单列表
     * @param billStatus 账单状态
     * @return {@link Boolean}
     */
    <B extends BaseBill> Boolean updateBillStatus(List<B> bills, BillStatus billStatus);

    /**
     * 创造历史账单
     *
     * @param clazz clazz
     * @return {@link Boolean}
     */
    Boolean createHistoryBill(Class<?> clazz);

    /**
     * 创建昨日账单
     *
     * @param clazz clazz
     * @return {@link Boolean}
     */
    Boolean createYesterdayBill(Class<?> clazz);

    /**
     * 创建账单
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param clazz      clazz
     * @return {@link Boolean}
     */
    Boolean createBill(LocalDate startDate, LocalDate endDate, Class<?> clazz);

    /**
     * 创建账单
     *
     * @param merchantId 商户id
     * @param tenantId   租户id
     * @param clazz      clazz
     * @return {@link Boolean}
     */
    Boolean createBill(String merchantId, String tenantId, Class<?> clazz);

    /**
     * 创建账单
     *
     * @param merchantId 商户id
     * @param tenantId   租户id
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param clazz      clazz
     * @return {@link Boolean}
     */
    Boolean createBill(String merchantId, String tenantId, LocalDate startDate, LocalDate endDate, Class<?> clazz);

    /**
     * 创建账单
     *
     * @param merchantId 商户id
     * @param tenantId   租户id
     * @param date       日期
     * @param clazz      clazz
     * @return {@link Boolean}
     */
    Boolean createBill(String merchantId, String tenantId, LocalDate date, Class<?> clazz);

    /**
     * 更新账单
     *
     * @param id    id
     * @param clazz clazz
     * @return {@link Boolean}
     */
    Boolean updateBill(Long id, Class<?> clazz);

    /**
     * 导出
     *
     * @param response  响应
     * @param billTable 账单
     */
    void export(HttpServletResponse response, BillTable billTable);

    /**
     * 基于对账任务创建账单
     *
     * @param task 对账任务
     * @return 创建结果
     */
    Boolean createBillByTask(ReconciliationTask task);

    /**
     * 基于对账任务更新账单
     *
     * @param task 对账任务
     * @return 更新结果
     */
    Boolean updateBillByTask(ReconciliationTask task);

    /**
     * 获取对账任务的账单数据
     *
     * @param task 对账任务
     * @return 账单表数据
     */
    BillTable getBillTableByTask(ReconciliationTask task);

    /**
     * 完成对账任务处理
     *
     * @param taskId 任务ID
     * @param reconciliationResult 对账结果
     * @return 处理结果
     */
    Boolean completeTaskProcessing(Long taskId, Integer reconciliationResult);
}
