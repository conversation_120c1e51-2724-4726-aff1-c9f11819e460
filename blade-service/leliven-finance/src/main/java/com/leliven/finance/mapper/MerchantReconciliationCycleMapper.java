package com.leliven.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.vo.MerchantReconciliationCycleVO;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.datascope.annotation.DataScope;
import org.springblade.core.tenant.annotation.TenantIgnore;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户对账周期配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface MerchantReconciliationCycleMapper extends BaseMapper<MerchantReconciliationCycle> {

    /**
     * 自定义分页查询商户对账周期配置
     *
     * @param page 分页对象
     * @param merchantReconciliationCycle 查询条件
     * @return 分页结果
     */
    @DataScope(deptColumn = "create_dept", userColumn = "create_user")
    List<MerchantReconciliationCycleVO> selectMerchantReconciliationCyclePage(IPage<MerchantReconciliationCycleVO> page, @Param("merchantReconciliationCycle") MerchantReconciliationCycleVO merchantReconciliationCycle);

    /**
     * 根据商户ID和渠道查询配置
     *
     * @param merchantId 商户ID
     * @param channel 渠道
     * @return 配置信息
     */
    @TenantIgnore
    MerchantReconciliationCycle selectByMerchantAndChannel(@Param("merchantId") String merchantId, @Param("channel") Integer channel);

    /**
     * 查询所有启用的配置
     *
     * @return 启用的配置列表
     */
    @TenantIgnore
    List<MerchantReconciliationCycle> selectEnabledConfigs();

    /**
     * 查询需要执行的配置（到达执行时间）
     *
     * @param currentTime 当前时间
     * @return 需要执行的配置列表
     */
    @TenantIgnore
    List<MerchantReconciliationCycle> selectConfigsToExecute(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新启用状态
     *
     * @param ids 配置ID列表
     * @param enabled 是否启用
     * @return 更新数量
     */
    @TenantIgnore
    Integer batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 根据商户ID查询所有配置
     *
     * @param merchantId 商户ID
     * @return 配置列表
     */
    @TenantIgnore
    List<MerchantReconciliationCycle> selectByMerchantId(@Param("merchantId") String merchantId);

    /**
     * 根据渠道查询所有配置
     *
     * @param channel 渠道
     * @return 配置列表
     */
    @TenantIgnore
    List<MerchantReconciliationCycle> selectByChannel(@Param("channel") Integer channel);

    /**
     * 删除商户的所有配置
     *
     * @param merchantId 商户ID
     * @return 删除数量
     */
    @TenantIgnore
    Integer deleteByMerchantId(@Param("merchantId") String merchantId);

    /**
     * 更新执行时间
     *
     * @param id 配置ID
     * @param lastExecuteTime 最后执行时间
     * @param nextExecuteTime 下次执行时间
     * @return 更新数量
     */
    @TenantIgnore
    Integer updateExecuteTime(@Param("id") Long id, @Param("lastExecuteTime") LocalDateTime lastExecuteTime, @Param("nextExecuteTime") LocalDateTime nextExecuteTime);

    /**
     * 增加失败次数
     *
     * @param id 配置ID
     * @return 更新数量
     */
    @TenantIgnore
    Integer incrementFailureCount(@Param("id") Long id);

    /**
     * 重置失败次数
     *
     * @param id 配置ID
     * @return 更新数量
     */
    @TenantIgnore
    Integer resetFailureCount(@Param("id") Long id);

    /**
     * 获取配置统计信息
     *
     * @return 统计信息
     */
    @TenantIgnore
    MerchantReconciliationCycleVO selectConfigStatistics();
}
