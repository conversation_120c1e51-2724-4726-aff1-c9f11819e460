package com.leliven.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.enums.BillChannel;
import com.leliven.finance.enums.ReconciliationCycleType;
import com.leliven.finance.enums.ReconciliationTaskStatus;
import com.leliven.finance.mapper.ReconciliationTaskMapper;
import com.leliven.finance.service.IReconciliationTaskService;
import com.leliven.finance.vo.ReconciliationTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateTimeUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 对账任务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class ReconciliationTaskServiceImpl extends BaseServiceImpl<ReconciliationTaskMapper, ReconciliationTask> implements IReconciliationTaskService {

    @Override
    public IPage<ReconciliationTaskVO> selectReconciliationTaskPage(IPage<ReconciliationTaskVO> page, ReconciliationTaskVO reconciliationTask) {
        List<ReconciliationTaskVO> list = baseMapper.selectReconciliationTaskPage(page, reconciliationTask);
        
        // 填充扩展字段
        for (ReconciliationTaskVO vo : list) {
            fillExtendedFields(vo);
        }
        
        return page.setRecords(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReconciliationTask createReconciliationTask(String merchantId, String tenantId, Integer channel, 
                                                      LocalDate reconciliationDate, Integer cycleType, 
                                                      LocalDate cycleStartDate, LocalDate cycleEndDate) {
        LecentAssert.notBlank(merchantId, "商户ID不能为空");
        LecentAssert.notNull(channel, "对账渠道不能为空");
        LecentAssert.notNull(reconciliationDate, "对账日期不能为空");
        LecentAssert.notNull(cycleType, "周期类型不能为空");
        LecentAssert.notNull(cycleStartDate, "周期开始日期不能为空");
        LecentAssert.notNull(cycleEndDate, "周期结束日期不能为空");

        // 检查是否已存在相同的任务
        ReconciliationTask existingTask = lambdaQuery()
                .eq(ReconciliationTask::getMerchantId, merchantId)
                .eq(ReconciliationTask::getChannel, channel)
                .eq(ReconciliationTask::getReconciliationDate, reconciliationDate)
                .eq(ReconciliationTask::getCycleStartDate, cycleStartDate)
                .eq(ReconciliationTask::getCycleEndDate, cycleEndDate)
                .one();
        
        if (existingTask != null) {
            log.warn("对账任务已存在: merchantId={}, channel={}, date={}", merchantId, channel, reconciliationDate);
            return existingTask;
        }

        // 创建新任务
        ReconciliationTask task = new ReconciliationTask();
        task.setMerchantId(merchantId);
        task.setTenantId(tenantId);
        task.setChannel(channel);
        task.setReconciliationDate(reconciliationDate);
        task.setCycleType(cycleType);
        task.setCycleStartDate(cycleStartDate);
        task.setCycleEndDate(cycleEndDate);
        
        // 生成任务名称
        String channelName = BillChannel.getTitleByValue(channel);
        String taskName = DateTimeUtil.formatDate(reconciliationDate) + channelName + "对账单";
        task.setTaskName(taskName);
        
        // 生成账单名称
        ReconciliationCycleType cycleTypeEnum = ReconciliationCycleType.getByValue(cycleType);
        String billName = generateBillName(reconciliationDate, cycleTypeEnum, channelName);
        task.setBillName(billName);
        
        // 设置初始状态
        task.setTaskStatus(ReconciliationTaskStatus.CREATING.getValue());
        task.setTaskCreateTime(LocalDateTime.now());
        task.setReconciliationResult(0); // 默认不平
        
        // 初始化统计字段
        task.setThirdPartyTotalAmount(java.math.BigDecimal.ZERO);
        task.setThirdPartyRefundAmount(java.math.BigDecimal.ZERO);
        task.setThirdPartyTotalCount(0);
        task.setThirdPartyRefundCount(0);
        task.setPlatformTotalAmount(java.math.BigDecimal.ZERO);
        task.setPlatformRefundAmount(java.math.BigDecimal.ZERO);
        task.setPlatformTotalCount(0);
        task.setPlatformRefundCount(0);

        save(task);
        log.info("创建对账任务成功: taskId={}, taskName={}", task.getId(), task.getTaskName());
        
        return task;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeReconciliationTask(Long taskId) {
        LecentAssert.notNull(taskId, "任务ID不能为空");
        
        ReconciliationTask task = getById(taskId);
        LecentAssert.notNull(task, "对账任务不存在");
        
        // 检查任务状态
        ReconciliationTaskStatus currentStatus = ReconciliationTaskStatus.getByValue(task.getTaskStatus());
        if (currentStatus != null && currentStatus.isFinalStatus()) {
            log.warn("任务已完成，无需重复执行: taskId={}, status={}", taskId, currentStatus.getDesc());
            return true;
        }
        
        try {
            // 更新任务状态为执行中
            updateTaskStatus(taskId, ReconciliationTaskStatus.EXECUTING.getValue(), null);
            
            // TODO: 实现具体的对账逻辑
            // 1. 获取第三方支付数据
            // 2. 获取本平台数据
            // 3. 进行数据对比
            // 4. 生成对账结果
            
            log.info("对账任务执行成功: taskId={}", taskId);
            return true;
            
        } catch (Exception e) {
            log.error("对账任务执行失败: taskId={}", taskId, e);
            updateTaskStatus(taskId, ReconciliationTaskStatus.EXECUTION_FAILED.getValue(), e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean batchExecuteReconciliationTask(List<Long> taskIds) {
        if (Func.isEmpty(taskIds)) {
            return true;
        }
        
        boolean allSuccess = true;
        for (Long taskId : taskIds) {
            try {
                Boolean result = executeReconciliationTask(taskId);
                if (!result) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("批量执行对账任务失败: taskId={}", taskId, e);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReconciliationTask(Long taskId, String reason) {
        LecentAssert.notNull(taskId, "任务ID不能为空");
        
        ReconciliationTask task = getById(taskId);
        LecentAssert.notNull(task, "对账任务不存在");
        
        // 检查任务状态是否可以取消
        ReconciliationTaskStatus currentStatus = ReconciliationTaskStatus.getByValue(task.getTaskStatus());
        if (currentStatus != null && currentStatus.isFinalStatus()) {
            log.warn("任务已完成，无法取消: taskId={}, status={}", taskId, currentStatus.getDesc());
            return false;
        }
        
        return updateTaskStatus(taskId, ReconciliationTaskStatus.CANCELLED.getValue(), reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean retryReconciliationTask(Long taskId) {
        LecentAssert.notNull(taskId, "任务ID不能为空");
        
        ReconciliationTask task = getById(taskId);
        LecentAssert.notNull(task, "对账任务不存在");
        
        // 检查任务状态是否可以重试
        ReconciliationTaskStatus currentStatus = ReconciliationTaskStatus.getByValue(task.getTaskStatus());
        if (currentStatus == null || !currentStatus.canRetry()) {
            log.warn("任务状态不支持重试: taskId={}, status={}", taskId, currentStatus != null ? currentStatus.getDesc() : "未知");
            return false;
        }
        
        // 重置任务状态并重新执行
        updateTaskStatus(taskId, ReconciliationTaskStatus.CREATING.getValue(), null);
        return executeReconciliationTask(taskId);
    }

    @Override
    public ReconciliationTask getLatestTaskByMerchantAndChannel(String merchantId, Integer channel) {
        return baseMapper.selectLatestTaskByMerchantAndChannel(merchantId, channel);
    }

    @Override
    public List<ReconciliationTask> getTasksByStatus(Integer taskStatus) {
        return baseMapper.selectTasksByStatus(taskStatus);
    }

    @Override
    public List<ReconciliationTask> getTasksNeedRetry() {
        return baseMapper.selectTasksNeedRetry();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTaskStatus(Long taskId, Integer taskStatus, String failureReason) {
        ReconciliationTask task = new ReconciliationTask();
        task.setId(taskId);
        task.setTaskStatus(taskStatus);
        task.setFailureReason(failureReason);
        
        // 如果是完成状态，设置完成时间
        ReconciliationTaskStatus status = ReconciliationTaskStatus.getByValue(taskStatus);
        if (status != null && status.isFinalStatus()) {
            task.setTaskCompleteTime(LocalDateTime.now());
        }
        
        return updateById(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeReconciliationTask(Long taskId, Integer reconciliationResult, Long billTotalId) {
        ReconciliationTask task = new ReconciliationTask();
        task.setId(taskId);
        task.setTaskStatus(ReconciliationTaskStatus.COMPLETED.getValue());
        task.setReconciliationResult(reconciliationResult);
        task.setBillTotalId(billTotalId);
        task.setTaskCompleteTime(LocalDateTime.now());
        
        return updateById(task);
    }

    @Override
    public ReconciliationTaskVO getTaskStatistics(LocalDate startDate, LocalDate endDate, String merchantId, Integer channel) {
        return baseMapper.selectTaskStatistics(startDate, endDate, merchantId, channel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteExpiredTasks(Integer expireDays) {
        return baseMapper.deleteExpiredTasks(expireDays);
    }

    /**
     * 填充扩展字段
     */
    private void fillExtendedFields(ReconciliationTaskVO vo) {
        // 填充渠道名称
        vo.setChannelName(BillChannel.getTitleByValue(vo.getChannel()));
        
        // 填充任务状态名称
        vo.setTaskStatusName(ReconciliationTaskStatus.getDescByValue(vo.getTaskStatus()));
        
        // 填充周期类型名称
        vo.setCycleTypeName(ReconciliationCycleType.getDescByValue(vo.getCycleType()));
        
        // 填充对账结果名称
        vo.setReconciliationResultName(vo.getReconciliationResult() != null && vo.getReconciliationResult() == 1 ? "平账" : "不平");
        
        // 计算执行耗时
        if (vo.getTaskCreateTime() != null && vo.getTaskCompleteTime() != null) {
            vo.setExecutionDuration(ChronoUnit.SECONDS.between(vo.getTaskCreateTime(), vo.getTaskCompleteTime()));
        }
        
        // 判断是否可以重试
        ReconciliationTaskStatus status = ReconciliationTaskStatus.getByValue(vo.getTaskStatus());
        vo.setCanRetry(status != null && status.canRetry());
        vo.setIsFinalStatus(status != null && status.isFinalStatus());
    }

    /**
     * 生成账单名称
     */
    private String generateBillName(LocalDate reconciliationDate, ReconciliationCycleType cycleType, String channelName) {
        if (cycleType == null) {
            return DateTimeUtil.formatDate(reconciliationDate) + channelName + "对账单";
        }
        
        switch (cycleType) {
            case WEEKLY:
                return reconciliationDate.getYear() + "年第" + reconciliationDate.getDayOfYear() / 7 + "周" + channelName + "对账单";
            case MONTHLY:
                return reconciliationDate.getYear() + "年" + String.format("%02d", reconciliationDate.getMonthValue()) + "月" + channelName + "对账单";
            case QUARTERLY:
                int quarter = (reconciliationDate.getMonthValue() - 1) / 3 + 1;
                return reconciliationDate.getYear() + "年第" + quarter + "季度" + channelName + "对账单";
            case YEARLY:
                return reconciliationDate.getYear() + "年" + channelName + "对账单";
            default:
                return DateTimeUtil.formatDate(reconciliationDate) + channelName + "对账单";
        }
    }
}
