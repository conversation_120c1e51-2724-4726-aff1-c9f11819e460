package com.leliven.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.vo.MerchantReconciliationCycleVO;
import org.springblade.core.mp.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户对账周期配置服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IMerchantReconciliationCycleService extends BaseService<MerchantReconciliationCycle> {

    /**
     * 自定义分页查询商户对账周期配置
     *
     * @param page 分页对象
     * @param merchantReconciliationCycle 查询条件
     * @return 分页结果
     */
    IPage<MerchantReconciliationCycleVO> selectMerchantReconciliationCyclePage(IPage<MerchantReconciliationCycleVO> page, MerchantReconciliationCycleVO merchantReconciliationCycle);

    /**
     * 创建或更新商户对账周期配置
     *
     * @param merchantReconciliationCycle 配置信息
     * @return 保存结果
     */
    Boolean saveOrUpdateMerchantCycle(MerchantReconciliationCycle merchantReconciliationCycle);

    /**
     * 根据商户ID和渠道查询配置
     *
     * @param merchantId 商户ID
     * @param channel 渠道
     * @return 配置信息
     */
    MerchantReconciliationCycle getByMerchantAndChannel(String merchantId, Integer channel);

    /**
     * 查询所有启用的配置
     *
     * @return 启用的配置列表
     */
    List<MerchantReconciliationCycle> getEnabledConfigs();

    /**
     * 查询需要执行的配置（到达执行时间）
     *
     * @param currentTime 当前时间
     * @return 需要执行的配置列表
     */
    List<MerchantReconciliationCycle> getConfigsToExecute(LocalDateTime currentTime);

    /**
     * 启用或禁用配置
     *
     * @param id 配置ID
     * @param enabled 是否启用
     * @return 操作结果
     */
    Boolean enableOrDisableConfig(Long id, Boolean enabled);

    /**
     * 更新最后执行时间和下次执行时间
     *
     * @param id 配置ID
     * @param lastExecuteTime 最后执行时间
     * @param nextExecuteTime 下次执行时间
     * @return 更新结果
     */
    Boolean updateExecuteTime(Long id, LocalDateTime lastExecuteTime, LocalDateTime nextExecuteTime);

    /**
     * 增加失败次数
     *
     * @param id 配置ID
     * @return 更新结果
     */
    Boolean incrementFailureCount(Long id);

    /**
     * 重置失败次数
     *
     * @param id 配置ID
     * @return 重置结果
     */
    Boolean resetFailureCount(Long id);

    /**
     * 根据商户ID查询所有配置
     *
     * @param merchantId 商户ID
     * @return 配置列表
     */
    List<MerchantReconciliationCycle> getByMerchantId(String merchantId);

    /**
     * 根据渠道查询所有配置
     *
     * @param channel 渠道
     * @return 配置列表
     */
    List<MerchantReconciliationCycle> getByChannel(Integer channel);

    /**
     * 批量启用或禁用配置
     *
     * @param ids 配置ID列表
     * @param enabled 是否启用
     * @return 操作结果
     */
    Boolean batchEnableOrDisable(List<Long> ids, Boolean enabled);

    /**
     * 删除商户的所有配置
     *
     * @param merchantId 商户ID
     * @return 删除结果
     */
    Boolean deleteByMerchantId(String merchantId);

    /**
     * 计算下次执行时间
     *
     * @param config 配置信息
     * @param baseTime 基准时间
     * @return 下次执行时间
     */
    LocalDateTime calculateNextExecuteTime(MerchantReconciliationCycle config, LocalDateTime baseTime);

    /**
     * 验证配置的有效性
     *
     * @param config 配置信息
     * @return 验证结果
     */
    Boolean validateConfig(MerchantReconciliationCycle config);

    /**
     * 获取配置统计信息
     *
     * @return 统计信息
     */
    MerchantReconciliationCycleVO getConfigStatistics();

    /**
     * 复制配置到其他商户
     *
     * @param sourceId 源配置ID
     * @param targetMerchantIds 目标商户ID列表
     * @return 复制结果
     */
    Boolean copyConfigToMerchants(Long sourceId, List<String> targetMerchantIds);
}
