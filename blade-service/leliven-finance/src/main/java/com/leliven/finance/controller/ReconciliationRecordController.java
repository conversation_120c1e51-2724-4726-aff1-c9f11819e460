package com.leliven.finance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.executor.ReconciliationTaskExecutor;
import com.leliven.finance.service.IReconciliationTaskService;
import com.leliven.finance.vo.ReconciliationTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 对账记录控制器
 * 展示所有对账任务的执行情况
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/reconciliation-record")
@Api(value = "对账记录", tags = "对账记录接口")
public class ReconciliationRecordController extends BladeController {

    private final IReconciliationTaskService reconciliationTaskService;
    private final ReconciliationTaskExecutor reconciliationTaskExecutor;

    /**
     * 分页查询对账记录
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询", notes = "传入ReconciliationTaskVO")
    public R<IPage<ReconciliationTaskVO>> list(ReconciliationTaskVO reconciliationTask, Query query) {
        IPage<ReconciliationTaskVO> pages = reconciliationTaskService.selectReconciliationTaskPage(Condition.getPage(query), reconciliationTask);
        return R.data(pages);
    }

    /**
     * 查看对账记录详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "详情", notes = "传入id")
    public R<ReconciliationTask> detail(@ApiParam(value = "主键id", required = true) @RequestParam Long id) {
        ReconciliationTask detail = reconciliationTaskService.getById(id);
        return R.data(detail);
    }

    /**
     * 创建对账任务
     */
    @PostMapping("/create")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "创建对账任务", notes = "传入ReconciliationTask")
    public R<ReconciliationTask> create(@Valid @RequestBody ReconciliationTask reconciliationTask) {
        ReconciliationTask task = reconciliationTaskService.createReconciliationTask(
            reconciliationTask.getMerchantId(),
            reconciliationTask.getTenantId(),
            reconciliationTask.getChannel(),
            reconciliationTask.getReconciliationDate(),
            reconciliationTask.getCycleType(),
            reconciliationTask.getCycleStartDate(),
            reconciliationTask.getCycleEndDate()
        );
        return R.status(task != null).data(task);
    }

    /**
     * 执行对账任务
     */
    @PostMapping("/execute/{id}")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "执行对账任务", notes = "传入任务id")
    public R<Boolean> execute(@ApiParam(value = "任务id", required = true) @PathVariable Long id) {
        Boolean result = reconciliationTaskExecutor.executeTask(id);
        return R.status(result);
    }

    /**
     * 批量执行对账任务
     */
    @PostMapping("/batch-execute")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "批量执行对账任务", notes = "传入任务id列表")
    public R<Boolean> batchExecute(@ApiParam(value = "任务id列表", required = true) @RequestBody List<Long> ids) {
        Boolean result = reconciliationTaskExecutor.batchExecuteTasks(ids);
        return R.status(result);
    }

    /**
     * 重试对账任务
     */
    @PostMapping("/retry/{id}")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "重试对账任务", notes = "传入任务id")
    public R<Boolean> retry(@ApiParam(value = "任务id", required = true) @PathVariable Long id) {
        Boolean result = reconciliationTaskService.retryReconciliationTask(id);
        return R.status(result);
    }

    /**
     * 取消对账任务
     */
    @PostMapping("/cancel/{id}")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "取消对账任务", notes = "传入任务id和取消原因")
    public R<Boolean> cancel(@ApiParam(value = "任务id", required = true) @PathVariable Long id,
                           @ApiParam(value = "取消原因") @RequestParam(required = false) String reason) {
        Boolean result = reconciliationTaskService.cancelReconciliationTask(id, reason);
        return R.status(result);
    }

    /**
     * 删除对账任务
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(reconciliationTaskService.deleteLogic(Func.toLongList(ids)));
    }

    /**
     * 获取对账任务统计信息
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "获取统计信息", notes = "传入查询条件")
    public R<ReconciliationTaskVO> statistics(ReconciliationTaskVO reconciliationTask) {
        ReconciliationTaskVO statistics = reconciliationTaskService.getTaskStatistics(
            reconciliationTask.getStartDate(),
            reconciliationTask.getEndDate(),
            reconciliationTask.getMerchantId(),
            reconciliationTask.getChannel()
        );
        return R.data(statistics);
    }

    /**
     * 查询指定状态的对账任务
     */
    @GetMapping("/by-status/{status}")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "按状态查询", notes = "传入任务状态")
    public R<List<ReconciliationTask>> getByStatus(@ApiParam(value = "任务状态", required = true) @PathVariable Integer status) {
        List<ReconciliationTask> tasks = reconciliationTaskService.getTasksByStatus(status);
        return R.data(tasks);
    }

    /**
     * 查询需要重试的对账任务
     */
    @GetMapping("/need-retry")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "查询需要重试的任务", notes = "无参数")
    public R<List<ReconciliationTask>> getNeedRetry() {
        List<ReconciliationTask> tasks = reconciliationTaskService.getTasksNeedRetry();
        return R.data(tasks);
    }

    /**
     * 清理过期的对账任务
     */
    @PostMapping("/cleanup-expired")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "清理过期任务", notes = "传入过期天数")
    public R<Integer> cleanupExpired(@ApiParam(value = "过期天数", required = true) @RequestParam Integer expireDays) {
        Integer deletedCount = reconciliationTaskService.deleteExpiredTasks(expireDays);
        return R.data(deletedCount);
    }

    /**
     * 异步执行对账任务
     */
    @PostMapping("/execute-async/{id}")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "异步执行对账任务", notes = "传入任务id")
    public R<String> executeAsync(@ApiParam(value = "任务id", required = true) @PathVariable Long id) {
        reconciliationTaskExecutor.executeTaskAsync(id);
        return R.success("对账任务已提交异步执行");
    }

    /**
     * 异步批量执行对账任务
     */
    @PostMapping("/batch-execute-async")
    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "异步批量执行对账任务", notes = "传入任务id列表")
    public R<String> batchExecuteAsync(@ApiParam(value = "任务id列表", required = true) @RequestBody List<Long> ids) {
        reconciliationTaskExecutor.batchExecuteTasksAsync(ids);
        return R.success("批量对账任务已提交异步执行");
    }
}
