package com.leliven.finance.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.enums.BillChannel;
import com.leliven.finance.enums.BillStatus;
import com.leliven.finance.enums.ReconciliationCycleType;
import com.leliven.finance.mapper.MerchantReconciliationCycleMapper;
import com.leliven.finance.service.IMerchantReconciliationCycleService;
import com.leliven.finance.vo.MerchantReconciliationCycleVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商户对账周期配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class MerchantReconciliationCycleServiceImpl extends BaseServiceImpl<MerchantReconciliationCycleMapper, MerchantReconciliationCycle> implements IMerchantReconciliationCycleService {

    @Override
    public IPage<MerchantReconciliationCycleVO> selectMerchantReconciliationCyclePage(IPage<MerchantReconciliationCycleVO> page, MerchantReconciliationCycleVO merchantReconciliationCycle) {
        List<MerchantReconciliationCycleVO> list = baseMapper.selectMerchantReconciliationCyclePage(page, merchantReconciliationCycle);
        
        // 填充扩展字段
        for (MerchantReconciliationCycleVO vo : list) {
            fillExtendedFields(vo);
        }
        
        return page.setRecords(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateMerchantCycle(MerchantReconciliationCycle merchantReconciliationCycle) {
        LecentAssert.notNull(merchantReconciliationCycle, "配置信息不能为空");
        
        // 验证配置有效性
        if (!validateConfig(merchantReconciliationCycle)) {
            return false;
        }
        
        // 检查是否已存在相同商户和渠道的配置
        MerchantReconciliationCycle existing = getByMerchantAndChannel(
            merchantReconciliationCycle.getMerchantId(), 
            merchantReconciliationCycle.getChannel()
        );
        
        if (existing != null && !existing.getId().equals(merchantReconciliationCycle.getId())) {
            log.warn("商户[{}]渠道[{}]的配置已存在", merchantReconciliationCycle.getMerchantId(), merchantReconciliationCycle.getChannel());
            return false;
        }
        
        // 计算下次执行时间
        LocalDateTime nextExecuteTime = calculateNextExecuteTime(merchantReconciliationCycle, LocalDateTime.now());
        merchantReconciliationCycle.setNextExecuteTime(nextExecuteTime);
        
        // 设置创建人信息
        if (merchantReconciliationCycle.getId() == null) {
            merchantReconciliationCycle.setCreatorName(AuthUtil.getUserName());
        } else {
            merchantReconciliationCycle.setUpdaterName(AuthUtil.getUserName());
        }
        
        return saveOrUpdate(merchantReconciliationCycle);
    }

    @Override
    public MerchantReconciliationCycle getByMerchantAndChannel(String merchantId, Integer channel) {
        return baseMapper.selectByMerchantAndChannel(merchantId, channel);
    }

    @Override
    public List<MerchantReconciliationCycle> getEnabledConfigs() {
        return baseMapper.selectEnabledConfigs();
    }

    @Override
    public List<MerchantReconciliationCycle> getConfigsToExecute(LocalDateTime currentTime) {
        return baseMapper.selectConfigsToExecute(currentTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableOrDisableConfig(Long id, Boolean enabled) {
        LecentAssert.notNull(id, "配置ID不能为空");
        LecentAssert.notNull(enabled, "启用状态不能为空");
        
        MerchantReconciliationCycle config = new MerchantReconciliationCycle();
        config.setId(id);
        config.setEnabled(enabled);
        config.setUpdaterName(AuthUtil.getUserName());
        
        return updateById(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateExecuteTime(Long id, LocalDateTime lastExecuteTime, LocalDateTime nextExecuteTime) {
        return baseMapper.updateExecuteTime(id, lastExecuteTime, nextExecuteTime) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean incrementFailureCount(Long id) {
        return baseMapper.incrementFailureCount(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetFailureCount(Long id) {
        return baseMapper.resetFailureCount(id) > 0;
    }

    @Override
    public List<MerchantReconciliationCycle> getByMerchantId(String merchantId) {
        return baseMapper.selectByMerchantId(merchantId);
    }

    @Override
    public List<MerchantReconciliationCycle> getByChannel(Integer channel) {
        return baseMapper.selectByChannel(channel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchEnableOrDisable(List<Long> ids, Boolean enabled) {
        if (Func.isEmpty(ids)) {
            return true;
        }
        
        return baseMapper.batchUpdateEnabled(ids, enabled) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByMerchantId(String merchantId) {
        return baseMapper.deleteByMerchantId(merchantId) >= 0;
    }

    @Override
    public LocalDateTime calculateNextExecuteTime(MerchantReconciliationCycle config, LocalDateTime baseTime) {
        if (config == null || config.getCycleType() == null) {
            return baseTime.plusDays(1);
        }
        
        ReconciliationCycleType cycleType = ReconciliationCycleType.getByValue(config.getCycleType());
        if (cycleType == null) {
            return baseTime.plusDays(1);
        }
        
        // 获取执行时间
        LocalDateTime executeDateTime = baseTime.toLocalDate().atTime(
            config.getExecuteTime() != null ? config.getExecuteTime() : java.time.LocalTime.of(8, 0)
        );
        
        // 如果当前时间已过今天的执行时间，则从明天开始计算
        if (baseTime.isAfter(executeDateTime)) {
            executeDateTime = executeDateTime.plusDays(1);
        }
        
        // 根据周期类型计算下次执行时间
        return executeDateTime.toLocalDate().atTime(executeDateTime.toLocalTime());
    }

    @Override
    public Boolean validateConfig(MerchantReconciliationCycle config) {
        if (config == null) {
            return false;
        }
        
        // 验证必填字段
        if (Func.isBlank(config.getMerchantId())) {
            log.warn("商户ID不能为空");
            return false;
        }
        
        if (config.getChannel() == null) {
            log.warn("对账渠道不能为空");
            return false;
        }
        
        if (config.getCycleType() == null) {
            log.warn("周期类型不能为空");
            return false;
        }
        
        if (config.getStartDate() == null) {
            log.warn("开始日期不能为空");
            return false;
        }
        
        // 验证周期类型
        ReconciliationCycleType cycleType = ReconciliationCycleType.getByValue(config.getCycleType());
        if (cycleType == null) {
            log.warn("无效的周期类型: {}", config.getCycleType());
            return false;
        }
        
        // 验证渠道
        String channelName = BillChannel.getTitleByValue(config.getChannel());
        if (Func.isBlank(channelName)) {
            log.warn("无效的对账渠道: {}", config.getChannel());
            return false;
        }
        
        // 验证周期值
        if (cycleType == ReconciliationCycleType.CUSTOM && (config.getCycleValue() == null || config.getCycleValue() <= 0)) {
            log.warn("自定义周期类型的周期值必须大于0");
            return false;
        }
        
        // 验证日期范围
        if (config.getEndDate() != null && config.getEndDate().isBefore(config.getStartDate())) {
            log.warn("结束日期不能早于开始日期");
            return false;
        }
        
        return true;
    }

    @Override
    public MerchantReconciliationCycleVO getConfigStatistics() {
        return baseMapper.selectConfigStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyConfigToMerchants(Long sourceId, List<String> targetMerchantIds) {
        if (sourceId == null || Func.isEmpty(targetMerchantIds)) {
            return false;
        }
        
        MerchantReconciliationCycle sourceConfig = getById(sourceId);
        if (sourceConfig == null) {
            log.warn("源配置不存在: {}", sourceId);
            return false;
        }
        
        boolean allSuccess = true;
        for (String merchantId : targetMerchantIds) {
            try {
                // 检查目标商户是否已有相同渠道的配置
                MerchantReconciliationCycle existing = getByMerchantAndChannel(merchantId, sourceConfig.getChannel());
                if (existing != null) {
                    log.warn("目标商户[{}]已存在渠道[{}]的配置，跳过复制", merchantId, sourceConfig.getChannel());
                    continue;
                }
                
                // 创建新配置
                MerchantReconciliationCycle newConfig = new MerchantReconciliationCycle();
                // 复制源配置的属性（除了ID、商户ID、创建时间等）
                newConfig.setMerchantId(merchantId);
                newConfig.setMerchantName(""); // 需要根据merchantId查询商户名称
                newConfig.setChannel(sourceConfig.getChannel());
                newConfig.setCycleType(sourceConfig.getCycleType());
                newConfig.setCycleValue(sourceConfig.getCycleValue());
                newConfig.setExecuteTime(sourceConfig.getExecuteTime());
                newConfig.setAutoReconciliation(sourceConfig.getAutoReconciliation());
                newConfig.setStartDate(sourceConfig.getStartDate());
                newConfig.setEndDate(sourceConfig.getEndDate());
                newConfig.setSupportedChannels(sourceConfig.getSupportedChannels());
                newConfig.setDescription("从配置[" + sourceId + "]复制");
                newConfig.setEnabled(sourceConfig.getEnabled());
                newConfig.setMaxRetryCount(sourceConfig.getMaxRetryCount());
                
                saveOrUpdateMerchantCycle(newConfig);
                
            } catch (Exception e) {
                log.error("复制配置到商户[{}]失败", merchantId, e);
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }

    /**
     * 填充扩展字段
     */
    private void fillExtendedFields(MerchantReconciliationCycleVO vo) {
        // 填充渠道名称
        vo.setChannelName(BillChannel.getTitleByValue(vo.getChannel()));
        
        // 填充周期类型名称
        ReconciliationCycleType cycleType = ReconciliationCycleType.getByValue(vo.getCycleType());
        if (cycleType != null) {
            vo.setCycleTypeName(cycleType.getFullDesc(vo.getCycleValue()));
        }
        
        // 填充账单状态名称
        vo.setBillStatusName(BillStatus.getDescByValue(vo.getBillStatus()));
        
        // 填充启用状态名称
        vo.setEnabledStatusName(Boolean.TRUE.equals(vo.getEnabled()) ? "启用" : "禁用");
        
        // 填充自动对账状态名称
        vo.setAutoReconciliationStatusName(Boolean.TRUE.equals(vo.getAutoReconciliation()) ? "启用" : "禁用");
        
        // 填充支持的渠道名称
        if (Func.isNotBlank(vo.getSupportedChannels())) {
            String channelNames = Arrays.stream(vo.getSupportedChannels().split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .map(BillChannel::getTitleByValue)
                .collect(Collectors.joining(","));
            vo.setSupportedChannelNames(channelNames);
        }
        
        // 判断是否超过最大重试次数
        vo.setExceedsMaxRetry(vo.getFailureCount() != null && vo.getMaxRetryCount() != null 
            && vo.getFailureCount() >= vo.getMaxRetryCount());
        
        // 计算距离下次执行的时间
        if (vo.getNextExecuteTime() != null) {
            long hours = ChronoUnit.HOURS.between(LocalDateTime.now(), vo.getNextExecuteTime());
            vo.setHoursToNextExecution(hours);
        }
        
        // 设置配置状态
        if (Boolean.FALSE.equals(vo.getEnabled())) {
            vo.setConfigStatus("禁用");
        } else if (Boolean.TRUE.equals(vo.getExceedsMaxRetry())) {
            vo.setConfigStatus("异常");
        } else {
            vo.setConfigStatus("正常");
        }
    }
}
