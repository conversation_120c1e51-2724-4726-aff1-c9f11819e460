package com.leliven.finance.executor;

import com.leliven.finance.entity.BillTotal;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.enums.BillChannel;
import com.leliven.finance.enums.ReconciliationTaskStatus;
import com.leliven.finance.service.IBillTotalService;
import com.leliven.finance.service.ICommonService;
import com.leliven.finance.service.IReconciliationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.tool.utils.SpringUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 对账任务执行器
 * 负责执行具体的对账逻辑
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Component
public class ReconciliationTaskExecutor {

    @Resource
    private IReconciliationTaskService reconciliationTaskService;

    @Resource
    private ICommonService commonService;

    @Resource
    private IBillTotalService billTotalService;

    /**
     * 线程池执行器
     */
    private final Executor executor = Executors.newFixedThreadPool(5);

    /**
     * 同步执行对账任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeTask(Long taskId) {
        LecentAssert.notNull(taskId, "任务ID不能为空");

        ReconciliationTask task = reconciliationTaskService.getById(taskId);
        if (task == null) {
            log.warn("对账任务不存在: taskId={}", taskId);
            return false;
        }

        return executeTaskInternal(task);
    }

    /**
     * 异步执行对账任务
     *
     * @param taskId 任务ID
     * @return 异步执行结果
     */
    public CompletableFuture<Boolean> executeTaskAsync(Long taskId) {
        return CompletableFuture.supplyAsync(() -> executeTask(taskId), executor);
    }

    /**
     * 批量执行对账任务
     *
     * @param taskIds 任务ID列表
     * @return 执行结果
     */
    public Boolean batchExecuteTasks(java.util.List<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return true;
        }

        boolean allSuccess = true;
        for (Long taskId : taskIds) {
            try {
                Boolean result = executeTask(taskId);
                if (!result) {
                    allSuccess = false;
                }
            } catch (Exception e) {
                log.error("批量执行对账任务失败: taskId={}", taskId, e);
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    /**
     * 异步批量执行对账任务
     *
     * @param taskIds 任务ID列表
     * @return 异步执行结果
     */
    public CompletableFuture<Boolean> batchExecuteTasksAsync(java.util.List<Long> taskIds) {
        return CompletableFuture.supplyAsync(() -> batchExecuteTasks(taskIds), executor);
    }

    /**
     * 内部执行对账任务的核心逻辑
     *
     * @param task 对账任务
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    private Boolean executeTaskInternal(ReconciliationTask task) {
        try {
            log.info("开始执行对账任务: taskId={}, taskName={}", task.getId(), task.getTaskName());

            // 1. 更新任务状态为执行中
            reconciliationTaskService.updateTaskStatus(task.getId(), ReconciliationTaskStatus.EXECUTING.getValue(), null);

            // 2. 创建或获取对账单
            BillTotal billTotal = getOrCreateBillTotal(task);
            if (billTotal == null) {
                reconciliationTaskService.updateTaskStatus(task.getId(), ReconciliationTaskStatus.DATA_FETCH_FAILED.getValue(), "创建对账单失败");
                return false;
            }

            // 3. 更新任务关联的对账单ID
            task.setBillTotalId(billTotal.getId());
            reconciliationTaskService.updateById(task);

            // 4. 执行对账逻辑
            ReconciliationResult result = performReconciliation(task, billTotal);

            // 5. 更新任务统计信息
            updateTaskStatistics(task, result);

            // 6. 完成任务
            reconciliationTaskService.completeReconciliationTask(
                task.getId(), 
                result.isBalanced() ? 1 : 0, 
                billTotal.getId()
            );

            log.info("对账任务执行完成: taskId={}, result={}", task.getId(), result.isBalanced() ? "平账" : "不平");
            return true;

        } catch (Exception e) {
            log.error("对账任务执行失败: taskId={}", task.getId(), e);
            reconciliationTaskService.updateTaskStatus(task.getId(), ReconciliationTaskStatus.EXECUTION_FAILED.getValue(), e.getMessage());
            return false;
        }
    }

    /**
     * 获取或创建对账单
     *
     * @param task 对账任务
     * @return 对账单
     */
    private BillTotal getOrCreateBillTotal(ReconciliationTask task) {
        try {
            // 先查找是否已存在对账单
            BillTotal existingBill = billTotalService.lambdaQuery()
                .eq(BillTotal::getMerchantId, task.getMerchantId())
                .eq(BillTotal::getChannel, task.getChannel())
                .eq(BillTotal::getDate, task.getReconciliationDate())
                .one();

            if (existingBill != null) {
                log.info("找到已存在的对账单: billId={}, taskId={}", existingBill.getId(), task.getId());
                return existingBill;
            }

            // 创建新的对账单
            Boolean createResult = commonService.createBillByTask(task);
            if (!createResult) {
                log.warn("创建对账单失败: taskId={}", task.getId());
                return null;
            }

            // 重新查询创建的对账单
            BillTotal newBill = billTotalService.lambdaQuery()
                .eq(BillTotal::getMerchantId, task.getMerchantId())
                .eq(BillTotal::getChannel, task.getChannel())
                .eq(BillTotal::getDate, task.getReconciliationDate())
                .orderByDesc(BillTotal::getCreateTime)
                .last("LIMIT 1")
                .one();

            if (newBill != null) {
                log.info("创建新对账单成功: billId={}, taskId={}", newBill.getId(), task.getId());
            }

            return newBill;

        } catch (Exception e) {
            log.error("获取或创建对账单失败: taskId={}", task.getId(), e);
            return null;
        }
    }

    /**
     * 执行对账逻辑
     *
     * @param task 对账任务
     * @param billTotal 对账单
     * @return 对账结果
     */
    private ReconciliationResult performReconciliation(ReconciliationTask task, BillTotal billTotal) {
        ReconciliationResult result = new ReconciliationResult();

        try {
            log.info("开始执行对账逻辑: taskId={}, billId={}, channel={}",
                task.getId(), billTotal.getId(), task.getChannel());

            // 1. 获取第三方支付数据统计
            ReconciliationResult thirdPartyData = getThirdPartyData(task, billTotal);
            result.setThirdPartyTotalAmount(thirdPartyData.getThirdPartyTotalAmount());
            result.setThirdPartyRefundAmount(thirdPartyData.getThirdPartyRefundAmount());
            result.setThirdPartyTotalCount(thirdPartyData.getThirdPartyTotalCount());
            result.setThirdPartyRefundCount(thirdPartyData.getThirdPartyRefundCount());

            // 2. 获取平台数据统计
            ReconciliationResult platformData = getPlatformData(task, billTotal);
            result.setPlatformTotalAmount(platformData.getPlatformTotalAmount());
            result.setPlatformRefundAmount(platformData.getPlatformRefundAmount());
            result.setPlatformTotalCount(platformData.getPlatformTotalCount());
            result.setPlatformRefundCount(platformData.getPlatformRefundCount());

            // 3. 进行对账比较
            boolean amountBalanced = compareAmounts(result.getThirdPartyTotalAmount(), result.getPlatformTotalAmount());
            boolean refundAmountBalanced = compareAmounts(result.getThirdPartyRefundAmount(), result.getPlatformRefundAmount());
            boolean countBalanced = result.getThirdPartyTotalCount().equals(result.getPlatformTotalCount());
            boolean refundCountBalanced = result.getThirdPartyRefundCount().equals(result.getPlatformRefundCount());

            result.setBalanced(amountBalanced && refundAmountBalanced && countBalanced && refundCountBalanced);

            log.info("对账结果详情: taskId={}, 第三方[金额:{}, 退款:{}, 笔数:{}, 退款笔数:{}], 平台[金额:{}, 退款:{}, 笔数:{}, 退款笔数:{}]",
                task.getId(),
                result.getThirdPartyTotalAmount(), result.getThirdPartyRefundAmount(),
                result.getThirdPartyTotalCount(), result.getThirdPartyRefundCount(),
                result.getPlatformTotalAmount(), result.getPlatformRefundAmount(),
                result.getPlatformTotalCount(), result.getPlatformRefundCount());

            log.info("对账比较结果: taskId={}, 金额平账={}, 退款金额平账={}, 笔数平账={}, 退款笔数平账={}, 总体结果={}",
                task.getId(), amountBalanced, refundAmountBalanced, countBalanced, refundCountBalanced, result.isBalanced());

        } catch (Exception e) {
            log.error("执行对账逻辑失败: taskId={}", task.getId(), e);
            result.setBalanced(false);
        }

        return result;
    }

    /**
     * 获取第三方支付数据
     *
     * @param task 对账任务
     * @param billTotal 对账单
     * @return 第三方数据
     */
    private ReconciliationResult getThirdPartyData(ReconciliationTask task, BillTotal billTotal) {
        ReconciliationResult result = new ReconciliationResult();

        try {
            // 根据渠道获取第三方数据
            String channelName = BillChannel.getTitleByValue(task.getChannel());
            log.debug("获取第三方支付数据: channel={}, channelName={}", task.getChannel(), channelName);

            // 目前从对账单的B端数据获取（B代表第三方数据）
            result.setThirdPartyTotalAmount(billTotal.getBReceiveAmount() != null ? billTotal.getBReceiveAmount() : BigDecimal.ZERO);
            result.setThirdPartyRefundAmount(billTotal.getBRefundAmount() != null ? billTotal.getBRefundAmount() : BigDecimal.ZERO);
            result.setThirdPartyTotalCount(billTotal.getBOrderNum() != null ? billTotal.getBOrderNum() : 0);
            result.setThirdPartyRefundCount(billTotal.getBRefundOrderNum() != null ? billTotal.getBRefundOrderNum() : 0);

            // TODO: 后续可以根据具体渠道调用相应的API获取真实数据
            // switch (task.getChannel()) {
            //     case 1: // 微信
            //         result = getWechatPayData(task);
            //         break;
            //     case 0: // 支付宝
            //         result = getAlipayData(task);
            //         break;
            //     default:
            //         // 使用对账单中的数据
            //         break;
            // }

            log.debug("第三方数据获取完成: 金额={}, 退款={}, 笔数={}, 退款笔数={}",
                result.getThirdPartyTotalAmount(), result.getThirdPartyRefundAmount(),
                result.getThirdPartyTotalCount(), result.getThirdPartyRefundCount());

        } catch (Exception e) {
            log.error("获取第三方支付数据失败: taskId={}", task.getId(), e);
            // 设置默认值
            result.setThirdPartyTotalAmount(BigDecimal.ZERO);
            result.setThirdPartyRefundAmount(BigDecimal.ZERO);
            result.setThirdPartyTotalCount(0);
            result.setThirdPartyRefundCount(0);
        }

        return result;
    }

    /**
     * 获取平台数据
     *
     * @param task 对账任务
     * @param billTotal 对账单
     * @return 平台数据
     */
    private ReconciliationResult getPlatformData(ReconciliationTask task, BillTotal billTotal) {
        ReconciliationResult result = new ReconciliationResult();

        try {
            log.debug("获取平台数据: taskId={}, merchantId={}", task.getId(), task.getMerchantId());

            // 从对账单的M端数据获取（M代表商户/平台数据）
            result.setPlatformTotalAmount(billTotal.getMReceiveAmount() != null ? billTotal.getMReceiveAmount() : BigDecimal.ZERO);
            result.setPlatformRefundAmount(billTotal.getMRefundAmount() != null ? billTotal.getMRefundAmount() : BigDecimal.ZERO);
            result.setPlatformTotalCount(billTotal.getMOrderNum() != null ? billTotal.getMOrderNum() : 0);
            result.setPlatformRefundCount(billTotal.getMRefundOrderNum() != null ? billTotal.getMRefundOrderNum() : 0);

            log.debug("平台数据获取完成: 金额={}, 退款={}, 笔数={}, 退款笔数={}",
                result.getPlatformTotalAmount(), result.getPlatformRefundAmount(),
                result.getPlatformTotalCount(), result.getPlatformRefundCount());

        } catch (Exception e) {
            log.error("获取平台数据失败: taskId={}", task.getId(), e);
            // 设置默认值
            result.setPlatformTotalAmount(BigDecimal.ZERO);
            result.setPlatformRefundAmount(BigDecimal.ZERO);
            result.setPlatformTotalCount(0);
            result.setPlatformRefundCount(0);
        }

        return result;
    }

    /**
     * 比较金额（考虑精度问题）
     *
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 是否相等
     */
    private boolean compareAmounts(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) amount1 = BigDecimal.ZERO;
        if (amount2 == null) amount2 = BigDecimal.ZERO;

        // 使用compareTo比较，避免精度问题
        return amount1.compareTo(amount2) == 0;
    }

    /**
     * 更新任务统计信息
     *
     * @param task 对账任务
     * @param result 对账结果
     */
    private void updateTaskStatistics(ReconciliationTask task, ReconciliationResult result) {
        try {
            ReconciliationTask updateTask = new ReconciliationTask();
            updateTask.setId(task.getId());
            updateTask.setThirdPartyTotalAmount(result.getThirdPartyTotalAmount());
            updateTask.setThirdPartyRefundAmount(result.getThirdPartyRefundAmount());
            updateTask.setThirdPartyTotalCount(result.getThirdPartyTotalCount());
            updateTask.setThirdPartyRefundCount(result.getThirdPartyRefundCount());
            updateTask.setPlatformTotalAmount(result.getPlatformTotalAmount());
            updateTask.setPlatformRefundAmount(result.getPlatformRefundAmount());
            updateTask.setPlatformTotalCount(result.getPlatformTotalCount());
            updateTask.setPlatformRefundCount(result.getPlatformRefundCount());

            reconciliationTaskService.updateById(updateTask);

        } catch (Exception e) {
            log.error("更新任务统计信息失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 对账结果内部类
     */
    private static class ReconciliationResult {
        private BigDecimal thirdPartyTotalAmount = BigDecimal.ZERO;
        private BigDecimal thirdPartyRefundAmount = BigDecimal.ZERO;
        private Integer thirdPartyTotalCount = 0;
        private Integer thirdPartyRefundCount = 0;
        private BigDecimal platformTotalAmount = BigDecimal.ZERO;
        private BigDecimal platformRefundAmount = BigDecimal.ZERO;
        private Integer platformTotalCount = 0;
        private Integer platformRefundCount = 0;
        private boolean balanced = false;

        // Getters and Setters
        public BigDecimal getThirdPartyTotalAmount() { return thirdPartyTotalAmount; }
        public void setThirdPartyTotalAmount(BigDecimal thirdPartyTotalAmount) { this.thirdPartyTotalAmount = thirdPartyTotalAmount; }
        public BigDecimal getThirdPartyRefundAmount() { return thirdPartyRefundAmount; }
        public void setThirdPartyRefundAmount(BigDecimal thirdPartyRefundAmount) { this.thirdPartyRefundAmount = thirdPartyRefundAmount; }
        public Integer getThirdPartyTotalCount() { return thirdPartyTotalCount; }
        public void setThirdPartyTotalCount(Integer thirdPartyTotalCount) { this.thirdPartyTotalCount = thirdPartyTotalCount; }
        public Integer getThirdPartyRefundCount() { return thirdPartyRefundCount; }
        public void setThirdPartyRefundCount(Integer thirdPartyRefundCount) { this.thirdPartyRefundCount = thirdPartyRefundCount; }
        public BigDecimal getPlatformTotalAmount() { return platformTotalAmount; }
        public void setPlatformTotalAmount(BigDecimal platformTotalAmount) { this.platformTotalAmount = platformTotalAmount; }
        public BigDecimal getPlatformRefundAmount() { return platformRefundAmount; }
        public void setPlatformRefundAmount(BigDecimal platformRefundAmount) { this.platformRefundAmount = platformRefundAmount; }
        public Integer getPlatformTotalCount() { return platformTotalCount; }
        public void setPlatformTotalCount(Integer platformTotalCount) { this.platformTotalCount = platformTotalCount; }
        public Integer getPlatformRefundCount() { return platformRefundCount; }
        public void setPlatformRefundCount(Integer platformRefundCount) { this.platformRefundCount = platformRefundCount; }
        public boolean isBalanced() { return balanced; }
        public void setBalanced(boolean balanced) { this.balanced = balanced; }
    }
}
