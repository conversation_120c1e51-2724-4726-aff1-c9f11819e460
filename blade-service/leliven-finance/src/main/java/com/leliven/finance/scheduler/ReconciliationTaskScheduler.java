package com.leliven.finance.scheduler;

import com.leliven.finance.entity.MerchantReconciliationCycle;
import com.leliven.finance.entity.ReconciliationTask;
import com.leliven.finance.enums.ReconciliationCycleType;
import com.leliven.finance.executor.ReconciliationTaskExecutor;
import com.leliven.finance.service.IMerchantReconciliationCycleService;
import com.leliven.finance.service.IReconciliationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 对账任务调度器
 * 负责根据商户配置动态调度对账任务
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Component
public class ReconciliationTaskScheduler {

    @Resource
    private IMerchantReconciliationCycleService merchantReconciliationCycleService;

    @Resource
    private IReconciliationTaskService reconciliationTaskService;

    @Resource
    private ReconciliationTaskExecutor reconciliationTaskExecutor;

    /**
     * 异步执行器
     */
    private final Executor asyncExecutor = Executors.newFixedThreadPool(3);

    /**
     * 执行调度检查
     * 检查所有需要执行的配置并创建对应的对账任务
     *
     * @return 处理的配置数量
     */
    public Integer executeScheduleCheck() {
        return executeScheduleCheck(LocalDateTime.now());
    }

    /**
     * 执行调度检查（指定时间）
     *
     * @param currentTime 当前时间
     * @return 处理的配置数量
     */
    public Integer executeScheduleCheck(LocalDateTime currentTime) {
        try {
            log.info("开始执行对账任务调度检查，当前时间: {}", currentTime);

            // 获取需要执行的配置
            List<MerchantReconciliationCycle> configsToExecute = 
                merchantReconciliationCycleService.getConfigsToExecute(currentTime);

            if (configsToExecute.isEmpty()) {
                log.debug("当前没有需要执行的对账任务配置");
                return 0;
            }

            log.info("找到 {} 个需要执行的对账任务配置", configsToExecute.size());

            int successCount = 0;
            for (MerchantReconciliationCycle config : configsToExecute) {
                try {
                    boolean success = processReconciliationConfig(config, currentTime);
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理对账任务配置失败: configId={}, merchantId={}, channel={}", 
                        config.getId(), config.getMerchantId(), config.getChannel(), e);
                    
                    // 增加失败次数
                    merchantReconciliationCycleService.incrementFailureCount(config.getId());
                }
            }

            log.info("对账任务调度检查完成，成功处理 {}/{} 个配置", successCount, configsToExecute.size());
            return successCount;

        } catch (Exception e) {
            log.error("对账任务调度执行异常", e);
            return 0;
        }
    }

    /**
     * 处理单个对账配置
     *
     * @param config 对账配置
     * @param currentTime 当前时间
     * @return 处理结果
     */
    private boolean processReconciliationConfig(MerchantReconciliationCycle config, LocalDateTime currentTime) {
        try {
            // 计算对账日期
            LocalDate reconciliationDate = calculateReconciliationDate(config, currentTime);
            
            // 检查是否已存在相同的任务
            ReconciliationTask existingTask = reconciliationTaskService.lambdaQuery()
                .eq(ReconciliationTask::getMerchantId, config.getMerchantId())
                .eq(ReconciliationTask::getChannel, config.getChannel())
                .eq(ReconciliationTask::getReconciliationDate, reconciliationDate)
                .eq(ReconciliationTask::getCycleType, config.getCycleType())
                .one();

            if (existingTask != null) {
                log.debug("对账任务已存在，跳过创建: merchantId={}, channel={}, date={}", 
                    config.getMerchantId(), config.getChannel(), reconciliationDate);
                
                // 更新下次执行时间
                updateNextExecuteTime(config, currentTime);
                return true;
            }

            // 创建对账任务
            ReconciliationTask task = createReconciliationTask(config, reconciliationDate);
            if (task == null) {
                log.warn("创建对账任务失败: merchantId={}, channel={}, date={}", 
                    config.getMerchantId(), config.getChannel(), reconciliationDate);
                return false;
            }

            log.info("成功创建对账任务: taskId={}, merchantId={}, channel={}, date={}", 
                task.getId(), config.getMerchantId(), config.getChannel(), reconciliationDate);

            // 异步执行对账任务
            CompletableFuture.runAsync(() -> {
                try {
                    reconciliationTaskExecutor.executeTask(task.getId());
                } catch (Exception e) {
                    log.error("异步执行对账任务失败: taskId={}", task.getId(), e);
                }
            }, asyncExecutor);

            // 更新配置的执行时间
            updateNextExecuteTime(config, currentTime);
            
            // 重置失败次数
            merchantReconciliationCycleService.resetFailureCount(config.getId());

            return true;

        } catch (Exception e) {
            log.error("处理对账配置异常: configId={}", config.getId(), e);
            return false;
        }
    }

    /**
     * 创建对账任务
     *
     * @param config 对账配置
     * @param reconciliationDate 对账日期
     * @return 创建的任务
     */
    private ReconciliationTask createReconciliationTask(MerchantReconciliationCycle config, LocalDate reconciliationDate) {
        // 根据周期类型计算周期范围
        LocalDate cycleStartDate = reconciliationDate;
        LocalDate cycleEndDate = reconciliationDate;

        ReconciliationCycleType cycleType = ReconciliationCycleType.getByValue(config.getCycleType());
        if (cycleType != null) {
            switch (cycleType) {
                case WEEKLY:
                    cycleStartDate = reconciliationDate.minusDays(reconciliationDate.getDayOfWeek().getValue() - 1);
                    cycleEndDate = cycleStartDate.plusDays(6);
                    break;
                case MONTHLY:
                    cycleStartDate = reconciliationDate.withDayOfMonth(1);
                    cycleEndDate = cycleStartDate.plusMonths(1).minusDays(1);
                    break;
                case QUARTERLY:
                    int quarter = (reconciliationDate.getMonthValue() - 1) / 3;
                    cycleStartDate = reconciliationDate.withMonth(quarter * 3 + 1).withDayOfMonth(1);
                    cycleEndDate = cycleStartDate.plusMonths(3).minusDays(1);
                    break;
                case YEARLY:
                    cycleStartDate = reconciliationDate.withDayOfYear(1);
                    cycleEndDate = cycleStartDate.plusYears(1).minusDays(1);
                    break;
                case CUSTOM:
                    if (config.getCycleValue() != null && config.getCycleValue() > 1) {
                        cycleEndDate = cycleStartDate.plusDays(config.getCycleValue() - 1);
                    }
                    break;
                default:
                    // DAILY 保持默认值
                    break;
            }
        }

        return reconciliationTaskService.createReconciliationTask(
            config.getMerchantId(),
            config.getTenantId(),
            config.getChannel(),
            reconciliationDate,
            config.getCycleType(),
            cycleStartDate,
            cycleEndDate
        );
    }

    /**
     * 计算对账日期
     *
     * @param config 对账配置
     * @param currentTime 当前时间
     * @return 对账日期
     */
    private LocalDate calculateReconciliationDate(MerchantReconciliationCycle config, LocalDateTime currentTime) {
        // 默认对账昨天的数据
        LocalDate reconciliationDate = currentTime.toLocalDate().minusDays(1);

        ReconciliationCycleType cycleType = ReconciliationCycleType.getByValue(config.getCycleType());
        if (cycleType != null) {
            switch (cycleType) {
                case WEEKLY:
                    // 周对账：对账上周的数据
                    reconciliationDate = currentTime.toLocalDate().minusWeeks(1);
                    break;
                case MONTHLY:
                    // 月对账：对账上月的数据
                    reconciliationDate = currentTime.toLocalDate().minusMonths(1);
                    break;
                case QUARTERLY:
                    // 季对账：对账上季度的数据
                    reconciliationDate = currentTime.toLocalDate().minusMonths(3);
                    break;
                case YEARLY:
                    // 年对账：对账去年的数据
                    reconciliationDate = currentTime.toLocalDate().minusYears(1);
                    break;
                case CUSTOM:
                    // 自定义周期：根据周期值计算
                    if (config.getCycleValue() != null && config.getCycleValue() > 0) {
                        reconciliationDate = currentTime.toLocalDate().minusDays(config.getCycleValue());
                    }
                    break;
                default:
                    // DAILY 保持默认值
                    break;
            }
        }

        return reconciliationDate;
    }

    /**
     * 更新下次执行时间
     *
     * @param config 对账配置
     * @param currentTime 当前时间
     */
    private void updateNextExecuteTime(MerchantReconciliationCycle config, LocalDateTime currentTime) {
        try {
            LocalDateTime nextExecuteTime = merchantReconciliationCycleService
                .calculateNextExecuteTime(config, currentTime);
            
            merchantReconciliationCycleService.updateExecuteTime(
                config.getId(), 
                currentTime, 
                nextExecuteTime
            );
            
            log.debug("更新配置执行时间: configId={}, nextTime={}", config.getId(), nextExecuteTime);
            
        } catch (Exception e) {
            log.error("更新配置执行时间失败: configId={}", config.getId(), e);
        }
    }

    /**
     * 手动触发指定商户和渠道的对账任务
     *
     * @param merchantId 商户ID
     * @param channel 渠道
     * @param reconciliationDate 对账日期
     * @return 创建的任务ID
     */
    public Long triggerManualReconciliation(String merchantId, Integer channel, LocalDate reconciliationDate) {
        try {
            log.info("手动触发对账任务: merchantId={}, channel={}, date={}", merchantId, channel, reconciliationDate);

            // 查找对应的配置
            MerchantReconciliationCycle config = merchantReconciliationCycleService
                .getByMerchantAndChannel(merchantId, channel);
            
            if (config == null) {
                log.warn("未找到对应的对账配置: merchantId={}, channel={}", merchantId, channel);
                return null;
            }

            // 创建对账任务
            ReconciliationTask task = createReconciliationTask(config, reconciliationDate);
            if (task == null) {
                log.warn("手动创建对账任务失败: merchantId={}, channel={}, date={}", 
                    merchantId, channel, reconciliationDate);
                return null;
            }

            // 同步执行对账任务
            reconciliationTaskExecutor.executeTask(task.getId());

            log.info("手动对账任务执行完成: taskId={}", task.getId());
            return task.getId();

        } catch (Exception e) {
            log.error("手动触发对账任务异常: merchantId={}, channel={}, date={}", 
                merchantId, channel, reconciliationDate, e);
            return null;
        }
    }
}
