-- 对账任务表
CREATE TABLE `reconciliation_task` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `tenant_id` varchar(12) NOT NULL DEFAULT '000000' COMMENT '租户ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称：日期+渠道+对账单',
  `reconciliation_date` date NOT NULL COMMENT '对账日期',
  `task_create_time` datetime NOT NULL COMMENT '任务创建时间',
  `task_complete_time` datetime DEFAULT NULL COMMENT '任务完成时间',
  `task_status` int(2) NOT NULL DEFAULT '0' COMMENT '任务执行状态：0-创建中，1-执行中，2-已完成，3-获取数据失败，4-执行失败，5-已取消，6-等待重试，7-部分完成',
  `bill_name` varchar(200) DEFAULT NULL COMMENT '账单名称',
  `failure_reason` text COMMENT '失败原因',
  `cycle_type` int(2) NOT NULL COMMENT '对账周期类型：1-日，2-周，3-月，4-季，5-年，6-自定义时间',
  `cycle_start_date` date NOT NULL COMMENT '周期开始日期',
  `cycle_end_date` date NOT NULL COMMENT '周期结束日期',
  `channel` int(2) NOT NULL COMMENT '对账渠道：0-支付宝，1-微信，2-建行，3-工行，4-云闪付，5-农业银行，6-贵州银行，20-ETC支付',
  `merchant_id` varchar(32) NOT NULL COMMENT '商户ID',
  `bill_status` int(2) NOT NULL DEFAULT '0' COMMENT '账单状态：0-未对账，1-已对账，2-已分账，3-未付款，4-已付款，5-已撤销，6-已忽略',
  `third_party_total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '第三方支付渠道总金额',
  `third_party_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '第三方支付渠道总退款金额',
  `third_party_total_count` int(10) DEFAULT '0' COMMENT '第三方支付渠道总交易单数',
  `third_party_refund_count` int(10) DEFAULT '0' COMMENT '第三方支付渠道总退款单数',
  `platform_total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '本平台总金额',
  `platform_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '本平台总退款金额',
  `platform_total_count` int(10) DEFAULT '0' COMMENT '本平台总交易单数',
  `platform_refund_count` int(10) DEFAULT '0' COMMENT '本平台总退款单数',
  `reconciliation_result` int(1) DEFAULT '0' COMMENT '对账结果：0-不平，1-平账',
  `bill_total_id` bigint(20) DEFAULT NULL COMMENT '关联的对账单ID',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_deleted` int(2) NOT NULL DEFAULT '0' COMMENT '是否已删除：1-已删除，0-未删除',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_reconciliation_date` (`reconciliation_date`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_channel` (`channel`),
  KEY `idx_bill_status` (`bill_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_bill_total_id` (`bill_total_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账任务表';
