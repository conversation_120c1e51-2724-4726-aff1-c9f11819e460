-- 扩展现有 bill_detail 表添加处理状态字段
ALTER TABLE `bill_detail` 
ADD COLUMN `reconciliation_task_id` bigint(20) DEFAULT NULL COMMENT '关联的对账任务ID' AFTER `bill_total_id`,
ADD COLUMN `process_status` int(2) DEFAULT '0' COMMENT '处理状态：0-待处理，1-处理中，2-已完成，3-已调账' AFTER `handler_status`,
ADD COLUMN `process_time` datetime DEFAULT NULL COMMENT '处理时间' AFTER `process_status`,
ADD COLUMN `processor_name` varchar(50) DEFAULT NULL COMMENT '处理人姓名' AFTER `process_time`,
ADD INDEX `idx_reconciliation_task_id` (`reconciliation_task_id`),
ADD INDEX `idx_process_status` (`process_status`);
