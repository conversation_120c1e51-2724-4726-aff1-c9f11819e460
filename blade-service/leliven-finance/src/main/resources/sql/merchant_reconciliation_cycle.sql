-- 商户对账周期配置表
CREATE TABLE `merchant_reconciliation_cycle` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `tenant_id` varchar(12) NOT NULL DEFAULT '000000' COMMENT '租户ID',
  `merchant_id` varchar(32) NOT NULL COMMENT '商户ID',
  `merchant_name` varchar(100) NOT NULL COMMENT '商户名称',
  `cycle_type` int(2) NOT NULL COMMENT '周期类型：1-日，2-周，3-月，4-季，5-年，6-自定义时间',
  `cycle_value` int(5) DEFAULT '1' COMMENT '周期值（用于自定义周期）',
  `execute_time` time NOT NULL DEFAULT '08:00:00' COMMENT '执行时间',
  `auto_reconciliation` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用自动对账：1-启用，0-禁用',
  `start_date` date NOT NULL COMMENT '对账开始日期',
  `end_date` date DEFAULT NULL COMMENT '对账结束日期（可选，用于临时配置）',
  `supported_channels` varchar(200) DEFAULT NULL COMMENT '支持的对账渠道（多个渠道用逗号分隔）',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `last_execute_time` datetime DEFAULT NULL COMMENT '最后执行时间',
  `next_execute_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `failure_count` int(5) DEFAULT '0' COMMENT '执行失败次数',
  `max_retry_count` int(3) DEFAULT '3' COMMENT '最大重试次数',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `updater_name` varchar(50) DEFAULT NULL COMMENT '更新人姓名',
  `channel` int(2) NOT NULL COMMENT '对账渠道：0-支付宝，1-微信，2-建行，3-工行，4-云闪付，5-农业银行，6-贵州银行，20-ETC支付',
  `bill_status` int(2) NOT NULL DEFAULT '0' COMMENT '账单状态：0-未对账，1-已对账，2-已分账，3-未付款，4-已付款，5-已撤销，6-已忽略',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_deleted` int(2) NOT NULL DEFAULT '0' COMMENT '是否已删除：1-已删除，0-未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_channel` (`merchant_id`,`channel`,`is_deleted`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_cycle_type` (`cycle_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_auto_reconciliation` (`auto_reconciliation`),
  KEY `idx_next_execute_time` (`next_execute_time`),
  KEY `idx_channel` (`channel`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户对账周期配置表';
