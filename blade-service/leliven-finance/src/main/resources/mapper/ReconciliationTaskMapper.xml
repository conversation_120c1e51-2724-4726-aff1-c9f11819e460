<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leliven.finance.mapper.ReconciliationTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="reconciliationTaskResultMap" type="com.leliven.finance.entity.ReconciliationTask">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="task_name" property="taskName" />
        <result column="reconciliation_date" property="reconciliationDate" />
        <result column="task_create_time" property="taskCreateTime" />
        <result column="task_complete_time" property="taskCompleteTime" />
        <result column="task_status" property="taskStatus" />
        <result column="bill_name" property="billName" />
        <result column="failure_reason" property="failureReason" />
        <result column="cycle_type" property="cycleType" />
        <result column="cycle_start_date" property="cycleStartDate" />
        <result column="cycle_end_date" property="cycleEndDate" />
        <result column="channel" property="channel" />
        <result column="merchant_id" property="merchantId" />
        <result column="bill_status" property="billStatus" />
        <result column="third_party_total_amount" property="thirdPartyTotalAmount" />
        <result column="third_party_refund_amount" property="thirdPartyRefundAmount" />
        <result column="third_party_total_count" property="thirdPartyTotalCount" />
        <result column="third_party_refund_count" property="thirdPartyRefundCount" />
        <result column="platform_total_amount" property="platformTotalAmount" />
        <result column="platform_refund_amount" property="platformRefundAmount" />
        <result column="platform_total_count" property="platformTotalCount" />
        <result column="platform_refund_count" property="platformRefundCount" />
        <result column="reconciliation_result" property="reconciliationResult" />
        <result column="bill_total_id" property="billTotalId" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, task_name, reconciliation_date, task_create_time, task_complete_time,
        task_status, bill_name, failure_reason, cycle_type, cycle_start_date, cycle_end_date,
        channel, merchant_id, bill_status, third_party_total_amount, third_party_refund_amount,
        third_party_total_count, third_party_refund_count, platform_total_amount, platform_refund_amount,
        platform_total_count, platform_refund_count, reconciliation_result, bill_total_id,
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询对账任务 -->
    <select id="selectReconciliationTaskPage" resultType="com.leliven.finance.vo.ReconciliationTaskVO">
        SELECT
        <include refid="baseColumnList" />
        FROM reconciliation_task
        <where>
            is_deleted = 0
            <if test="reconciliationTask.tenantId != null and reconciliationTask.tenantId != ''">
                AND tenant_id = #{reconciliationTask.tenantId}
            </if>
            <if test="reconciliationTask.merchantId != null and reconciliationTask.merchantId != ''">
                AND merchant_id = #{reconciliationTask.merchantId}
            </if>
            <if test="reconciliationTask.merchantIds != null and reconciliationTask.merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="reconciliationTask.merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            <if test="reconciliationTask.channel != null">
                AND channel = #{reconciliationTask.channel}
            </if>
            <if test="reconciliationTask.channels != null and reconciliationTask.channels.size() > 0">
                AND channel IN
                <foreach collection="reconciliationTask.channels" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="reconciliationTask.taskStatus != null">
                AND task_status = #{reconciliationTask.taskStatus}
            </if>
            <if test="reconciliationTask.taskStatuses != null and reconciliationTask.taskStatuses.size() > 0">
                AND task_status IN
                <foreach collection="reconciliationTask.taskStatuses" item="taskStatus" open="(" separator="," close=")">
                    #{taskStatus}
                </foreach>
            </if>
            <if test="reconciliationTask.billStatus != null">
                AND bill_status = #{reconciliationTask.billStatus}
            </if>
            <if test="reconciliationTask.reconciliationResult != null">
                AND reconciliation_result = #{reconciliationTask.reconciliationResult}
            </if>
            <if test="reconciliationTask.startDate != null">
                AND reconciliation_date >= #{reconciliationTask.startDate}
            </if>
            <if test="reconciliationTask.endDate != null">
                AND reconciliation_date &lt;= #{reconciliationTask.endDate}
            </if>
            <if test="reconciliationTask.taskName != null and reconciliationTask.taskName != ''">
                AND task_name LIKE CONCAT('%', #{reconciliationTask.taskName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据商户和渠道查询最新的对账任务 -->
    <select id="selectLatestTaskByMerchantAndChannel" resultMap="reconciliationTaskResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM reconciliation_task
        WHERE merchant_id = #{merchantId}
        AND channel = #{channel}
        AND is_deleted = 0
        ORDER BY reconciliation_date DESC, create_time DESC
        LIMIT 1
    </select>

    <!-- 查询指定状态的对账任务 -->
    <select id="selectTasksByStatus" resultMap="reconciliationTaskResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM reconciliation_task
        WHERE task_status = #{taskStatus}
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 查询需要重试的对账任务 -->
    <select id="selectTasksNeedRetry" resultMap="reconciliationTaskResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM reconciliation_task
        WHERE task_status IN (3, 4, 6)
        AND is_deleted = 0
        AND (failure_count IS NULL OR failure_count &lt; 3)
        ORDER BY create_time ASC
    </select>

    <!-- 获取对账任务统计信息 -->
    <select id="selectTaskStatistics" resultType="com.leliven.finance.vo.ReconciliationTaskVO">
        SELECT
        COUNT(*) as totalTaskCount,
        COUNT(CASE WHEN task_status = 2 THEN 1 END) as successTaskCount,
        COUNT(CASE WHEN task_status IN (3, 4) THEN 1 END) as failedTaskCount,
        COUNT(CASE WHEN task_status = 1 THEN 1 END) as executingTaskCount,
        COUNT(CASE WHEN reconciliation_result = 1 THEN 1 END) as balancedTaskCount,
        COUNT(CASE WHEN reconciliation_result = 0 THEN 1 END) as unbalancedTaskCount,
        ROUND(COUNT(CASE WHEN task_status = 2 THEN 1 END) * 100.0 / COUNT(*), 2) as successRate,
        ROUND(COUNT(CASE WHEN reconciliation_result = 1 THEN 1 END) * 100.0 / COUNT(CASE WHEN task_status = 2 THEN 1 END), 2) as balanceRate
        FROM reconciliation_task
        <where>
            is_deleted = 0
            <if test="startDate != null">
                AND reconciliation_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND reconciliation_date &lt;= #{endDate}
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND merchant_id = #{merchantId}
            </if>
            <if test="channel != null">
                AND channel = #{channel}
            </if>
        </where>
    </select>

    <!-- 删除过期的对账任务 -->
    <update id="deleteExpiredTasks">
        UPDATE reconciliation_task
        SET is_deleted = 1, update_time = NOW()
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{expireDays} DAY)
        AND is_deleted = 0
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus">
        UPDATE reconciliation_task
        SET task_status = #{taskStatus}, update_time = NOW()
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 更新任务失败次数 -->
    <update id="incrementFailureCount">
        UPDATE reconciliation_task
        SET failure_count = IFNULL(failure_count, 0) + 1,
        update_time = NOW()
        WHERE id = #{taskId}
        AND is_deleted = 0
    </update>

    <!-- 重置任务失败次数 -->
    <update id="resetFailureCount">
        UPDATE reconciliation_task
        SET failure_count = 0,
        update_time = NOW()
        WHERE id = #{taskId}
        AND is_deleted = 0
    </update>

</mapper>
