<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leliven.finance.mapper.MerchantReconciliationCycleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="merchantReconciliationCycleResultMap" type="com.leliven.finance.entity.MerchantReconciliationCycle">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="merchant_id" property="merchantId" />
        <result column="merchant_name" property="merchantName" />
        <result column="cycle_type" property="cycleType" />
        <result column="cycle_value" property="cycleValue" />
        <result column="execute_time" property="executeTime" />
        <result column="auto_reconciliation" property="autoReconciliation" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="supported_channels" property="supportedChannels" />
        <result column="description" property="description" />
        <result column="enabled" property="enabled" />
        <result column="last_execute_time" property="lastExecuteTime" />
        <result column="next_execute_time" property="nextExecuteTime" />
        <result column="failure_count" property="failureCount" />
        <result column="max_retry_count" property="maxRetryCount" />
        <result column="creator_name" property="creatorName" />
        <result column="updater_name" property="updaterName" />
        <result column="channel" property="channel" />
        <result column="bill_status" property="billStatus" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, tenant_id, merchant_id, merchant_name, cycle_type, cycle_value, execute_time,
        auto_reconciliation, start_date, end_date, supported_channels, description, enabled,
        last_execute_time, next_execute_time, failure_count, max_retry_count, creator_name,
        updater_name, channel, bill_status, create_user, create_dept, create_time,
        update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询商户对账周期配置 -->
    <select id="selectMerchantReconciliationCyclePage" resultType="com.leliven.finance.vo.MerchantReconciliationCycleVO">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        <where>
            is_deleted = 0
            <if test="merchantReconciliationCycle.tenantId != null and merchantReconciliationCycle.tenantId != ''">
                AND tenant_id = #{merchantReconciliationCycle.tenantId}
            </if>
            <if test="merchantReconciliationCycle.merchantId != null and merchantReconciliationCycle.merchantId != ''">
                AND merchant_id = #{merchantReconciliationCycle.merchantId}
            </if>
            <if test="merchantReconciliationCycle.merchantIds != null and merchantReconciliationCycle.merchantIds.size() > 0">
                AND merchant_id IN
                <foreach collection="merchantReconciliationCycle.merchantIds" item="merchantId" open="(" separator="," close=")">
                    #{merchantId}
                </foreach>
            </if>
            <if test="merchantReconciliationCycle.merchantName != null and merchantReconciliationCycle.merchantName != ''">
                AND merchant_name LIKE CONCAT('%', #{merchantReconciliationCycle.merchantName}, '%')
            </if>
            <if test="merchantReconciliationCycle.channel != null">
                AND channel = #{merchantReconciliationCycle.channel}
            </if>
            <if test="merchantReconciliationCycle.channels != null and merchantReconciliationCycle.channels.size() > 0">
                AND channel IN
                <foreach collection="merchantReconciliationCycle.channels" item="channel" open="(" separator="," close=")">
                    #{channel}
                </foreach>
            </if>
            <if test="merchantReconciliationCycle.cycleType != null">
                AND cycle_type = #{merchantReconciliationCycle.cycleType}
            </if>
            <if test="merchantReconciliationCycle.cycleTypes != null and merchantReconciliationCycle.cycleTypes.size() > 0">
                AND cycle_type IN
                <foreach collection="merchantReconciliationCycle.cycleTypes" item="cycleType" open="(" separator="," close=")">
                    #{cycleType}
                </foreach>
            </if>
            <if test="merchantReconciliationCycle.enabled != null">
                AND enabled = #{merchantReconciliationCycle.enabled}
            </if>
            <if test="merchantReconciliationCycle.enabledStatuses != null and merchantReconciliationCycle.enabledStatuses.size() > 0">
                AND enabled IN
                <foreach collection="merchantReconciliationCycle.enabledStatuses" item="enabledStatus" open="(" separator="," close=")">
                    #{enabledStatus}
                </foreach>
            </if>
            <if test="merchantReconciliationCycle.autoReconciliation != null">
                AND auto_reconciliation = #{merchantReconciliationCycle.autoReconciliation}
            </if>
            <if test="merchantReconciliationCycle.startDate != null">
                AND start_date >= #{merchantReconciliationCycle.startDate}
            </if>
            <if test="merchantReconciliationCycle.endDate != null">
                AND (end_date IS NULL OR end_date &lt;= #{merchantReconciliationCycle.endDate})
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据商户ID和渠道查询配置 -->
    <select id="selectByMerchantAndChannel" resultMap="merchantReconciliationCycleResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        WHERE merchant_id = #{merchantId}
        AND channel = #{channel}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectEnabledConfigs" resultMap="merchantReconciliationCycleResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        WHERE enabled = 1
        AND auto_reconciliation = 1
        AND is_deleted = 0
        AND (end_date IS NULL OR end_date >= CURDATE())
        ORDER BY next_execute_time ASC
    </select>

    <!-- 查询需要执行的配置（到达执行时间） -->
    <select id="selectConfigsToExecute" resultMap="merchantReconciliationCycleResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        WHERE enabled = 1
        AND auto_reconciliation = 1
        AND is_deleted = 0
        AND next_execute_time &lt;= #{currentTime}
        AND (end_date IS NULL OR end_date >= CURDATE())
        AND (failure_count IS NULL OR failure_count &lt; max_retry_count)
        ORDER BY next_execute_time ASC
    </select>

    <!-- 批量更新启用状态 -->
    <update id="batchUpdateEnabled">
        UPDATE merchant_reconciliation_cycle
        SET enabled = #{enabled}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 根据商户ID查询所有配置 -->
    <select id="selectByMerchantId" resultMap="merchantReconciliationCycleResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        WHERE merchant_id = #{merchantId}
        AND is_deleted = 0
        ORDER BY channel ASC, create_time DESC
    </select>

    <!-- 根据渠道查询所有配置 -->
    <select id="selectByChannel" resultMap="merchantReconciliationCycleResultMap">
        SELECT
        <include refid="baseColumnList" />
        FROM merchant_reconciliation_cycle
        WHERE channel = #{channel}
        AND is_deleted = 0
        ORDER BY merchant_id ASC, create_time DESC
    </select>

    <!-- 删除商户的所有配置 -->
    <update id="deleteByMerchantId">
        UPDATE merchant_reconciliation_cycle
        SET is_deleted = 1, update_time = NOW()
        WHERE merchant_id = #{merchantId}
        AND is_deleted = 0
    </update>

    <!-- 更新执行时间 -->
    <update id="updateExecuteTime">
        UPDATE merchant_reconciliation_cycle
        SET
        <if test="lastExecuteTime != null">
            last_execute_time = #{lastExecuteTime},
        </if>
        <if test="nextExecuteTime != null">
            next_execute_time = #{nextExecuteTime},
        </if>
        update_time = NOW()
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 增加失败次数 -->
    <update id="incrementFailureCount">
        UPDATE merchant_reconciliation_cycle
        SET failure_count = IFNULL(failure_count, 0) + 1,
        update_time = NOW()
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 重置失败次数 -->
    <update id="resetFailureCount">
        UPDATE merchant_reconciliation_cycle
        SET failure_count = 0,
        update_time = NOW()
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 获取配置统计信息 -->
    <select id="selectConfigStatistics" resultType="com.leliven.finance.vo.MerchantReconciliationCycleVO">
        SELECT
        COUNT(*) as totalConfigCount,
        COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabledConfigCount,
        COUNT(CASE WHEN enabled = 0 THEN 1 END) as disabledConfigCount,
        COUNT(CASE WHEN auto_reconciliation = 1 THEN 1 END) as autoReconciliationConfigCount,
        COUNT(CASE WHEN failure_count >= max_retry_count THEN 1 END) as abnormalConfigCount,
        COUNT(CASE WHEN DATE(next_execute_time) = CURDATE() THEN 1 END) as todayExecutionCount,
        ROUND(AVG(IFNULL(failure_count, 0)), 2) as averageFailureCount,
        ROUND(COUNT(CASE WHEN enabled = 1 AND (failure_count IS NULL OR failure_count &lt; max_retry_count) THEN 1 END) * 100.0 / COUNT(*), 2) as configHealthRate
        FROM merchant_reconciliation_cycle
        WHERE is_deleted = 0
    </select>

</mapper>
