# 停车系统对账管理功能需求

## 1. 概述

对账管理功能旨在通过按周期获取商户账单，生成对账任务，并最终形成对账单和对账结果明细。该功能支持查看和处理待处理的对账单，并提供已归档对账单的查询。

------

## 2. 功能模块

对账管理功能包括以下三个子模块：

### 2.1 对账记录

此模块用于展示所有对账任务的执行情况，包括任务的创建、执行状态和结果。

#### **核心信息**

- **任务名称：** 任务的唯一标识，命名结构为：**`日期+渠道+对账单`**。例如：`20211002微信对账单`。
- **对账渠道：** 任务所针对的支付渠道（如微信、支付宝等）。
- **任务创建时间：** 任务发起的时间。
- **任务完成时间：** 任务执行完毕的时间。
- **任务执行状态：** 任务当前的执行状态，如“执行中”、“已完成”、“获取数据失败”等。
- **账单名称：** 对账任务所使用的账单文件名称。
- **失败原因：** 当任务执行失败（如数据获取异常）时，记录具体失败原因。

### 2.2 待处理对账单

此模块用于展示所有需要人工干预和处理的对账单。

#### **核心信息**

- **账单名称：** 对账单的名称，命名结构为：**`周期+渠道+对账单`**。例如：`2021年09月微信对账单`、`2022年微信对账单`。
- **对账渠道：** 账单所属的支付渠道。
- **第三方支付渠道账单信息：**
  - 总金额
  - 总退款金额
  - 总交易单数
  - 总退款单数
- **本平台账单信息：**
  - 总金额
  - 总退款金额
  - 总交易单数
  - 总退款单数
- **对账结果：**
  - **平账：** 第三方支付渠道与本平台数据完全一致。
  - **不平：** 第三方支付渠道与本平台数据存在差异。
- **账单状态：** 账单当前的状态，默认为“待处理”。

#### **操作功能**

- **处理不平记录：** 用户可进入对账单详情，对其中“不平”的对账结果进行处理。
- **标记为已调账：** 用户可以手动将一条不平的对账结果明细标记为“已调账”，并在此操作时**写入备注**。系统只做状态标记，不做其他数据处理。
- **完成处理：** 当所有不平记录都被处理（即标记为“已调账”）后，用户可以将对账单的状态改为“已完成”。

### 2.3 已归档对账单
此模块用于展示所有已完成处理的对账单，可供查询和审计。

------

## 3. 对账结果明细

每张对账单的详细内容，用于展示具体交易层面的对账结果。

#### **核心信息**

- **第三方支付渠道流水账单信息：**
  - **订单号**
  - **交易类型：** 支付、退款
  - **交易时间**
  - **交易状态：** 成功、失败
  - **金额**
- **本平台流水明细账单信息：**
  - **订单号**
  - **交易类型：** 支付、退款
  - **交易时间**
  - **交易状态：** 成功、失败
  - **金额**
- **对账结果：**
  - **正常：** 第三方支付与本平台流水匹配。
  - **异常：** 第三方支付与本平台流水不匹配。
  - **已调账：** 人工处理后标记的状态。
- **备注：** 针对异常或已调账对账结果的补充说明。

------

## 4. 商户对账周期管理

此功能用于配置每个商户的对账周期，系统将根据配置自动生成对账任务。

#### **周期类型**

- **日**
- **周**
- **月**
- **季**
- **年**
- **自定义时间**