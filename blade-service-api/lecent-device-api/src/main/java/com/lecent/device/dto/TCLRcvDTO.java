package com.lecent.device.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 海康TCL设备ISAPI事件接受类
 */
@Data
@XStreamAlias("EventNotificationAlert")
@EqualsAndHashCode(callSuper = false)
public class TCLRcvDTO {

	/**
	 * 报警设备IPv4地址
	 */
	@XStreamAlias("ipAddress")
	private String ipAddress;

	/**
	 * 报警设备IPv6地址
	 */
	@XStreamAlias("ipv6Address")
	private String ipv6Address;

	/**
	 * 报警设备端口号
	 */
	@XStreamAlias("portNo")
	private String portNo;

	/**
	 * 传输通信协议类型
	 */
	@XStreamAlias("protocol")
	private String protocol;

	/**
	 * MAC地址
	 */
	@XStreamAlias("macAddress")
	private String macAddress;

	/**
	 * 数字通道号
	 */
	@XStreamAlias("channelID")
	private String channelID;

	/**
	 * 报警触发时间
	 */
	@XStreamAlias("dateTime")
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX", timezone = "GMT+8")
	private Date dateTime;

	/**
	 * 同一个报警已经上传的次数
	 */
	@XStreamAlias("activePostCount")
	private String activePostCount;

	/**
	 * 事件类型 TFS-交通取证
	 */
	@XStreamAlias("eventType")
	private String eventType;

	/**
	 * 事件状态 active#有效事件,inactive#无效事件
	 */
	@XStreamAlias("eventState")
	private String eventState;

	/**
	 * 事件描述
	 */
	@XStreamAlias("eventDescription")
	private String eventDescription;

	/**
	 * 通道名称
	 */
	@XStreamAlias("channelName")
	private String channelName;

	/**
	 * 设备ID
	 */
	@XStreamAlias("deviceID")
	private String deviceID;

	/**
	 * 图片传输方式 [url#图片url,binary#二进制]
	 */
	@XStreamAlias("detectionPictureTransType")
	private String detectionPictureTransType;

	/**
	 * 接收报警中包含的图片数量
	 */
	@XStreamAlias("detectionPicturesNumber")
	private String detectionPicturesNumber;

	/**
	 * 序列号
	 */
	@JsonProperty("serialNumber")
	private String serialNumber;

	/**
	 * 摄像头信息
	 */
	@JsonProperty("CameraInfo")
	private Object cameraInfo;

	/**
	 * 停车位识别信息
	 */
	@JsonProperty("PackingSpaceRecognition")
	private List<PackingSpaceRecognition> packingSpaceRecognition;

	/**
	 * 场景模式
	 */
	@JsonProperty("sceneMode")
	private String sceneMode;

	/**
	 * 图片资源
	 */
	@JsonProperty("PicResources")
	private PicResources picResources;

	/**
	 * 车牌图片资源
	 */
	@JsonProperty("PlateImageResources")
	private PlateImageResources plateImageResources;

	private TFS tfs;

	@Data
	public class TFS {
		/**
		 * 交通取证事件信息
		 * [abandonedObject#抛洒物,
		 * checkPoint#卡口检测,
		 * conflagration#火灾,
		 * congestion#拥堵,
		 * construction#施工,
		 * crossLane#压线,
		 * edfManual#手动取证,
		 * fogDetection#浓雾检测,
		 * gasser#加塞,
		 * group#人员聚集,
		 * illegalParking#违停,
		 * intersectionStranded#车辆滞留,
		 * laneChange#变道,
		 * objectDroppedDown#物体坠落,
		 * occupyOvertakingLane#占用超车道,
		 * parallelParking#侧方位停车,
		 * pedestrian#行人检测,
		 * polyJam#多边形拥堵 鹰眼拥堵,
		 * prohibitionMarkViolation#违反禁令,
		 * roadBlock#路障,slowMoving#车辆缓行,
		 * smoke#烟雾,
		 * speed#车辆超速,
		 * suddenSpeedDrop#速度骤降,
		 * trafficAccident#交通事故检测,
		 * trafficConflict#车流冲突,
		 * turnRound#掉头,
		 * unknown#未知,
		 * vehicleexist#机占非,
		 * wrongDirection#逆行,
		 * blackSmokeVehicle#黑烟车,
		 * occupyDedicatedLane#占用专用车道,
		 * notDriveInDedicatedLane#未按专用车道行驶,
		 * nonZipperMerge#未交替同行,
		 * SSharpDriving#蛇形行驶,
		 * notKeepDistance#未保持车距,
		 * notSlowZebraCrossing#斑马线未减速,
		 * overtakeRightSide#右侧超车,
		 * lowSpeed#机动车低速行驶,
		 * dragRacing#飙车,
		 * changeLaneContinuously#连续变道,
		 * largeVehicleOccupyLine#大车占道,
		 * emergencyLane#占用应急车道,
		 * lowVisibility#低环境能见度,
		 * overSpeed#车辆超速行驶]
		 */
		@XStreamAlias("illegalTrafficEvent")
		private String illegalTrafficEvent;

		/**
		 * 车辆场景图片ID
		 */
		@XStreamAlias("contentIDSence1")
		private String contentIDSenceOne;

		/**
		 * 车辆场景图片ID
		 */
		@XStreamAlias("contentIDSence2")
		private String contentIDSenceTwo;

		/**
		 * 车牌图片ID
		 */
		@XStreamAlias("contentIDVehiclePlate")
		private String contentIDVehiclePlate;

		/**
		 * 违章类型
		 */
		@XStreamAlias("illegalType")
		private String illegalType;

		/**
		 * 泊车编号
		 */
		@XStreamAlias("parkingSerialNo")
		private String parkingSerialNo;

		/**
		 * 车牌信息
		 */
		@XStreamAlias("PlateInfo")
		private PlateInfo plateInfo;

		/**
		 * 车辆信息
		 */
		@XStreamAlias("VehicleInfo")
		private VehicleInfo vehicleInfo;
	}

	@Data
	public class PlateInfo {
		/**
		 * 车牌
		 */
		@XStreamAlias("plate")
		private String plate;

		/**
		 * 车牌类型
		 *  [02TypePersonalized#02式个性化车,
		 *  04NewMilitay#04式新军车,
		 *  92FarmVehicle#民用车双行尾牌（补录）,
		 *  92TypeArm#92式武警车,
		 *  92TypeCivil#92式民用车,
		 *  arm#警车,
		 *  civilAviation#民航车牌,
		 *  coach#教练车,
		 *  consulate#领馆汽车,
		 *  embassy#使馆车,
		 *  emergency#应急车牌,
		 *  green1325FarmVehicle#绿色1325农用车,
		 *  hongKongMacao#港澳入出车,
		 *  leftRightMilitay#左右军车,
		 *  motorola#摩托车,
		 *  newEnergy#新能源车牌,
		 *  oneLineArm#一行结构的新武警车,
		 *  oneLineArmHeadquarters#一行结构武警总部车牌,
		 *  tempEntry#临时入境车,tempTravl#临时行驶车,trailer#挂车,
		 *  twoLineArm#两行结构的新武警车,
		 *  twoLineArmHeadquarters#两行结构武警总部车牌,
		 *  unknown#未知,
		 *  upDownMilitay#上下军车,
		 *  yellow1225FarmVehicle#黄色1225农用车,
		 *  yellow1325FarmVehicle#黄色1325结构农用车,
		 *  yellowTwoLine#黄色双行尾牌]
		 */
		@XStreamAlias("plateType")
		private String plateType;

		/**
		 * 车牌颜色
		 *  [black#黑色,
		 *  blue#蓝色,
		 *  yellow#黄色,
		 *  white#白色,
		 *  civilAviationBlack#民航黑色,
		 *  civilAviationGreen#民航绿色,
		 *  golden#金色,
		 *  green#绿色,
		 *  mixedColor#花底,
		 *  newEnergyGreen#新能源绿色,
		 *  newEnergyYellowGreen#新能源黄绿色,
		 *  orange#橙色,
		 *  other#其他颜色,
		 *  red#红色,
		 *  unknown#未知}
		 */
		@XStreamAlias("plateColor")
		private String plateColor;

		/**
		 *车牌置信度
		 */
		@XStreamAlias("confidence")
		private String confidence;


	}

	/**
	 * 车辆信息
	 */
	@Data
	public class VehicleInfo {

		/**
		 * 车辆类型
		 * [SUVMPV#SUV/MPV,buggy#小货车,
		 * bus#客车,concreteMixer#混凝土搅拌车,
		 * containerTruck#集装箱卡车,coupe#轿跑,
		 * crane#吊车,hatchback#两厢轿车,
		 * largeBus#大型客车,
		 * lightTruck#轻微货车,
		 * mediumBus#中型客车,
		 * mediumHeavyTruck#中重型货车,
		 * miniCar#微型轿车（平台转成：轿车）,
		 * minibus#小型客车,
		 * minitruck#微卡,
		 * motorVehicle#机动车（平台转成：轿车）,
		 * nonmotorVehicle#非机动车（平台转成：三轮车）,oilTankTruck#油罐车,pedestrian#行人,pickupTruck#皮卡车（平台转成：小货车）,platformTrailer#平板拖车,saloon#三厢轿车,slagTruck#渣土车,smallCar#小型轿车（平台转成：轿车）,threeWheelVehicle#三轮车,truck#货车,twoWheelVehicle#二轮车,unknown#未知,van#面包车,vehicle#轿车]
		 */
		@XStreamAlias("vehicleType")
		private String vehicleType;

		/**
		 *车辆进入状态
		 *  [vehicleEnter#车辆进入,vehicleExit#车辆离开]
		 */
		@XStreamAlias("vehicleEnterState")
		private String vehicleEnterState;

		/**
		 * 车辆置信度
		 */
		@XStreamAlias("vehicleConfidence")
		private String vehicleConfidence;

	}

	/**
	 * 停车位识别信息
	 */
	@Data
	public static class PackingSpaceRecognition {
		/**
		 * 是否停车
		 */
		@JsonProperty("isParked")
		private String isParked;

		/**
		 * 车牌号
		 */
		@JsonProperty("plateNo")
		private String plateNo;

		/**
		 * 绝对停车位编号
		 */
		@JsonProperty("absoulteParkingNum")
		private String absoulteParkingNum;

		/**
		 * 区域ID
		 */
		@JsonProperty("regionalID")
		private Integer regionalID;

		/**
		 * 相对停车位编号
		 */
		@JsonProperty("relativeParkingNum")
		private Integer relativeParkingNum;

		/**
		 * 检测模式
		 */
		@JsonProperty("detectMode")
		private String detectMode;
	}

	/**
	 * 图片资源
	 */
	@Data
	public static class PicResources {
		/**
		 * 资源内容类型
		 */
		@JsonProperty("resourcesContentType")
		private String resourcesContentType;

		/**
		 * 资源内容
		 */
		@JsonProperty("resourcesContent")
		private String resourcesContent;
	}

	/**
	 * 车牌图片资源
	 */
	@Data
	public static class PlateImageResources {
		/**
		 * 资源内容类型
		 */
		@JsonProperty("resourcesContentType")
		private String resourcesContentType;

		/**
		 * 资源内容
		 */
		@JsonProperty("resourcesContent")
		private String resourcesContent;
	}

}
