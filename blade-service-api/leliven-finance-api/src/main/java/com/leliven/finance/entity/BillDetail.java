package com.leliven.finance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 对账明细表实体类
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillDetail对象", description = "对账明细表")
public class BillDetail extends BaseBill {
    private static final long serialVersionUID = 1L;
    /**
     * 对账时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "对账日期")
    private LocalDate date;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer type;
    /**
     * 车场id
     */
    @ApiModelProperty(value = "车场id")
    private Long parklotId;
    /**
     * 商户-订单号
     */
    @ApiModelProperty(value = "商户-订单号")
    private String mTradeNo;
    /**
     * 商户-交易金额
     */
    @ApiModelProperty(value = "商户-交易金额")
    private BigDecimal mReceiveAmount;
    /**
     * 商户-手续费
     */
    @ApiModelProperty(value = "商户-手续费")
    private BigDecimal mServiceFee;
    /**
     * 商户-结算金额
     */
    @ApiModelProperty(value = "商户-结算金额")
    private BigDecimal mSettlementAmount;
    /**
     * 商户-交易时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "商户-交易时间")
    private LocalDateTime mPayTime;
    /**
     * 停车-订单编号
     */
    @ApiModelProperty(value = "停车-订单编号")
    private String bTradeNo;
    /**
     * 停车-实收金额
     */
    @ApiModelProperty(value = "停车-实收金额")
    private BigDecimal bReceiveAmount;
    /**
     * 停车-退款金额
     */
    @ApiModelProperty(value = "停车-退款金额")
    private BigDecimal bRefundAmount;
    /**
     * 停车-支付时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "停车-支付时间")
    private LocalDateTime bPayTime;
    /**
     * 停车-车牌
     */
    @ApiModelProperty(value = "停车-车牌")
    private String bPlate;
    /**
     * 停车-订单状态：0-异常，1-正常
     */
    @ApiModelProperty(value = "停车-订单状态：0-异常，1-正常")
    private Integer bStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 处理状态：0-未处理，1-已处理
     */
    @ApiModelProperty(value = "处理状态：0-未处理，1-已处理")
    private Integer handlerStatus;
    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    private String handlerRemark;
    /**
     * 对账单id
     */
    @ApiModelProperty(value = "对账单id")
    private Long billTotalId;

    public BillDetail() {
        super();
        this.mReceiveAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.mServiceFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.mSettlementAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.bReceiveAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.bRefundAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.remark = "";
    }
}
