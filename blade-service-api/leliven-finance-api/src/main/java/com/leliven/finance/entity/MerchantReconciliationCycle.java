package com.leliven.finance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 商户对账周期配置实体类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MerchantReconciliationCycle对象", description = "商户对账周期配置表")
public class MerchantReconciliationCycle extends BaseBill {
    private static final long serialVersionUID = 1L;

    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /**
     * 周期类型
     * {@link com.leliven.finance.enums.ReconciliationCycleType}
     */
    @ApiModelProperty(value = "周期类型")
    private Integer cycleType;

    /**
     * 周期值（用于自定义周期）
     * 例如：每3天、每2周、每6个月等
     */
    @ApiModelProperty(value = "周期值")
    private Integer cycleValue;

    /**
     * 执行时间（小时）
     */
    @DateTimeFormat(pattern = "HH:mm:ss")
    @JsonFormat(pattern = "HH:mm:ss")
    @ApiModelProperty(value = "执行时间")
    private LocalTime executeTime;

    /**
     * 是否启用自动对账
     */
    @ApiModelProperty(value = "是否启用自动对账")
    private Boolean autoReconciliation;

    /**
     * 对账开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "对账开始日期")
    private LocalDate startDate;

    /**
     * 对账结束日期（可选，用于临时配置）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "对账结束日期")
    private LocalDate endDate;

    /**
     * 支持的对账渠道（多个渠道用逗号分隔）
     */
    @ApiModelProperty(value = "支持的对账渠道")
    private String supportedChannels;

    /**
     * 配置描述
     */
    @ApiModelProperty(value = "配置描述")
    private String description;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    /**
     * 最后执行时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后执行时间")
    private java.time.LocalDateTime lastExecuteTime;

    /**
     * 下次执行时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下次执行时间")
    private java.time.LocalDateTime nextExecuteTime;

    /**
     * 执行失败次数
     */
    @ApiModelProperty(value = "执行失败次数")
    private Integer failureCount;

    /**
     * 最大重试次数
     */
    @ApiModelProperty(value = "最大重试次数")
    private Integer maxRetryCount;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;
}
