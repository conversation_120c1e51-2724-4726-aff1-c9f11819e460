package com.leliven.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对账任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Getter
@AllArgsConstructor
public enum ReconciliationTaskStatus {
    /**
     * 创建中
     */
    CREATING(0, "创建中"),
    
    /**
     * 执行中
     */
    EXECUTING(1, "执行中"),
    
    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),
    
    /**
     * 获取数据失败
     */
    DATA_FETCH_FAILED(3, "获取数据失败"),
    
    /**
     * 执行失败
     */
    EXECUTION_FAILED(4, "执行失败"),
    
    /**
     * 已取消
     */
    CANCELLED(5, "已取消"),
    
    /**
     * 等待重试
     */
    WAITING_RETRY(6, "等待重试"),
    
    /**
     * 部分完成
     */
    PARTIALLY_COMPLETED(7, "部分完成");

    private final int value;
    private final String desc;

    /**
     * 根据值获取描述
     *
     * @param value 状态值
     * @return 状态描述
     */
    public static String getDescByValue(Integer value) {
        for (ReconciliationTaskStatus status : values()) {
            if (value.equals(status.getValue())) {
                return status.desc;
            }
        }
        return "";
    }

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 枚举对象
     */
    public static ReconciliationTaskStatus getByValue(Integer value) {
        for (ReconciliationTaskStatus status : values()) {
            if (value.equals(status.getValue())) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为最终状态
     *
     * @return true-最终状态，false-中间状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == DATA_FETCH_FAILED || 
               this == EXECUTION_FAILED || this == CANCELLED;
    }

    /**
     * 判断是否为失败状态
     *
     * @return true-失败状态，false-非失败状态
     */
    public boolean isFailureStatus() {
        return this == DATA_FETCH_FAILED || this == EXECUTION_FAILED;
    }

    /**
     * 判断是否可以重试
     *
     * @return true-可以重试，false-不可重试
     */
    public boolean canRetry() {
        return this == DATA_FETCH_FAILED || this == EXECUTION_FAILED || this == WAITING_RETRY;
    }
}
