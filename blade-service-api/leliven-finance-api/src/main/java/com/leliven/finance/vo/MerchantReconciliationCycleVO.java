package com.leliven.finance.vo;

import com.leliven.finance.entity.MerchantReconciliationCycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 商户对账周期配置视图对象
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MerchantReconciliationCycleVO对象", description = "商户对账周期配置视图对象")
public class MerchantReconciliationCycleVO extends MerchantReconciliationCycle {
    private static final long serialVersionUID = 1L;

    /**
     * 对账渠道名称
     */
    @ApiModelProperty(value = "对账渠道名称")
    private String channelName;

    /**
     * 周期类型名称
     */
    @ApiModelProperty(value = "周期类型名称")
    private String cycleTypeName;

    /**
     * 账单状态名称
     */
    @ApiModelProperty(value = "账单状态名称")
    private String billStatusName;

    /**
     * 启用状态名称
     */
    @ApiModelProperty(value = "启用状态名称")
    private String enabledStatusName;

    /**
     * 自动对账状态名称
     */
    @ApiModelProperty(value = "自动对账状态名称")
    private String autoReconciliationStatusName;

    /**
     * 支持的渠道名称列表
     */
    @ApiModelProperty(value = "支持的渠道名称列表")
    private String supportedChannelNames;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 是否超过最大重试次数
     */
    @ApiModelProperty(value = "是否超过最大重试次数")
    private Boolean exceedsMaxRetry;

    /**
     * 距离下次执行的时间（小时）
     */
    @ApiModelProperty(value = "距离下次执行的时间（小时）")
    private Long hoursToNextExecution;

    /**
     * 配置状态：正常、异常、禁用
     */
    @ApiModelProperty(value = "配置状态")
    private String configStatus;

    // 查询条件字段
    /**
     * 查询开始日期
     */
    @ApiModelProperty(value = "查询开始日期")
    private LocalDate startDate;

    /**
     * 查询结束日期
     */
    @ApiModelProperty(value = "查询结束日期")
    private LocalDate endDate;

    /**
     * 商户ID列表（用于批量查询）
     */
    @ApiModelProperty(value = "商户ID列表")
    private List<String> merchantIds;

    /**
     * 渠道列表（用于批量查询）
     */
    @ApiModelProperty(value = "渠道列表")
    private List<Integer> channels;

    /**
     * 周期类型列表（用于批量查询）
     */
    @ApiModelProperty(value = "周期类型列表")
    private List<Integer> cycleTypes;

    /**
     * 启用状态列表（用于批量查询）
     */
    @ApiModelProperty(value = "启用状态列表")
    private List<Boolean> enabledStatuses;

    // 统计字段
    /**
     * 总配置数
     */
    @ApiModelProperty(value = "总配置数")
    private Integer totalConfigCount;

    /**
     * 启用配置数
     */
    @ApiModelProperty(value = "启用配置数")
    private Integer enabledConfigCount;

    /**
     * 禁用配置数
     */
    @ApiModelProperty(value = "禁用配置数")
    private Integer disabledConfigCount;

    /**
     * 自动对账配置数
     */
    @ApiModelProperty(value = "自动对账配置数")
    private Integer autoReconciliationConfigCount;

    /**
     * 异常配置数（失败次数超过最大重试次数）
     */
    @ApiModelProperty(value = "异常配置数")
    private Integer abnormalConfigCount;

    /**
     * 今日需要执行的配置数
     */
    @ApiModelProperty(value = "今日需要执行的配置数")
    private Integer todayExecutionCount;

    /**
     * 各渠道配置数统计
     */
    @ApiModelProperty(value = "各渠道配置数统计")
    private String channelStatistics;

    /**
     * 各周期类型配置数统计
     */
    @ApiModelProperty(value = "各周期类型配置数统计")
    private String cycleTypeStatistics;

    /**
     * 平均失败次数
     */
    @ApiModelProperty(value = "平均失败次数")
    private Double averageFailureCount;

    /**
     * 配置健康度（正常配置占比）
     */
    @ApiModelProperty(value = "配置健康度")
    private Double configHealthRate;
}
