package com.leliven.finance.vo;

import com.leliven.finance.entity.ReconciliationTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 对账任务视图对象
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReconciliationTaskVO对象", description = "对账任务视图对象")
public class ReconciliationTaskVO extends ReconciliationTask {
    private static final long serialVersionUID = 1L;

    /**
     * 对账渠道名称
     */
    @ApiModelProperty(value = "对账渠道名称")
    private String channelName;

    /**
     * 任务状态名称
     */
    @ApiModelProperty(value = "任务状态名称")
    private String taskStatusName;

    /**
     * 对账周期类型名称
     */
    @ApiModelProperty(value = "对账周期类型名称")
    private String cycleTypeName;

    /**
     * 账单状态名称
     */
    @ApiModelProperty(value = "账单状态名称")
    private String billStatusName;

    /**
     * 对账结果名称
     */
    @ApiModelProperty(value = "对账结果名称")
    private String reconciliationResultName;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 任务执行耗时（秒）
     */
    @ApiModelProperty(value = "任务执行耗时（秒）")
    private Long executionDuration;

    /**
     * 是否可以重试
     */
    @ApiModelProperty(value = "是否可以重试")
    private Boolean canRetry;

    /**
     * 是否为最终状态
     */
    @ApiModelProperty(value = "是否为最终状态")
    private Boolean isFinalStatus;

    // 查询条件字段
    /**
     * 查询开始日期
     */
    @ApiModelProperty(value = "查询开始日期")
    private LocalDate startDate;

    /**
     * 查询结束日期
     */
    @ApiModelProperty(value = "查询结束日期")
    private LocalDate endDate;

    /**
     * 商户ID列表（用于批量查询）
     */
    @ApiModelProperty(value = "商户ID列表")
    private List<String> merchantIds;

    /**
     * 渠道列表（用于批量查询）
     */
    @ApiModelProperty(value = "渠道列表")
    private List<Integer> channels;

    /**
     * 任务状态列表（用于批量查询）
     */
    @ApiModelProperty(value = "任务状态列表")
    private List<Integer> taskStatuses;

    // 统计字段
    /**
     * 总任务数
     */
    @ApiModelProperty(value = "总任务数")
    private Integer totalTaskCount;

    /**
     * 成功任务数
     */
    @ApiModelProperty(value = "成功任务数")
    private Integer successTaskCount;

    /**
     * 失败任务数
     */
    @ApiModelProperty(value = "失败任务数")
    private Integer failedTaskCount;

    /**
     * 执行中任务数
     */
    @ApiModelProperty(value = "执行中任务数")
    private Integer executingTaskCount;

    /**
     * 平账任务数
     */
    @ApiModelProperty(value = "平账任务数")
    private Integer balancedTaskCount;

    /**
     * 不平任务数
     */
    @ApiModelProperty(value = "不平任务数")
    private Integer unbalancedTaskCount;

    /**
     * 成功率
     */
    @ApiModelProperty(value = "成功率")
    private Double successRate;

    /**
     * 平账率
     */
    @ApiModelProperty(value = "平账率")
    private Double balanceRate;
}
