package com.leliven.finance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 对账任务实体类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReconciliationTask对象", description = "对账任务表")
public class ReconciliationTask extends BaseBill {
    private static final long serialVersionUID = 1L;

    /**
     * 任务名称：日期+渠道+对账单，例如：20211002微信对账单
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 对账日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "对账日期")
    private LocalDate reconciliationDate;

    /**
     * 任务创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "任务创建时间")
    private LocalDateTime taskCreateTime;

    /**
     * 任务完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "任务完成时间")
    private LocalDateTime taskCompleteTime;

    /**
     * 任务执行状态
     * {@link com.leliven.finance.enums.ReconciliationTaskStatus}
     */
    @ApiModelProperty(value = "任务执行状态")
    private Integer taskStatus;

    /**
     * 账单名称
     */
    @ApiModelProperty(value = "账单名称")
    private String billName;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    /**
     * 对账周期类型
     * {@link com.leliven.finance.enums.ReconciliationCycleType}
     */
    @ApiModelProperty(value = "对账周期类型")
    private Integer cycleType;

    /**
     * 周期开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "周期开始日期")
    private LocalDate cycleStartDate;

    /**
     * 周期结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "周期结束日期")
    private LocalDate cycleEndDate;

    /**
     * 第三方支付渠道总金额
     */
    @ApiModelProperty(value = "第三方支付渠道总金额")
    private java.math.BigDecimal thirdPartyTotalAmount;

    /**
     * 第三方支付渠道总退款金额
     */
    @ApiModelProperty(value = "第三方支付渠道总退款金额")
    private java.math.BigDecimal thirdPartyRefundAmount;

    /**
     * 第三方支付渠道总交易单数
     */
    @ApiModelProperty(value = "第三方支付渠道总交易单数")
    private Integer thirdPartyTotalCount;

    /**
     * 第三方支付渠道总退款单数
     */
    @ApiModelProperty(value = "第三方支付渠道总退款单数")
    private Integer thirdPartyRefundCount;

    /**
     * 本平台总金额
     */
    @ApiModelProperty(value = "本平台总金额")
    private java.math.BigDecimal platformTotalAmount;

    /**
     * 本平台总退款金额
     */
    @ApiModelProperty(value = "本平台总退款金额")
    private java.math.BigDecimal platformRefundAmount;

    /**
     * 本平台总交易单数
     */
    @ApiModelProperty(value = "本平台总交易单数")
    private Integer platformTotalCount;

    /**
     * 本平台总退款单数
     */
    @ApiModelProperty(value = "本平台总退款单数")
    private Integer platformRefundCount;

    /**
     * 对账结果：0-不平，1-平账
     */
    @ApiModelProperty(value = "对账结果：0-不平，1-平账")
    private Integer reconciliationResult;

    /**
     * 关联的对账单ID
     */
    @ApiModelProperty(value = "关联的对账单ID")
    private Long billTotalId;
}
