package com.leliven.finance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

/**
 * 对账汇总表实体类
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BillTotal对象", description = "对账汇总表")
public class BillTotal extends BaseBill {
    private static final long serialVersionUID = 1L;
    /**
     * 对账时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "对账日期")
    private LocalDate date;
    /**
     * 商户id
     */
    @ApiModelProperty(value = "商户名称")
    private String merchantName;
    /**
     * 商户-订单数
     */
    @ApiModelProperty(value = "商户-订单数")
    private Integer mOrderNum;
    /**
     * 商户-实收金额
     */
    @ApiModelProperty(value = "商户-实收金额")
    private BigDecimal mReceiveAmount;
    /**
     * 商户-退款订单数
     */
    @ApiModelProperty(value = "商户-退款订单数")
    private Integer mRefundOrderNum;
    /**
     * 商户-退款金额
     */
    @ApiModelProperty(value = "商户-退款金额")
    private BigDecimal mRefundAmount;
    /**
     * 商户-手续费
     */
    @ApiModelProperty(value = "商户-手续费")
    private BigDecimal mServiceFee;
    /**
     * 商户-结算金额
     */
    @ApiModelProperty(value = "商户-结算金额")
    private BigDecimal mSettlementAmount;
    /**
     * 停车-订单数
     */
    @ApiModelProperty(value = "停车-订单数")
    private Integer bOrderNum;
    /**
     * 停车-实收金额
     */
    @ApiModelProperty(value = "停车-实收金额")
    private BigDecimal bReceiveAmount;
    /**
     * 停车-退款订单数
     */
    @ApiModelProperty(value = "停车-退款订单数")
    private Integer bRefundOrderNum;
    /**
     * 停车-退款金额
     */
    @ApiModelProperty(value = "停车-退款金额")
    private BigDecimal bRefundAmount;

    /**
     * 关联的对账任务ID
     */
    @ApiModelProperty(value = "关联的对账任务ID")
    private Long reconciliationTaskId;

    public BillTotal() {
        super();
        this.mReceiveAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.mRefundAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.mServiceFee = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.mSettlementAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.bReceiveAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        this.bRefundAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    }
}
