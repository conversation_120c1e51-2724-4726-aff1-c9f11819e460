package com.leliven.finance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 对账周期类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Getter
@AllArgsConstructor
public enum ReconciliationCycleType {
    /**
     * 日
     */
    DAILY(1, "日", ChronoUnit.DAYS),
    
    /**
     * 周
     */
    WEEKLY(2, "周", ChronoUnit.WEEKS),
    
    /**
     * 月
     */
    MONTHLY(3, "月", ChronoUnit.MONTHS),
    
    /**
     * 季
     */
    QUARTERLY(4, "季", ChronoUnit.MONTHS),
    
    /**
     * 年
     */
    YEARLY(5, "年", ChronoUnit.YEARS),
    
    /**
     * 自定义时间
     */
    CUSTOM(6, "自定义时间", ChronoUnit.DAYS);

    private final int value;
    private final String desc;
    private final ChronoUnit chronoUnit;

    /**
     * 根据值获取描述
     *
     * @param value 周期类型值
     * @return 周期类型描述
     */
    public static String getDescByValue(Integer value) {
        for (ReconciliationCycleType type : values()) {
            if (value.equals(type.getValue())) {
                return type.desc;
            }
        }
        return "";
    }

    /**
     * 根据值获取枚举
     *
     * @param value 周期类型值
     * @return 枚举对象
     */
    public static ReconciliationCycleType getByValue(Integer value) {
        for (ReconciliationCycleType type : values()) {
            if (value.equals(type.getValue())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 计算下次执行时间
     *
     * @param currentDate 当前日期
     * @param cycleValue 周期值（用于自定义周期）
     * @return 下次执行日期
     */
    public LocalDate calculateNextExecuteDate(LocalDate currentDate, Integer cycleValue) {
        if (cycleValue == null || cycleValue <= 0) {
            cycleValue = 1;
        }
        
        switch (this) {
            case DAILY:
                return currentDate.plus(cycleValue, ChronoUnit.DAYS);
            case WEEKLY:
                return currentDate.plus(cycleValue, ChronoUnit.WEEKS);
            case MONTHLY:
                return currentDate.plus(cycleValue, ChronoUnit.MONTHS);
            case QUARTERLY:
                return currentDate.plus(cycleValue * 3L, ChronoUnit.MONTHS);
            case YEARLY:
                return currentDate.plus(cycleValue, ChronoUnit.YEARS);
            case CUSTOM:
                return currentDate.plus(cycleValue, ChronoUnit.DAYS);
            default:
                return currentDate.plus(1, ChronoUnit.DAYS);
        }
    }

    /**
     * 获取默认周期值
     *
     * @return 默认周期值
     */
    public Integer getDefaultCycleValue() {
        switch (this) {
            case DAILY:
                return 1;
            case WEEKLY:
                return 1;
            case MONTHLY:
                return 1;
            case QUARTERLY:
                return 1; // 实际是3个月，但在计算时会乘以3
            case YEARLY:
                return 1;
            case CUSTOM:
                return 1;
            default:
                return 1;
        }
    }

    /**
     * 获取周期描述（包含周期值）
     *
     * @param cycleValue 周期值
     * @return 完整的周期描述
     */
    public String getFullDesc(Integer cycleValue) {
        if (cycleValue == null || cycleValue <= 0) {
            cycleValue = getDefaultCycleValue();
        }
        
        if (cycleValue == 1 && this != CUSTOM) {
            return "每" + this.desc;
        } else {
            return "每" + cycleValue + this.desc;
        }
    }
}
