package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务分类枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessCategoryEnum {

    /**
     * 追缴业务类
     */
    COLLECTION_BUSINESS("COLLECTION_BUSINESS", "追缴业务类"),

    /**
     * 进出场业务类
     */
    ENTRY_BUSINESS("ENTRY_BUSINESS", "进出场业务类"),

    /**
     * 优惠业务类
     */
    DISCOUNT_BUSINESS("DISCOUNT_BUSINESS", "优惠业务类"),

    /**
     * 设备业务类
     */
    DEVICE_BUSINESS("DEVICE_BUSINESS", "设备业务类"),

    /**
     * 发票业务类
     */
    INVOICE_BUSINESS("INVOICE_BUSINESS", "发票业务类"),

    /**
     * 支付业务类
     */
    PAYMENT_BUSINESS("PAYMENT_BUSINESS", "支付业务类"),

    /**
     * 停车业务类
     */
    PARKING_BUSINESS("PARKING_BUSINESS", "停车业务类"),

    /**
     * 通知业务类
     */
    NOTIFY_BUSINESS("NOTIFY_BUSINESS", "通知业务类");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static BusinessCategoryEnum getByCode(String code) {
        for (BusinessCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

} 