package org.springblade.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 变更类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ChangeTypeEnum {

    /**
     * 新增
     */
    CREATE("CREATE", "新增"),

    /**
     * 修改
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ChangeTypeEnum getByCode(String code) {
        for (ChangeTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

} 